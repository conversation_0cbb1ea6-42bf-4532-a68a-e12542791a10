<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新手疑惑解答</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        header {
            background: #007bff;
            color: #fff;
            padding: 1rem 0;
            text-align: center;
        }

        .container {
            width: 80%;
            margin: auto;
            overflow: hidden;
        }

        .search-bar {
            margin: 1rem 0;
            display: flex;
            justify-content: center;
        }

        .search-bar input {
            width: 80%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .search-bar button {
            padding: 0.5rem 1rem;
            border: none;
            background: #007bff;
            color: #fff;
            border-radius: 5px;
            cursor: pointer;
        }

        .search-bar button:hover {
            background: #0056b3;
        }

        .faq-section {
            margin: 2rem 0;
        }

        .faq-section h2 {
            background: #007bff;
            color: #fff;
            padding: 1rem;
            border-radius: 5px;
        }

        .faq-item {
            background: #fff;
            margin: 0.5rem 0;
            padding: 1rem;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .faq-item h3 {
            margin: 0;
            color: #007bff;
        }

        .faq-item p {
            margin: 0.5rem 0 0;
        }

        .contact-info {
            background: #fff;
            padding: 1rem;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
        }

        .contact-info h3 {
            margin-top: 0;
            color: #007bff;
        }

        .contact-info p {
            margin: 0.5rem 0;
        }

        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin: 0.5rem 0;
            text-decoration: none;
            color: #fff;
            background: #007bff;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #0056b3;
        }

        footer {
            background: #007bff;
            color: #fff;
            text-align: center;
            padding: 1rem;
            position: absolute;
            bottom: 0;
            width: 100%;
        }
    </style>
</head>
<body>
    <header>
        <h1>新手疑惑解答</h1>
    </header>

    <div class="container">
        <section class="faq-section">
            <h2>常见问题</h2>
            
            <div class="faq-item">
                <h3>手游(安卓/Android)如何选择自己的机型适配款？</h3>
                <p>首先找到分类【-—🍁安卓手游专区🍁-—】下边都是安卓手游辅助。</p>
                <p><b>无Root教程：</b></p>
                <p>如果你手机没有Root就去(直装专区)里边看，你只能买直装类型的辅助，带Root/内核这类字样的你都用不了。</p>
                <p><b>Root教程：</b></p>
                <p>如果你有Root安卓专区的所有辅助都可以用(包括直装)，下载之后会给Root权限就可以了。</p>
                <p><b>防封说明：</b></p>
                <p>防封一般不要求Root权限，可以根据自己的食用需求自行搭配。</p>
            </div>
            
            <div class="faq-item">
                <h3>手游(苹果/IOS)如何选择自己的机型适配款？</h3>
                <p>首先找到分类【-—🍁苹果专手游区🍁-—】下边都是苹果手游的辅助。</p>
                <p><b>无越狱教程：</b></p>
                <p>如果你手机没有越狱就去(免越狱专区)里边看，你只能买免越狱类型的辅助，带越狱这类字样的你都用不了。</p>
                <p><b>已越狱教程：</b></p>
                <p>如果你已越狱苹果专区的所有辅助都可以用(包括免越狱)，有这类权限你都不用看教程了。</p>
                <p><b>防封说明：</b></p>
                <p>防封一般不要求越狱权限(前提会安装就行)，可以根据自己的食用需求自行搭配。</p>
            </div>
            
            <div class="faq-item">
                <h3>购买了卡密如何下载软件？</h3>
                <p>每个商品的简介区都会有一下网盘链接。</p>
                <p>购买前务必检查链接是否正常打开，减少不必要的麻烦。</p>
                <br>
                <h3>购买的小号怎么登录？</h3>
                <p>小号都会配带一个上号器，输入购买的卡密自动上号。</p>
                <p>如果你的辅助是框架版的话你需要把上号器也添加进框架内再一键上号即可！</p>
                <br>
                <h3>不知道哪个稳定？不会选择？</h3>
                <p>网站首页都有一个页端转图，每天更新最新效果图。</p>
                <p>转图页先下拉侧栏会出现一个搜索，根据(和平/王者)这类关键词搜就能看到你想要的内容。</p>
                <h5>注：需要其他的教程请反馈给AK~</h5>
            </div>
            
        </section>
    </div>
</body>
</html>