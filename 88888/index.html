<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=Edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="keywords" content="平台中心主页">
  <meta name="description" content="平台中心主页，简约而不简单。">
  <link rel="shortcut icon" href="https://q1.qlogo.cn/g?b=qq&nk=836721503&s=640" />
  <link rel="stylesheet" href="asset/css/style.css">
  <link rel="stylesheet" href="asset/css/bc.css">
  <title>平台自助中心</title>
</head>
  <body>
  <!-- 加载动画 -->
  <script>
   document.body.innerHTML += ('<div id="loader-wrapper"><div id="loader"></div><div class="loader-section section-left"></div><div class="loader-section section-right"></div><div id="load_title">正在加载中，请稍后</div></div>');
   window.onload = function () { document.body.className += ' loaded';}
</script>
  <div class="bc_content">
  <div class="bc-fluid">
  <div class="bc-row bc-space10">
  <div class="bc-xs12 bc-sm6 bc-md6 bc-lg6">
  <div class="bc_mbl bc_box" id="bc_mobile_head">
  <div class="bc-row">
  <div class="bc-xs3">
  <img class="bc_mbl bc_box img-avatar" style="border-radius: 50%;" src="https://q1.qlogo.cn/g?b=qq&nk=836721503&s=640">
</div>
  <div class="bc-xs9">
  <div class="bc-xs12">
  <div id="bc_name">平台自助中心</div>
  <div id="bc_tip">Tips：打造全网第一售后！！</div>
</div>
  <!-- 日期时间 -->
  <span id=localtime></span>
  <script type="text/javascript">
   function showLocale(objD) {
   var str,colorhead,colorfoot;
   var yy = objD.getYear();
   if(yy<1900) yy = yy+1900;
   var MM = objD.getMonth()+1;
   if(MM<10) MM = '0' + MM;
   var dd = objD.getDate();
   if(dd<10) dd = '0' + dd;
   var hh = objD.getHours();
   if(hh<10) hh = '0' + hh;
   var mm = objD.getMinutes();
   if(mm<10) mm = '0' + mm;
   var ss = objD.getSeconds();
   if(ss<10) ss = '0' + ss;
   var ww = objD.getDay();
   if ( ww==0 ) colorhead="<font color=\"#ffffff\">";
   if ( ww > 0 && ww < 6 ) colorhead="<font color=\"#ffffff\">";
   if ( ww==6 ) colorhead="<font color=\"#ffffff\">";
   if (ww==0) ww="星期天";
   if (ww==1) ww="星期一";
   if (ww==2) ww="星期二";
   if (ww==3) ww="星期三";
   if (ww==4) ww="星期四";
   if (ww==5) ww="星期五";
   if (ww==6) ww="星期六";
   colorfoot="</font>"
   str = colorhead + yy + "年" + MM + "月" + dd + " " + hh + "点" + mm + "分" + ss + " " + ww + colorfoot;
   return(str);}
   function tick() {
   var today;
   today = new Date();
   document.getElementById("localtime").innerHTML = showLocale(today);
   window.setTimeout("tick()", 1000);}
   tick();
</script>
  <div class="bc-xs12">
  <a class="btn">标签：</a>
  <a class="btn btn-green">售后</a>
  <a class="btn btn-yellow">效率</a>
  <a class="btn btn-blue">ฅ՞•ﻌ•՞ฅ</a>
</div>
 <div class="bc-xs12">
<a class="btn">擅长：</a>
<a class="btn btn-green">帮助客户解决问题</a>
<a class="btn btn-yellow">全天候在线解决</a>
</div>
</div>
</div>
</div>
</div>
   <div class="bc-xs12 bc-sm5 bc-md5 bc-lg5">
          <div class="bc_box bc_mbl">
            <div class="bc-row">
              <h3 class="bc-xs12 bc_box"><svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-zhandian"></use>
                </svg>(无广告)人工客服</h3>
              <hr>
            </div>
            <div class="bc-row bc-space10" style="word-wrap:break-word;">
              <a href="https://work.weixin.qq.com/kfid/kfc0f9777a80a9c763f" class="bc-xs6 bc-sm6 bc-md6 bc-lg6">
                <div class="bc_a btn-yellow bc_center"> <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-weixin"></use>
                  </svg>点我进入人工客服</div>
              </a>
              <a href="jc/qq.php" class="bc-xs6 bc-sm6 bc-md6 bc-lg6">
                <div class="bc_a btn-blue bc_center"> <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-QQ"></use>
                  </svg>qq客服【未开放】</div>
              </a>
            </div>
          </div>
        </div>
      </div>
 
</div>
</div>
  <div class="bc-sm6 bc-md4 bc-lg6 bc-hide-xs">
  <div class="bc_box bc_mbl">
  <div class="bc_box bc_mbl">
  <p>如果您喜欢我们的网站，请将本站添加到收藏夹（快捷键<code>Ctrl+D</code>），并<a class="btn btn-green" href="https://jingyan.baidu.com/article/4dc40848868eba89d946f1c0.html" target="_blank">设为浏览器主页</a>，方便您的下次访问，感谢支持。</p>
</div>
</div>
</div>
</div>
  <div class="bc-row bc-space10">
  <div class="bc-xs12 bc-sm7 bc-md7 bc-lg7">
  <div class="bc_box bc_mbl">
  <div class="bc-row">
  <h3 class="bc-xs12 bc_box"><svg class="icon" aria-hidden="true">
  <use xlink:href="#icon-zhandian"></use>
</svg>【自助】问题解决综合</h3><hr>
</div>
  <div class="bc-row bc-space10" style="word-wrap:break-word;">
              <a href="jc/1.html" class="bc-xs6 bc-sm4 bc-md4 bc-lg4">
                <div class="bc_a btn-orange bc_center"> <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-gitee"></use>
                  </svg>新手问题解决</div>
              </a>
  <a href="jc/2.html" class="bc-xs6 bc-sm4 bc-md4 bc-lg4">
                <div class="bc_a btn-green bc_center"> <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-gitee"></use>
                  </svg>平台售后规则</div>
              </a>
  </a>
              <a href="jc/3.html" class="bc-xs6 bc-sm4 bc-md4 bc-lg4">
                <div class="bc_a btn-yellow bc_center"> <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-gitee"></use>
                  </svg>安卓问题解决</div>
              </a>
              <a href="jc/4.html" class="bc-xs6 bc-sm4 bc-md4 bc-lg4">
                <div class="bc_a btn-purple bc_center"> <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-gitee"></use>
                  </svg>苹果问题解决</div>
              </a>
              <a href="jc/5.html" class="bc-xs6 bc-sm4 bc-md4 bc-lg4">
                <div class="bc_a btn-yellow bc_center"> <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-gitee"></use>
                  </svg>国际服区解决</div>
              </a>
               <a href="jc/10.html" class="bc-xs6 bc-sm4 bc-md4 bc-lg4">
                <div class="bc_a btn-yellow bc_center"> <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-gitee"></use>
                  </svg>端游问题解决</div>
              </a>
</div>
</div>
</div>
  <div class="bc-xs12 bc-sm5 bc-md5 bc-lg5">
  <div class="bc_box bc_mbl">
  <div class="bc-row">
  <h3 class="bc-xs12 bc_box"><svg class="icon" aria-hidden="true">
  <use xlink:href="#icon-zhandian"></use>
</svg>【工具】分享大全</h3><hr>
</div>
 <div class="bc-row bc-space10" style="word-wrap:break-word;">
              <a href="jc/6.html" class="bc-xs6 bc-sm6 bc-md6 bc-lg6">
                <div class="bc_a btn-yellow bc_center"> <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-lianjie"></use>
                  </svg>残留三清工具</div>
              </a>
  <a href="#" class="bc-xs6 bc-sm6 bc-md6 bc-lg6">
  <div class="bc_a btn-blue bc_center"><svg class="icon" aria-hidden="true">
  <use xlink:href="#icon-lianjie"></use>
</svg>待添加</div></a>
</div>
</div>
</div>
</div>
  <div class="bc-row bc-space10">
  <div class="bc-xs12">
  <div class="bc_mbl bc_box" id="bc_foot">Copyright © 2025 神秘人 <span class="btn bc-hide-xs">收藏本站（快捷键<code>Ctrl+D</code>）</span></div>
</div>
</div>
</div>
</div>
  <script src="asset/js/iconfont.js"></script>
</body>
</html>