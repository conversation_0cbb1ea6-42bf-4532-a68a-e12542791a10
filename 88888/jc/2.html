<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平台下单规则</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        header {
            background: #007bff;
            color: #fff;
            padding: 1rem 0;
            text-align: center;
        }

        .container {
            width: 80%;
            margin: auto;
            overflow: hidden;
        }

        .search-bar {
            margin: 1rem 0;
            display: flex;
            justify-content: center;
        }

        .search-bar input {
            width: 80%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .search-bar button {
            padding: 0.5rem 1rem;
            border: none;
            background: #007bff;
            color: #fff;
            border-radius: 5px;
            cursor: pointer;
        }

        .search-bar button:hover {
            background: #0056b3;
        }

        .faq-section {
            margin: 2rem 0;
        }

        .faq-section h2 {
            background: #007bff;
            color: #fff;
            padding: 1rem;
            border-radius: 5px;
        }

        .faq-item {
            background: #fff;
            margin: 0.5rem 0;
            padding: 1rem;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .faq-item h3 {
            margin: 0;
            color: #007bff;
        }

        .faq-item p {
            margin: 0.5rem 0 0;
        }

        .contact-info {
            background: #fff;
            padding: 1rem;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
        }

        .contact-info h3 {
            margin-top: 0;
            color: #007bff;
        }

        .contact-info p {
            margin: 0.5rem 0;
        }

        .btn {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin: 0.5rem 0;
            text-decoration: none;
            color: #fff;
            background: #007bff;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #0056b3;
        }

        footer {
            background: #007bff;
            color: #fff;
            text-align: center;
            padding: 1rem;
            position: absolute;
            bottom: 0;
            width: 100%;
        }
    </style>
</head>
<body>
    <header>
        <h1>平台下单售后</h1>
    </header>

    <div class="container">
        <section class="faq-section">
            <h2>常见问题</h2>
            
            <div class="faq-item">
               <h3>卡密出现以上问题第一时间联系客服给予处理，不拖拉，不敷衍，将问题处理到底！</h3>
                <p>下单必看注意事项/免责说明/不看完盲目下单造成的任何损失与本站无关</p>
                <p>本站退单须知：（卡密错误无法解决的情况下）（网盘不存在无法解决的情况下）（没效果无法解决的情况下）可退单至平台站内，非微信支付宝，退单与支付金额不符，支付产生的手续费自行承担！如不能接受请勿下单！</p>
                <p><b>注意：</b></p>
                <p>1：本站所有的商品不包封号问题！不包封号退款问题！至于稳不稳.稳多久.看商品的防封和自己的演技和设备是否干净！</p>
                <p>2：如果您无法承担封号的风险，请勿下单，如下单则您愿承担一切封号风险！</p>
                <p>3：首先必须要明白的一点：本站仅是软件商品寄售平台站点，并非软件开发商，本站优先保障（激活码/卡密/CDK）正确无误</p>
                <p></p>
                <p>4：关于软件的使用问题.平台提供教学服务(99%的同行都不会提供使用问题教学)但本平台软件总数5000+款商品使用方法繁琐复杂，不能保证每一个都能教会！其次最重要的就是下单前自己需要做的两个事情(有问题直接换别的商品即可)选择商品后立即购买键上方有软件的下载链接，检查下载链接是否可以正常打开并下载安装(本站软件总数5000+款不能时刻保证每个下载链接都不过期/不过时效的)</p>
                <p>5：下载完软件检查是否支持自己的手机并打开软件是否有黑屏/白屏/闪退等异常情况如确定没问题再下单(有部分情况是可以解决的咨询客服或查看教程即可)如网盘失效/过期/可发送给客服更换！感谢你的帮助！</p>
                <p>6：本站倡导下单用户先下载后下单避免少数机型不兼容的情况！盲目者自行承担损失！</p>
                <p>7：对已出售的（卡密）是否能退单？退款不要了？我要换？因卡密在本站为是虚拟未激活状态如退换无法保证不被激活使用或您二次出售！</p>
                <p>8：特此非卡密问题不支持退换/不扯皮！如您不接受以上上述条例请勿下单！下单默认为同意以上条例！！！扯皮直接拉黑</p>
                <p>9：如卡密问题无法处理的情况下，没效果无法解决的情况下，支持更换或退单！</p>
                <p>10：关于卡密问题售后我们支持以下方式：（卡密过期/卡密错误/卡密封禁/卡密失效/卡密不存在/网盘失效类似这种问题）</p>
                
            </div>
            
            
        </section>
    </div>
</body>
</html>