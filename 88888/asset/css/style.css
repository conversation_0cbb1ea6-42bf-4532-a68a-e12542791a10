body{background: url("../img/tup.png");background-size: cover;height: 100vh;background-repeat: no-repeat;background-attachment: fixed!important;}.bc_right{text-align: right;}.bc_content{margin-top: 30px;}#bc_mobile_head{margin-top: 0px;}.icon{width: 1em;height: 1em;vertical-align: -0.15em;fill: currentColor;overflow: hidden;padding-right: 2px;}.img-avatar{display: inline-block !important;width: 64px;height: 64px;}.img-avatar-thumb{margin: 5px;-webkit-box-shadow: 0 0 0 4px rgba(255,255,255,0.4);box-shadow: 0 0 0 5px rgba(255,255,255,0.4);}.bc_a{width: auto;word-wrap: break-word;font-size: 12pt;padding: 5px 15px;text-align: center;text-transform: uppercase;transition: 0.5s;background-size: 200% auto;color: white;box-shadow: 0 0 2px #eee;border-radius: 25px;}.bc_center{text-align: center;}.bc_box{padding: 10px;border-radius: 10px;}.bc_mbl{backdrop-filter: blur(20px);box-shadow: 0 1px 30px 1px rgba(0,0,0,.3);border-bottom: 1px solid rgba(255,255,255,0.4);border-left: 1px solid rgba(255,255,255,0.4);color: #FFFFFF;}#bc_name{font-size: 18pt;margin: 5px 5px 0 0;}#bc_tip{padding: 5px 0;}.btn{width: auto;word-wrap: break-word;margin: 2px;padding: 0px 5px;font-size: 8pt;text-align: center;text-transform: uppercase;transition: 0.5s;background-size: 200% auto;color: white;box-shadow: 0 0 2px #eee;border-radius: 25px;}.btn:hover{background-position: right center;color: #FFFFFF;}.btn-orange{background-image: linear-gradient(to right,#f6d365 0%,#fda085 51%,#f6d365 100%);}.btn-purple{background-image: linear-gradient(to right,#fbc2eb 0%,#a6c1ee 51%,#fbc2eb 100%);}.btn-green{background-image: linear-gradient(to right,#84fab0 0%,#8fd3f4 51%,#84fab0 100%);}.btn-blue{background-image: linear-gradient(to right,#a1c4fd 0%,#c2e9fb 51%,#a1c4fd 100%);}.btn-yellow{background-image: linear-gradient(to right,#ffecd2 0%,#fcb69f 51%,#ffecd2 100%);}#bc_foot{text-align: center;}@media screen and (min-width:768px){body{background: url("../img/tup.png");background-size: cover;height: 100vh;}.bc_content{margin-top: 15px;}#bc_foot{padding: 5px;}}.chromeframe{margin: .2em 0; background: #ccc; color: #000; padding: .2em 0} #loader-wrapper{position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 999999} #loader{display: block; position: relative; left: 50%; top: 50%; width: 150px; height: 150px; margin: -75px 0 0 -75px; border-radius: 50%; border: 3px solid transparent; border-top-color: #FFF; -webkit-animation: spin 2s linear infinite; -ms-animation: spin 2s linear infinite; -moz-animation: spin 2s linear infinite; -o-animation: spin 2s linear infinite; animation: spin 2s linear infinite; z-index: 1001} #loader:before{content: ""; position: absolute; top: 5px; left: 5px; right: 5px; bottom: 5px; border-radius: 50%; border: 3px solid transparent; border-top-color: #FFF; -webkit-animation: spin 3s linear infinite; -moz-animation: spin 3s linear infinite; -o-animation: spin 3s linear infinite; -ms-animation: spin 3s linear infinite; animation: spin 3s linear infinite} #loader:after{content: ""; position: absolute; top: 15px; left: 15px; right: 15px; bottom: 15px; border-radius: 50%; border: 3px solid transparent; border-top-color: #FFF; -moz-animation: spin 1.5s linear infinite; -o-animation: spin 1.5s linear infinite; -ms-animation: spin 1.5s linear infinite; -webkit-animation: spin 1.5s linear infinite; animation: spin 1.5s linear infinite} @-webkit-keyframes spin{0%{-webkit-transform: rotate(0deg); -ms-transform: rotate(0deg); transform: rotate(0deg)} 100%{-webkit-transform: rotate(360deg); -ms-transform: rotate(360deg); transform: rotate(360deg)}} @keyframes spin{0%{-webkit-transform: rotate(0deg); -ms-transform: rotate(0deg); transform: rotate(0deg)} 100%{-webkit-transform: rotate(360deg); -ms-transform: rotate(360deg); transform: rotate(360deg)}} #loader-wrapper .loader-section{position: fixed; top: 0; width: 51%; height: 100%; backdrop-filter:blur(20px); z-index: 1000; -webkit-transform: translateX(0); -ms-transform: translateX(0); transform: translateX(0)} #loader-wrapper .loader-section.section-left{left: 0} #loader-wrapper .loader-section.section-right{right: 0} .loaded #loader-wrapper .loader-section.section-left{-webkit-transform: translateX(-100%); -ms-transform: translateX(-100%); transform: translateX(-100%); -webkit-transition: all .7s .3s cubic-bezier(0.645,0.045,0.355,1.000); transition: all .7s .3s cubic-bezier(0.645,0.045,0.355,1.000)} .loaded #loader-wrapper .loader-section.section-right{-webkit-transform: translateX(100%); -ms-transform: translateX(100%); transform: translateX(100%); -webkit-transition: all .7s .3s cubic-bezier(0.645,0.045,0.355,1.000); transition: all .7s .3s cubic-bezier(0.645,0.045,0.355,1.000)} .loaded #loader{opacity: 0; -webkit-transition: all .3s ease-out; transition: all .3s ease-out}.loaded #loader-wrapper{visibility: hidden;} .no-js #loader-wrapper{display: none} .no-js h1{color: #222} #loader-wrapper #load_title{font-family: 'Open Sans'; color: #FFF; font-size: 19px; width: 100%; text-align: center; z-index: 9999999999999; position: absolute; top: 60%; opacity: 1; line-height: 30px} #loader-wrapper #load_title span{font-weight: normal; font-style: italic; font-size: 13px; color: #FFF; opacity: .5}