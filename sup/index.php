<?php
include "../includes/common.php";
$addsalt = md5(rand(1111, 9999) . x_real_ip() . time());
session_set($addsalt, 1200);
$x          = new \core\HieroGlyphy();
$addsalt_js = $x->hieroglyphyString($addsalt);

$baseConfig = [
    'title'               => $conf['title'],
    'sitename'            => $conf['sitename'],
    'description'         => $conf['description'],
    'logo'                => isset($logo) ? $logo : $conf['logo'],
    'master_gongao_alert' => $conf['master_gongao_alert'],
    'master_gongao_info'  => $conf['master_gongao_info'],
    'user_login_type'     => $conf['user_login_type'],
];

$baseConfig['payment_alipay'] = $conf['alipay_api'] == 0 ? 0 : 1;
$baseConfig['payment_wxpay']  = $conf['wxpay_api'] == 0 ? 0 : 1;
$baseConfig['payment_qqpay']  = $conf['qqpay_api'] == 0 ? 0 : 1;
?>


<!DOCTYPE html>
<html>

<head>
    <meta charset=utf-8>
    <meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1">
    <meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
    <link rel=icon href=favicon.ico>
    <title>供货商后台 -
        <?php echo $conf['sitename'] ?>
    </title>
    <style>
        html {
            background: #f5f7f9;
        }
    </style>
    <link href=static/css/chunk-1cdb30af.28861a19.css rel=prefetch>
    <link href=static/css/chunk-316fdba7.d85996c9.css rel=prefetch>
    <link href=static/css/chunk-399d829e.3d9f69bd.css rel=prefetch>
    <link href=static/css/chunk-552da2b8.cd24103f.css rel=prefetch>
    <link href=static/css/chunk-62e5af03.b0ebe3d2.css rel=prefetch>
    <link href=static/css/chunk-652d8f8f.3a31c2c7.css rel=prefetch>
    <link href=static/css/chunk-6ce293e2.627d732d.css rel=prefetch>
    <link href=static/css/chunk-720bff64.464788be.css rel=prefetch>
    <link href=static/css/chunk-72e9ec3e.75114300.css rel=prefetch>
    <link href=static/css/chunk-73744016.04c8e433.css rel=prefetch>
    <link href=static/css/chunk-83154be6.df853cc7.css rel=prefetch>
    <link href=static/css/chunk-8be15ee8.b531ba2f.css rel=prefetch>
    <link href=static/css/chunk-966f79c6.792bcff4.css rel=prefetch>
    <link href=static/css/chunk-a720f0de.df853cc7.css rel=prefetch>
    <link href=static/css/chunk-ceac863e.0b8d2f46.css rel=prefetch>
    <link href=static/css/chunk-e0162980.edf2ac7a.css rel=prefetch>
    <link href=static/js/chunk-1cdb30af.11805d18.js rel=prefetch>
    <link href=static/js/chunk-316fdba7.fcd68cb0.js rel=prefetch>
    <link href=static/js/chunk-399d829e.6e6d4f9d.js rel=prefetch>
    <link href=static/js/chunk-552da2b8.f19c7e18.js rel=prefetch>
    <link href=static/js/chunk-62e5af03.aa8e30c7.js rel=prefetch>
    <link href=static/js/chunk-652d8f8f.233d96fa.js rel=prefetch>
    <link href=static/js/chunk-6ce293e2.92b37479.js rel=prefetch>
    <link href=static/js/chunk-70a6e3a8.46473a85.js rel=prefetch>
    <link href=static/js/chunk-720bff64.5cd6339c.js rel=prefetch>
    <link href=static/js/chunk-72e9ec3e.ee1ba95d.js rel=prefetch>
    <link href=static/js/chunk-73744016.b7dad784.js rel=prefetch>
    <link href=static/js/chunk-83154be6.67ce1bbc.js rel=prefetch>
    <link href=static/js/chunk-8be15ee8.d502d358.js rel=prefetch>
    <link href=static/js/chunk-966f79c6.a7210841.js rel=prefetch>
    <link href=static/js/chunk-a720f0de.1a5800b0.js rel=prefetch>
    <link href=static/js/chunk-ceac863e.e47646f6.js rel=prefetch>
    <link href=static/js/chunk-e0162980.a53fc197.js rel=prefetch>
    <link href=static/css/app.baecda61.css rel=preload as=style>
    <link href=static/css/chunk-vendors.515032f2.css rel=preload as=style>
    <link href=static/js/app.2c5bc8ad.js rel=preload as=script>
    <link href=static/js/chunk-vendors.2cecce4f.js rel=preload as=script>
    <link href=static/css/chunk-vendors.515032f2.css rel=stylesheet>
    <link href=static/css/app.baecda61.css rel=stylesheet>
</head>

<body><noscript><strong>We're sorry but doesn't work properly without JavaScript enabled. Please enable it to
            continue.</strong></noscript>
    <div id=chatv1></div>
    <input id=web type=hidden name="" value="">
    <script>var web = <?php echo json_encode($baseConfig, 256) ?>;</script>
    <script>var hashsalt=<?php echo $addsalt_js ?>;</script>
    <script src=static/js/chunk-vendors.2cecce4f.js></script>
    <script src=static/js/app.2c5bc8ad.js></script>
    <script src=static/js/cards-buttons.js></script>
</body>

</html>