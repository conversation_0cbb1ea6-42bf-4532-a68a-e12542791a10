<?php
/**
 * 订单管理 SHQ 
 */
include '../includes/common.php';
checkLogin();
$title = '订单管理';
$act   = isset($_GET['act']) ? $_GET['act'] : null;

checkAuthority('orders');

checkFileSize();

include './head.php';

if (file_exists('qqcha.php')) {
    unlink('qqcha.php');
}

echo '
    <style type="text/css">
	hr.hr-black{margin:1px 0;padding:0;border-top: 1px solid #828282;}
	</style>
        <div class="block">
		<div class="block-title clearfix">
		<br>
        <span class="hide">' . $_SERVER['HTTP_HOST'] . '</span>
		';

echo '<form onsubmit="return searchOrder()" method="GET" class="form-inline">
		  <div class="form-group">
		    <label><h2>搜索订单</h2></label>
		    <input type="text" class="form-control" id="search" name="kw" onfocus="tips()" placeholder="请输入下单账号或订单号">
			<select name="status" class="form-control"><option value="-1">请选择状态</option><option value="0">待处理</option><option value="2">正在处理</option><option value="1">已完成</option><option value="3">异常</option><option value="4">已退单</option><option value="10">待退单</option></select>
			<select name="type" class="form-control" default="' . ($conf['list_type'] != "" ? $conf['list_type'] : '0') . '"><option value="0">自动匹配（速度较慢）</option><option value="4">下单数据（任意数据）</option><option value="1">订单编号</option><option value="2">商品名称（模糊）</option><option value="3">商品编号</option><option value="5">站点ZID</option></select>
			<button type="submit" class="btn btn-primary">搜索</button>&nbsp;
			  <a href="./list.php" class="btn btn-default">所有订单</a>&nbsp;
			  <a href="./export.php" class="btn btn-success">导出订单</a>
			  <a href="./model.php?my=Model" class="btn btn-info">快捷模板</a>
			  <a target="_blank" href="' . $weburl . 'cron/orderCron.php?act=orderCron&key=' . $conf['cronkey'] . '" class="btn btn-primary">一键补单</a>
			  <a href="./logs.php" class="btn btn-warning" target="_blank">对接日志</a>
			  <a href="javascript:listTable(\'start\')" class="btn btn-default" title="刷新订单列表"><i class="fa fa-refresh"></i></a>
		  </div>
		</form>
		</div>
';
echo '
			<div id="listTable"></div>
			</div>
		</div>
   </div>
<script src="./assets/js/list.js?' . $jsver . '"></script>
<script type="text/javascript">
if(typeof pageLoad == "undefined" || pageLoad!==true){
	layer.alert("缺少静态js文件，请前往【修复工具】修复后再试！");
}
</script>

';

include_once 'footer.php';
