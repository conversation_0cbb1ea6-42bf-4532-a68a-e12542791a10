<?php
if ($conf['ui_user'] == 1) {
    $ui_user = array('bg-dark', 'bg-white-only', 'bg-dark');
} else {
    $ui_user = array('bg-primary', 'bg-primary', 'bg-light dker');
}

if ($is_account === true) {
    $head_user = $account['user'];
    $head_qq   = $account['qq'];
} else {
    $head_user = $conf['adm_user'];
    $head_qq   = $conf['zzqq'] ? $conf['zzqq'] : $conf['qq'];
}

@header('Content-Type: text/html; charset=UTF-8');
//\core\BsEncode::start();

//检测到后台文件夹自动更新文件
if (is_dir(dirname(__DIR__) . '/admuser') && $conf['updatemovetime'] - time() <= 0 && !preg_match('/admuser\/|updatemove/', $_SERVER['REQUEST_URI'])) {
    exit('<script language=\'javascript\'>window.location.href=\'./updatemove.php\';</script>');
}

$Menu = new \core\Menu();

include './page.head.php';

if ($conf && isset($conf['sitename'])) {
    if (!preg_match('/^[\w\-]{40}$/', $conf['adm_pwd']) && $conf['pwd_no_en'] != 1 && $conf['pwd_en'] != 1) {
        if (isset($_GET['pwd_no_en'])) {
            saveSetting('pwd_no_en', 1);
        } elseif (isset($_GET['pwd_en'])) {
            saveSetting('adm_pwd', sha1(md5($conf['adm_pwd'] . $conf['syskey']) . 'cmy *********'));
            saveSetting('pwd_en', 1);
            showmsg('后台登录密码已成功加密，请返回<br/><a href="./"><<<返回到后台首页</a>', 1);
        } else {
            showmsg('当前可加密升级后台登录密码为sha1，加密后会更安全，请选择是否加密<br/><br/><a href="./?pwd_en=1">立即加密</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="./?pwd_no_en=1">不需要谢谢</a><br/>', 1);
        }
    }
}?>

<style>
.bg-primary {
    color: #f4f3f9;
    background-color: #03a9f4 !important;
}
input[type="checkbox"]{
    width: 16px;
    height: 16px;
}
</style>
