.table {
    width: 100%;
    margin-bottom: 1rem;
    background-color: transparent
}
.table td, .table th {
    padding: .75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6
}
.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6
}
.table tbody+tbody {
    border-top: 2px solid #dee2e6
}
.table .table {
    background-color: #fff
}
.table-sm td, .table-sm th {
    padding: .3rem
}
.table-bordered {
    border: 1px solid #dee2e6
}
.table-bordered td, .table-bordered th {
    border: 1px solid #dee2e6
}
.table-bordered thead td, .table-bordered thead th {
    border-bottom-width: 2px
}
.table-borderless tbody+tbody, .table-borderless td, .table-borderless th, .table-borderless thead th {
    border: 0
}
.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, .05)
}
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, .075)
}
.table-primary, .table-primary>td, .table-primary>th {
    background-color: #b8daff
}
.table-hover .table-primary:hover {
    background-color: #9fcdff
}
.table-hover .table-primary:hover>td, .table-hover .table-primary:hover>th {
    background-color: #9fcdff
}
.table-secondary, .table-secondary>td, .table-secondary>th {
    background-color: #d6d8db
}
.table-hover .table-secondary:hover {
    background-color: #c8cbcf
}
.table-hover .table-secondary:hover>td, .table-hover .table-secondary:hover>th {
    background-color: #c8cbcf
}
.table-success, .table-success>td, .table-success>th {
    background-color: #c3e6cb
}
.table-hover .table-success:hover {
    background-color: #b1dfbb
}
.table-hover .table-success:hover>td, .table-hover .table-success:hover>th {
    background-color: #b1dfbb
}
.table-info, .table-info>td, .table-info>th {
    background-color: #bee5eb
}
.table-hover .table-info:hover {
    background-color: #abdde5
}
.table-hover .table-info:hover>td, .table-hover .table-info:hover>th {
    background-color: #abdde5
}
.table-warning, .table-warning>td, .table-warning>th {
    background-color: #ffeeba
}
.table-hover .table-warning:hover {
    background-color: #ffe8a1
}
.table-hover .table-warning:hover>td, .table-hover .table-warning:hover>th {
    background-color: #ffe8a1
}
.table-danger, .table-danger>td, .table-danger>th {
    background-color: #f5c6cb
}
.table-hover .table-danger:hover {
    background-color: #f1b0b7
}
.table-hover .table-danger:hover>td, .table-hover .table-danger:hover>th {
    background-color: #f1b0b7
}
.table-light, .table-light>td, .table-light>th {
    background-color: #fdfdfe
}
.table-hover .table-light:hover {
    background-color: #ececf6
}
.table-hover .table-light:hover>td, .table-hover .table-light:hover>th {
    background-color: #ececf6
}
.table-dark, .table-dark>td, .table-dark>th {
    background-color: #c6c8ca
}
.table-hover .table-dark:hover {
    background-color: #b9bbbe
}
.table-hover .table-dark:hover>td, .table-hover .table-dark:hover>th {
    background-color: #b9bbbe
}
.table-active, .table-active>td, .table-active>th {
    background-color: rgba(0, 0, 0, .075)
}
.table-hover .table-active:hover {
    background-color: rgba(0, 0, 0, .075)
}
.table-hover .table-active:hover>td, .table-hover .table-active:hover>th {
    background-color: rgba(0, 0, 0, .075)
}
.table .thead-dark th {
    color: #fff;
    background-color: #212529;
    border-color: #32383e
}
.table .thead-light th {
    color: #495057;
    background-color: #e9ecef;
    border-color: #dee2e6
}
.table-dark {
    color: #fff;
    background-color: #212529
}
.table-dark td, .table-dark th, .table-dark thead th {
    border-color: #32383e
}
.table-dark.table-bordered {
    border: 0
}
.table-dark.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, .05)
}
.table-dark.table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, .075)
}
@media (max-width:575.98px) {
    .table-responsive-sm {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar
    }
    .table-responsive-sm>.table-bordered {
        border: 0
    }
}
@media (max-width:767.98px) {
    .table-responsive-md {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar
    }
    .table-responsive-md>.table-bordered {
        border: 0
    }
}
@media (max-width:991.98px) {
    .table-responsive-lg {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar
    }
    .table-responsive-lg>.table-bordered {
        border: 0
    }
}
@media (max-width:1199.98px) {
    .table-responsive-xl {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar
    }
    .table-responsive-xl>.table-bordered {
        border: 0
    }
}
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar
}
.table-responsive>.table-bordered {
    border: 0
}
.form-control {
    display: block;
    width: 100%;
    height: calc(2.25rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out
}
@media screen and (prefers-reduced-motion:reduce) {
    .form-control {
        transition: none
    }
}
.form-control::-ms-expand {
    background-color: transparent;
    border: 0
}
.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25)
}
.form-control::-webkit-input-placeholder {
    color: #6c757d;
    opacity: 1
}
.form-control::-moz-placeholder {
    color: #6c757d;
    opacity: 1
}
.form-control:-ms-input-placeholder {
    color: #6c757d;
    opacity: 1
}
.form-control::-ms-input-placeholder {
    color: #6c757d;
    opacity: 1
}
.form-control::placeholder {
    color: #6c757d;
    opacity: 1
}
.form-control:disabled, .form-control[readonly] {
    background-color: #e9ecef;
    opacity: 1
}
select.form-control:focus::-ms-value {
    color: #495057;
    background-color: #fff
}
.form-control-file, .form-control-range {
    display: block;
    width: 100%
}
.col-form-label {
    padding-top: calc(.375rem + 1px);
    padding-bottom: calc(.375rem + 1px);
    margin-bottom: 0;
    font-size: inherit;
    line-height: 1.5
}
.col-form-label-lg {
    padding-top: calc(.5rem + 1px);
    padding-bottom: calc(.5rem + 1px);
    font-size: 1.25rem;
    line-height: 1.5
}
.col-form-label-sm {
    padding-top: calc(.25rem + 1px);
    padding-bottom: calc(.25rem + 1px);
    font-size: .875rem;
    line-height: 1.5
}
.form-control-plaintext {
    display: block;
    width: 100%;
    padding-top: .375rem;
    padding-bottom: .375rem;
    margin-bottom: 0;
    line-height: 1.5;
    color: #212529;
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0
}
.form-control-plaintext.form-control-lg, .form-control-plaintext.form-control-sm {
    padding-right: 0;
    padding-left: 0
}
.form-control-sm {
    height: calc(1.8125rem + 2px);
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem
}
.form-control-lg {
    height: calc(2.875rem + 2px);
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: .3rem
}
select.form-control[multiple], select.form-control[size] {
    height: auto
}
textarea.form-control {
    height: auto
}
.form-group {
    margin-bottom: 1rem
}
.form-text {
    display: block;
    margin-top: .25rem
}
.form-row {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-right: -5px;
    margin-left: -5px
}
.form-row>.col, .form-row>[class*=col-] {
    padding-right: 5px;
    padding-left: 5px
}
.form-check {
    position: relative;
    display: block;
    padding-left: 1.25rem
}
.form-check-input {
    position: absolute;
    margin-top: .3rem;
    margin-left: -1.25rem
}
.form-check-input:disabled~.form-check-label {
    color: #6c757d
}
.form-check-label {
    margin-bottom: 0
}
.form-check-inline {
    display: -ms-inline-flexbox;
    display: inline-flex;
    -ms-flex-align: center;
    align-items: center;
    padding-left: 0;
    margin-right: .75rem
}
.form-check-inline .form-check-input {
    position: static;
    margin-top: 0;
    margin-right: .3125rem;
    margin-left: 0
}
.valid-feedback {
    display: none;
    width: 100%;
    margin-top: .25rem;
    font-size: 80%;
    color: #28a745
}
.valid-tooltip {
    position: absolute;
    top: 100%;
    z-index: 5;
    display: none;
    max-width: 100%;
    padding: .25rem .5rem;
    margin-top: .1rem;
    font-size: .875rem;
    line-height: 1.5;
    color: #fff;
    background-color: rgba(40, 167, 69, .9);
    border-radius: .25rem
}
.custom-select.is-valid, .form-control.is-valid, .was-validated .custom-select:valid, .was-validated .form-control:valid {
    border-color: #28a745
}
.custom-select.is-valid:focus, .form-control.is-valid:focus, .was-validated .custom-select:valid:focus, .was-validated .form-control:valid:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .25)
}
.custom-select.is-valid~.valid-feedback, .custom-select.is-valid~.valid-tooltip, .form-control.is-valid~.valid-feedback, .form-control.is-valid~.valid-tooltip, .was-validated .custom-select:valid~.valid-feedback, .was-validated .custom-select:valid~.valid-tooltip, .was-validated .form-control:valid~.valid-feedback, .was-validated .form-control:valid~.valid-tooltip {
    display: block
}
.form-control-file.is-valid~.valid-feedback, .form-control-file.is-valid~.valid-tooltip, .was-validated .form-control-file:valid~.valid-feedback, .was-validated .form-control-file:valid~.valid-tooltip {
    display: block
}
.form-check-input.is-valid~.form-check-label, .was-validated .form-check-input:valid~.form-check-label {
    color: #28a745
}
.form-check-input.is-valid~.valid-feedback, .form-check-input.is-valid~.valid-tooltip, .was-validated .form-check-input:valid~.valid-feedback, .was-validated .form-check-input:valid~.valid-tooltip {
    display: block
}
.custom-control-input.is-valid~.custom-control-label, .was-validated .custom-control-input:valid~.custom-control-label {
    color: #28a745
}
.custom-control-input.is-valid~.custom-control-label::before, .was-validated .custom-control-input:valid~.custom-control-label::before {
    background-color: #71dd8a
}
.custom-control-input.is-valid~.valid-feedback, .custom-control-input.is-valid~.valid-tooltip, .was-validated .custom-control-input:valid~.valid-feedback, .was-validated .custom-control-input:valid~.valid-tooltip {
    display: block
}
.custom-control-input.is-valid:checked~.custom-control-label::before, .was-validated .custom-control-input:valid:checked~.custom-control-label::before {
    background-color: #34ce57
}
.custom-control-input.is-valid:focus~.custom-control-label::before, .was-validated .custom-control-input:valid:focus~.custom-control-label::before {
    box-shadow: 0 0 0 1px #fff, 0 0 0 .2rem rgba(40, 167, 69, .25)
}
.custom-file-input.is-valid~.custom-file-label, .was-validated .custom-file-input:valid~.custom-file-label {
    border-color: #28a745
}
.custom-file-input.is-valid~.custom-file-label::after, .was-validated .custom-file-input:valid~.custom-file-label::after {
    border-color: inherit
}
.custom-file-input.is-valid~.valid-feedback, .custom-file-input.is-valid~.valid-tooltip, .was-validated .custom-file-input:valid~.valid-feedback, .was-validated .custom-file-input:valid~.valid-tooltip {
    display: block
}
.custom-file-input.is-valid:focus~.custom-file-label, .was-validated .custom-file-input:valid:focus~.custom-file-label {
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .25)
}
.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: .25rem;
    font-size: 80%;
    color: #dc3545
}
.invalid-tooltip {
    position: absolute;
    top: 100%;
    z-index: 5;
    display: none;
    max-width: 100%;
    padding: .25rem .5rem;
    margin-top: .1rem;
    font-size: .875rem;
    line-height: 1.5;
    color: #fff;
    background-color: rgba(220, 53, 69, .9);
    border-radius: .25rem
}
.custom-select.is-invalid, .form-control.is-invalid, .was-validated .custom-select:invalid, .was-validated .form-control:invalid {
    border-color: #dc3545
}
.custom-select.is-invalid:focus, .form-control.is-invalid:focus, .was-validated .custom-select:invalid:focus, .was-validated .form-control:invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .25)
}
.custom-select.is-invalid~.invalid-feedback, .custom-select.is-invalid~.invalid-tooltip, .form-control.is-invalid~.invalid-feedback, .form-control.is-invalid~.invalid-tooltip, .was-validated .custom-select:invalid~.invalid-feedback, .was-validated .custom-select:invalid~.invalid-tooltip, .was-validated .form-control:invalid~.invalid-feedback, .was-validated .form-control:invalid~.invalid-tooltip {
    display: block
}
.form-control-file.is-invalid~.invalid-feedback, .form-control-file.is-invalid~.invalid-tooltip, .was-validated .form-control-file:invalid~.invalid-feedback, .was-validated .form-control-file:invalid~.invalid-tooltip {
    display: block
}
.form-check-input.is-invalid~.form-check-label, .was-validated .form-check-input:invalid~.form-check-label {
    color: #dc3545
}
.form-check-input.is-invalid~.invalid-feedback, .form-check-input.is-invalid~.invalid-tooltip, .was-validated .form-check-input:invalid~.invalid-feedback, .was-validated .form-check-input:invalid~.invalid-tooltip {
    display: block
}
.custom-control-input.is-invalid~.custom-control-label, .was-validated .custom-control-input:invalid~.custom-control-label {
    color: #dc3545
}
.custom-control-input.is-invalid~.custom-control-label::before, .was-validated .custom-control-input:invalid~.custom-control-label::before {
    background-color: #efa2a9
}
.custom-control-input.is-invalid~.invalid-feedback, .custom-control-input.is-invalid~.invalid-tooltip, .was-validated .custom-control-input:invalid~.invalid-feedback, .was-validated .custom-control-input:invalid~.invalid-tooltip {
    display: block
}
.custom-control-input.is-invalid:checked~.custom-control-label::before, .was-validated .custom-control-input:invalid:checked~.custom-control-label::before {
    background-color: #e4606d
}
.custom-control-input.is-invalid:focus~.custom-control-label::before, .was-validated .custom-control-input:invalid:focus~.custom-control-label::before {
    box-shadow: 0 0 0 1px #fff, 0 0 0 .2rem rgba(220, 53, 69, .25)
}
.custom-file-input.is-invalid~.custom-file-label, .was-validated .custom-file-input:invalid~.custom-file-label {
    border-color: #dc3545
}
.custom-file-input.is-invalid~.custom-file-label::after, .was-validated .custom-file-input:invalid~.custom-file-label::after {
    border-color: inherit
}
.custom-file-input.is-invalid~.invalid-feedback, .custom-file-input.is-invalid~.invalid-tooltip, .was-validated .custom-file-input:invalid~.invalid-feedback, .was-validated .custom-file-input:invalid~.invalid-tooltip {
    display: block
}
.custom-file-input.is-invalid:focus~.custom-file-label, .was-validated .custom-file-input:invalid:focus~.custom-file-label {
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .25)
}
.form-inline {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -ms-flex-align: center;
    align-items: center
}
.form-inline .form-check {
    width: 100%
}
@media (min-width:576px) {
    .form-inline label {
        display: -ms-flexbox;
        display: flex;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-pack: center;
        justify-content: center;
        margin-bottom: 0
    }
    .form-inline .form-group {
        display: -ms-flexbox;
        display: flex;
        -ms-flex: 0 0 auto;
        flex: 0 0 auto;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -ms-flex-align: center;
        align-items: center;
        margin-bottom: 0
    }
    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }
    .form-inline .form-control-plaintext {
        display: inline-block
    }
    .form-inline .custom-select, .form-inline .input-group {
        width: auto
    }
    .form-inline .form-check {
        display: -ms-flexbox;
        display: flex;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-pack: center;
        justify-content: center;
        width: auto;
        padding-left: 0
    }
    .form-inline .form-check-input {
        position: relative;
        margin-top: 0;
        margin-right: .25rem;
        margin-left: 0
    }
    .form-inline .custom-control {
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-pack: center;
        justify-content: center
    }
    .form-inline .custom-control-label {
        margin-bottom: 0
    }
}
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: .25rem;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out
}
@media screen and (prefers-reduced-motion:reduce) {
    .btn {
        transition: none
    }
}
.btn:focus, .btn:hover {
    text-decoration: none
}
.btn.focus, .btn:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25)
}
.btn.disabled, .btn:disabled {
    opacity: .65
}
.btn:not(:disabled):not(.disabled) {
    cursor: pointer
}
a.btn.disabled, fieldset:disabled a.btn {
    pointer-events: none
}
.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}
.btn-primary:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc
}
.btn-primary.focus, .btn-primary:focus {
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .5)
}
.btn-primary.disabled, .btn-primary:disabled {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}
.btn-primary:not(:disabled):not(.disabled).active, .btn-primary:not(:disabled):not(.disabled):active, .show>.btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #0062cc;
    border-color: #005cbf
}
.btn-primary:not(:disabled):not(.disabled).active:focus, .btn-primary:not(:disabled):not(.disabled):active:focus, .show>.btn-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .5)
}
.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d
}
.btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62
}
.btn-secondary.focus, .btn-secondary:focus {
    box-shadow: 0 0 0 .2rem rgba(108, 117, 125, .5)
}
.btn-secondary.disabled, .btn-secondary:disabled {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d
}
.btn-secondary:not(:disabled):not(.disabled).active, .btn-secondary:not(:disabled):not(.disabled):active, .show>.btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #545b62;
    border-color: #4e555b
}
.btn-secondary:not(:disabled):not(.disabled).active:focus, .btn-secondary:not(:disabled):not(.disabled):active:focus, .show>.btn-secondary.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(108, 117, 125, .5)
}
.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}
.btn-success:hover {
    color: #fff;
    background-color: #218838;
    border-color: #1e7e34
}
.btn-success.focus, .btn-success:focus {
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .5)
}
.btn-success.disabled, .btn-success:disabled {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}
.btn-success:not(:disabled):not(.disabled).active, .btn-success:not(:disabled):not(.disabled):active, .show>.btn-success.dropdown-toggle {
    color: #fff;
    background-color: #1e7e34;
    border-color: #1c7430
}
.btn-success:not(:disabled):not(.disabled).active:focus, .btn-success:not(:disabled):not(.disabled):active:focus, .show>.btn-success.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .5)
}
.btn-info {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}
.btn-info:hover {
    color: #fff;
    background-color: #138496;
    border-color: #117a8b
}
.btn-info.focus, .btn-info:focus {
    box-shadow: 0 0 0 .2rem rgba(23, 162, 184, .5)
}
.btn-info.disabled, .btn-info:disabled {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}
.btn-info:not(:disabled):not(.disabled).active, .btn-info:not(:disabled):not(.disabled):active, .show>.btn-info.dropdown-toggle {
    color: #fff;
    background-color: #117a8b;
    border-color: #10707f
}
.btn-info:not(:disabled):not(.disabled).active:focus, .btn-info:not(:disabled):not(.disabled):active:focus, .show>.btn-info.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(23, 162, 184, .5)
}
.btn-warning {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107
}
.btn-warning:hover {
    color: #212529;
    background-color: #e0a800;
    border-color: #d39e00
}
.btn-warning.focus, .btn-warning:focus {
    box-shadow: 0 0 0 .2rem rgba(255, 193, 7, .5)
}
.btn-warning.disabled, .btn-warning:disabled {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107
}
.btn-warning:not(:disabled):not(.disabled).active, .btn-warning:not(:disabled):not(.disabled):active, .show>.btn-warning.dropdown-toggle {
    color: #212529;
    background-color: #d39e00;
    border-color: #c69500
}
.btn-warning:not(:disabled):not(.disabled).active:focus, .btn-warning:not(:disabled):not(.disabled):active:focus, .show>.btn-warning.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(255, 193, 7, .5)
}
.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}
.btn-danger:hover {
    color: #fff;
    background-color: #c82333;
    border-color: #bd2130
}
.btn-danger.focus, .btn-danger:focus {
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .5)
}
.btn-danger.disabled, .btn-danger:disabled {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}
.btn-danger:not(:disabled):not(.disabled).active, .btn-danger:not(:disabled):not(.disabled):active, .show>.btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #bd2130;
    border-color: #b21f2d
}
.btn-danger:not(:disabled):not(.disabled).active:focus, .btn-danger:not(:disabled):not(.disabled):active:focus, .show>.btn-danger.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .5)
}
.btn-light {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa
}
.btn-light:hover {
    color: #212529;
    background-color: #e2e6ea;
    border-color: #dae0e5
}
.btn-light.focus, .btn-light:focus {
    box-shadow: 0 0 0 .2rem rgba(248, 249, 250, .5)
}
.btn-light.disabled, .btn-light:disabled {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa
}
.btn-light:not(:disabled):not(.disabled).active, .btn-light:not(:disabled):not(.disabled):active, .show>.btn-light.dropdown-toggle {
    color: #212529;
    background-color: #dae0e5;
    border-color: #d3d9df
}
.btn-light:not(:disabled):not(.disabled).active:focus, .btn-light:not(:disabled):not(.disabled):active:focus, .show>.btn-light.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(248, 249, 250, .5)
}
.btn-dark {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40
}
.btn-dark:hover {
    color: #fff;
    background-color: #23272b;
    border-color: #1d2124
}
.btn-dark.focus, .btn-dark:focus {
    box-shadow: 0 0 0 .2rem rgba(52, 58, 64, .5)
}
.btn-dark.disabled, .btn-dark:disabled {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40
}
.btn-dark:not(:disabled):not(.disabled).active, .btn-dark:not(:disabled):not(.disabled):active, .show>.btn-dark.dropdown-toggle {
    color: #fff;
    background-color: #1d2124;
    border-color: #171a1d
}
.btn-dark:not(:disabled):not(.disabled).active:focus, .btn-dark:not(:disabled):not(.disabled):active:focus, .show>.btn-dark.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(52, 58, 64, .5)
}
.btn-outline-primary {
    color: #007bff;
    background-color: transparent;
    background-image: none;
    border-color: #007bff
}
.btn-outline-primary:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}
.btn-outline-primary.focus, .btn-outline-primary:focus {
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .5)
}
.btn-outline-primary.disabled, .btn-outline-primary:disabled {
    color: #007bff;
    background-color: transparent
}
.btn-outline-primary:not(:disabled):not(.disabled).active, .btn-outline-primary:not(:disabled):not(.disabled):active, .show>.btn-outline-primary.dropdown-toggle {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}
.btn-outline-primary:not(:disabled):not(.disabled).active:focus, .btn-outline-primary:not(:disabled):not(.disabled):active:focus, .show>.btn-outline-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .5)
}
.btn-outline-secondary {
    color: #6c757d;
    background-color: transparent;
    background-image: none;
    border-color: #6c757d
}
.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d
}
.btn-outline-secondary.focus, .btn-outline-secondary:focus {
    box-shadow: 0 0 0 .2rem rgba(108, 117, 125, .5)
}
.btn-outline-secondary.disabled, .btn-outline-secondary:disabled {
    color: #6c757d;
    background-color: transparent
}
.btn-outline-secondary:not(:disabled):not(.disabled).active, .btn-outline-secondary:not(:disabled):not(.disabled):active, .show>.btn-outline-secondary.dropdown-toggle {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d
}
.btn-outline-secondary:not(:disabled):not(.disabled).active:focus, .btn-outline-secondary:not(:disabled):not(.disabled):active:focus, .show>.btn-outline-secondary.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(108, 117, 125, .5)
}
.btn-outline-success {
    color: #28a745;
    background-color: transparent;
    background-image: none;
    border-color: #28a745
}
.btn-outline-success:hover {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}
.btn-outline-success.focus, .btn-outline-success:focus {
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .5)
}
.btn-outline-success.disabled, .btn-outline-success:disabled {
    color: #28a745;
    background-color: transparent
}
.btn-outline-success:not(:disabled):not(.disabled).active, .btn-outline-success:not(:disabled):not(.disabled):active, .show>.btn-outline-success.dropdown-toggle {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}
.btn-outline-success:not(:disabled):not(.disabled).active:focus, .btn-outline-success:not(:disabled):not(.disabled):active:focus, .show>.btn-outline-success.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(40, 167, 69, .5)
}
.btn-outline-info {
    color: #17a2b8;
    background-color: transparent;
    background-image: none;
    border-color: #17a2b8
}
.btn-outline-info:hover {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}
.btn-outline-info.focus, .btn-outline-info:focus {
    box-shadow: 0 0 0 .2rem rgba(23, 162, 184, .5)
}
.btn-outline-info.disabled, .btn-outline-info:disabled {
    color: #17a2b8;
    background-color: transparent
}
.btn-outline-info:not(:disabled):not(.disabled).active, .btn-outline-info:not(:disabled):not(.disabled):active, .show>.btn-outline-info.dropdown-toggle {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}
.btn-outline-info:not(:disabled):not(.disabled).active:focus, .btn-outline-info:not(:disabled):not(.disabled):active:focus, .show>.btn-outline-info.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(23, 162, 184, .5)
}
.btn-outline-warning {
    color: #ffc107;
    background-color: transparent;
    background-image: none;
    border-color: #ffc107
}
.btn-outline-warning:hover {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107
}
.btn-outline-warning.focus, .btn-outline-warning:focus {
    box-shadow: 0 0 0 .2rem rgba(255, 193, 7, .5)
}
.btn-outline-warning.disabled, .btn-outline-warning:disabled {
    color: #ffc107;
    background-color: transparent
}
.btn-outline-warning:not(:disabled):not(.disabled).active, .btn-outline-warning:not(:disabled):not(.disabled):active, .show>.btn-outline-warning.dropdown-toggle {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107
}
.btn-outline-warning:not(:disabled):not(.disabled).active:focus, .btn-outline-warning:not(:disabled):not(.disabled):active:focus, .show>.btn-outline-warning.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(255, 193, 7, .5)
}
.btn-outline-danger {
    color: #dc3545;
    background-color: transparent;
    background-image: none;
    border-color: #dc3545
}
.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}
.btn-outline-danger.focus, .btn-outline-danger:focus {
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .5)
}
.btn-outline-danger.disabled, .btn-outline-danger:disabled {
    color: #dc3545;
    background-color: transparent
}
.btn-outline-danger:not(:disabled):not(.disabled).active, .btn-outline-danger:not(:disabled):not(.disabled):active, .show>.btn-outline-danger.dropdown-toggle {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}
.btn-outline-danger:not(:disabled):not(.disabled).active:focus, .btn-outline-danger:not(:disabled):not(.disabled):active:focus, .show>.btn-outline-danger.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(220, 53, 69, .5)
}
.btn-outline-light {
    color: #f8f9fa;
    background-color: transparent;
    background-image: none;
    border-color: #f8f9fa
}
.btn-outline-light:hover {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa
}
.btn-outline-light.focus, .btn-outline-light:focus {
    box-shadow: 0 0 0 .2rem rgba(248, 249, 250, .5)
}
.btn-outline-light.disabled, .btn-outline-light:disabled {
    color: #f8f9fa;
    background-color: transparent
}
.btn-outline-light:not(:disabled):not(.disabled).active, .btn-outline-light:not(:disabled):not(.disabled):active, .show>.btn-outline-light.dropdown-toggle {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa
}
.btn-outline-light:not(:disabled):not(.disabled).active:focus, .btn-outline-light:not(:disabled):not(.disabled):active:focus, .show>.btn-outline-light.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(248, 249, 250, .5)
}
.btn-outline-dark {
    color: #343a40;
    background-color: transparent;
    background-image: none;
    border-color: #343a40
}
.btn-outline-dark:hover {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40
}
.btn-outline-dark.focus, .btn-outline-dark:focus {
    box-shadow: 0 0 0 .2rem rgba(52, 58, 64, .5)
}
.btn-outline-dark.disabled, .btn-outline-dark:disabled {
    color: #343a40;
    background-color: transparent
}
.btn-outline-dark:not(:disabled):not(.disabled).active, .btn-outline-dark:not(:disabled):not(.disabled):active, .show>.btn-outline-dark.dropdown-toggle {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40
}
.btn-outline-dark:not(:disabled):not(.disabled).active:focus, .btn-outline-dark:not(:disabled):not(.disabled):active:focus, .show>.btn-outline-dark.dropdown-toggle:focus {
    box-shadow: 0 0 0 .2rem rgba(52, 58, 64, .5)
}
.btn-link {
    font-weight: 400;
    color: #007bff;
    background-color: transparent
}
.btn-link:hover {
    color: #0056b3;
    text-decoration: underline;
    background-color: transparent;
    border-color: transparent
}
.btn-link.focus, .btn-link:focus {
    text-decoration: underline;
    border-color: transparent;
    box-shadow: none
}
.btn-link.disabled, .btn-link:disabled {
    color: #6c757d;
    pointer-events: none
}
.btn-group-lg>.btn, .btn-lg {
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: .3rem
}
.btn-group-sm>.btn, .btn-sm {
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem
}
.btn-block {
    display: block;
    width: 100%
}
.btn-block+.btn-block {
    margin-top: .5rem
}
input[type=button].btn-block, input[type=reset].btn-block, input[type=submit].btn-block {
    width: 100%
}
.fade {
    transition: opacity .15s linear
}
@media screen and (prefers-reduced-motion:reduce) {
    .fade {
        transition: none
    }
}
.fade:not(.show) {
    opacity: 0
}
.collapse:not(.show) {
    display: none
}
.collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    transition: height .35s ease
}
@media screen and (prefers-reduced-motion:reduce) {
    .collapsing {
        transition: none
    }
}
.dropdown, .dropleft, .dropright, .dropup {
    position: relative
}
.dropdown-toggle::after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-bottom: 0;
    border-left: .3em solid transparent
}
.dropdown-toggle:empty::after {
    margin-left: 0
}
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: .5rem 0;
    margin: .125rem 0 0;
    font-size: 1rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .15);
    border-radius: .25rem
}
.dropdown-menu-right {
    right: 0;
    left: auto
}
.dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: .125rem
}
.dropup .dropdown-toggle::after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: 0;
    border-right: .3em solid transparent;
    border-bottom: .3em solid;
    border-left: .3em solid transparent
}
.dropup .dropdown-toggle:empty::after {
    margin-left: 0
}
.dropright .dropdown-menu {
    top: 0;
    right: auto;
    left: 100%;
    margin-top: 0;
    margin-left: .125rem
}
.dropright .dropdown-toggle::after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid transparent;
    border-right: 0;
    border-bottom: .3em solid transparent;
    border-left: .3em solid
}
.dropright .dropdown-toggle:empty::after {
    margin-left: 0
}
.dropright .dropdown-toggle::after {
    vertical-align: 0
}
.dropleft .dropdown-menu {
    top: 0;
    right: 100%;
    left: auto;
    margin-top: 0;
    margin-right: .125rem
}
.dropleft .dropdown-toggle::after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: .255em;
    vertical-align: .255em;
    content: ""
}
.dropleft .dropdown-toggle::after {
    display: none
}
.dropleft .dropdown-toggle::before {
    display: inline-block;
    width: 0;
    height: 0;
    margin-right: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid transparent;
    border-right: .3em solid;
    border-bottom: .3em solid transparent
}
.dropleft .dropdown-toggle:empty::after {
    margin-left: 0
}
.dropleft .dropdown-toggle::before {
    vertical-align: 0
}
.dropdown-menu[x-placement^=bottom], .dropdown-menu[x-placement^=left], .dropdown-menu[x-placement^=right], .dropdown-menu[x-placement^=top] {
    right: auto;
    bottom: auto
}
.dropdown-divider {
    height: 0;
    margin: .5rem 0;
    overflow: hidden;
    border-top: 1px solid #e9ecef
}
.dropdown-item {
    display: block;
    width: 100%;
    padding: .25rem 1.5rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0
}
.dropdown-item:focus, .dropdown-item:hover {
    color: #16181b;
    text-decoration: none;
    background-color: #f8f9fa
}
.dropdown-item.active, .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #007bff
}
.dropdown-item.disabled, .dropdown-item:disabled {
    color: #6c757d;
    background-color: transparent
}
.dropdown-menu.show {
    display: block
}
.dropdown-header {
    display: block;
    padding: .5rem 1.5rem;
    margin-bottom: 0;
    font-size: .875rem;
    color: #6c757d;
    white-space: nowrap
}
.dropdown-item-text {
    display: block;
    padding: .25rem 1.5rem;
    color: #212529
}
.btn-group, .btn-group-vertical {
    position: relative;
    display: -ms-inline-flexbox;
    display: inline-flex;
    vertical-align: middle
}
.btn-group-vertical>.btn, .btn-group>.btn {
    position: relative;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto
}
.btn-group-vertical>.btn:hover, .btn-group>.btn:hover {
    z-index: 1
}
.btn-group-vertical>.btn.active, .btn-group-vertical>.btn:active, .btn-group-vertical>.btn:focus, .btn-group>.btn.active, .btn-group>.btn:active, .btn-group>.btn:focus {
    z-index: 1
}
.btn-group .btn+.btn, .btn-group .btn+.btn-group, .btn-group .btn-group+.btn, .btn-group .btn-group+.btn-group, .btn-group-vertical .btn+.btn, .btn-group-vertical .btn+.btn-group, .btn-group-vertical .btn-group+.btn, .btn-group-vertical .btn-group+.btn-group {
    margin-left: -1px
}
.btn-toolbar {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-pack: start;
    justify-content: flex-start
}
.btn-toolbar .input-group {
    width: auto
}
.btn-group>.btn:first-child {
    margin-left: 0
}
.btn-group>.btn-group:not(:last-child)>.btn, .btn-group>.btn:not(:last-child):not(.dropdown-toggle) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}
.btn-group>.btn-group:not(:first-child)>.btn, .btn-group>.btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}
.dropdown-toggle-split {
    padding-right: .5625rem;
    padding-left: .5625rem
}
.dropdown-toggle-split::after, .dropright .dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after {
    margin-left: 0
}
.dropleft .dropdown-toggle-split::before {
    margin-right: 0
}
.btn-group-sm>.btn+.dropdown-toggle-split, .btn-sm+.dropdown-toggle-split {
    padding-right: .375rem;
    padding-left: .375rem
}
.btn-group-lg>.btn+.dropdown-toggle-split, .btn-lg+.dropdown-toggle-split {
    padding-right: .75rem;
    padding-left: .75rem
}
.btn-group-vertical {
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: center;
    justify-content: center
}
.btn-group-vertical .btn, .btn-group-vertical .btn-group {
    width: 100%
}
.btn-group-vertical>.btn+.btn, .btn-group-vertical>.btn+.btn-group, .btn-group-vertical>.btn-group+.btn, .btn-group-vertical>.btn-group+.btn-group {
    margin-top: -1px;
    margin-left: 0
}
.btn-group-vertical>.btn-group:not(:last-child)>.btn, .btn-group-vertical>.btn:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}
.btn-group-vertical>.btn-group:not(:first-child)>.btn, .btn-group-vertical>.btn:not(:first-child) {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}
.btn-group-toggle>.btn, .btn-group-toggle>.btn-group>.btn {
    margin-bottom: 0
}
.btn-group-toggle>.btn input[type=checkbox], .btn-group-toggle>.btn input[type=radio], .btn-group-toggle>.btn-group>.btn input[type=checkbox], .btn-group-toggle>.btn-group>.btn input[type=radio] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none
}
.input-group {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-align: stretch;
    align-items: stretch;
    width: 100%
}
.input-group>.custom-file, .input-group>.custom-select, .input-group>.form-control {
    position: relative;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    width: 1%;
    margin-bottom: 0
}
.input-group>.custom-file+.custom-file, .input-group>.custom-file+.custom-select, .input-group>.custom-file+.form-control, .input-group>.custom-select+.custom-file, .input-group>.custom-select+.custom-select, .input-group>.custom-select+.form-control, .input-group>.form-control+.custom-file, .input-group>.form-control+.custom-select, .input-group>.form-control+.form-control {
    margin-left: -1px
}
.input-group>.custom-file .custom-file-input:focus~.custom-file-label, .input-group>.custom-select:focus, .input-group>.form-control:focus {
    z-index: 3
}
.input-group>.custom-file .custom-file-input:focus {
    z-index: 4
}
.input-group>.custom-select:not(:last-child), .input-group>.form-control:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}
.input-group>.custom-select:not(:first-child), .input-group>.form-control:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}
.input-group>.custom-file {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center
}
.input-group>.custom-file:not(:last-child) .custom-file-label, .input-group>.custom-file:not(:last-child) .custom-file-label::after {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}
.input-group>.custom-file:not(:first-child) .custom-file-label {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}
.input-group-append, .input-group-prepend {
    display: -ms-flexbox;
    display: flex
}
.input-group-append .btn, .input-group-prepend .btn {
    position: relative;
    z-index: 2
}
.input-group-append .btn+.btn, .input-group-append .btn+.input-group-text, .input-group-append .input-group-text+.btn, .input-group-append .input-group-text+.input-group-text, .input-group-prepend .btn+.btn, .input-group-prepend .btn+.input-group-text, .input-group-prepend .input-group-text+.btn, .input-group-prepend .input-group-text+.input-group-text {
    margin-left: -1px
}
.input-group-prepend {
    margin-right: -1px
}
.input-group-append {
    margin-left: -1px
}
.input-group-text {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    padding: .375rem .75rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    text-align: center;
    white-space: nowrap;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: .25rem
}
.input-group-text input[type=checkbox], .input-group-text input[type=radio] {
    margin-top: 0
}
.input-group-lg>.form-control, .input-group-lg>.input-group-append>.btn, .input-group-lg>.input-group-append>.input-group-text, .input-group-lg>.input-group-prepend>.btn, .input-group-lg>.input-group-prepend>.input-group-text {
    height: calc(2.875rem + 2px);
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: .3rem
}
.input-group-sm>.form-control, .input-group-sm>.input-group-append>.btn, .input-group-sm>.input-group-append>.input-group-text, .input-group-sm>.input-group-prepend>.btn, .input-group-sm>.input-group-prepend>.input-group-text {
    height: calc(1.8125rem + 2px);
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5;
    border-radius: .2rem
}
.input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle), .input-group>.input-group-append:last-child>.input-group-text:not(:last-child), .input-group>.input-group-append:not(:last-child)>.btn, .input-group>.input-group-append:not(:last-child)>.input-group-text, .input-group>.input-group-prepend>.btn, .input-group>.input-group-prepend>.input-group-text {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
}
.input-group>.input-group-append>.btn, .input-group>.input-group-append>.input-group-text, .input-group>.input-group-prepend:first-child>.btn:not(:first-child), .input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child), .input-group>.input-group-prepend:not(:first-child)>.btn, .input-group>.input-group-prepend:not(:first-child)>.input-group-text {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
}
.custom-control {
    position: relative;
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5rem
}
.custom-control-inline {
    display: -ms-inline-flexbox;
    display: inline-flex;
    margin-right: 1rem
}
.custom-control-input {
    position: absolute;
    z-index: -1;
    opacity: 0
}
.custom-control-input:checked~.custom-control-label::before {
    color: #fff;
    background-color: #007bff
}
.custom-control-input:focus~.custom-control-label::before {
    box-shadow: 0 0 0 1px #fff, 0 0 0 .2rem rgba(0, 123, 255, .25)
}
.custom-control-input:active~.custom-control-label::before {
    color: #fff;
    background-color: #b3d7ff
}
.custom-control-input:disabled~.custom-control-label {
    color: #6c757d
}
.custom-control-input:disabled~.custom-control-label::before {
    background-color: #e9ecef
}
.custom-control-label {
    position: relative;
    margin-bottom: 0
}
.custom-control-label::before {
    position: absolute;
    top: .25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    content: "";
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: #dee2e6
}
.custom-control-label::after {
    position: absolute;
    top: .25rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    content: "";
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 50% 50%
}
.custom-checkbox .custom-control-label::before {
    border-radius: .25rem
}
.custom-checkbox .custom-control-input:checked~.custom-control-label::before {
    background-color: #007bff
}
.custom-checkbox .custom-control-input:checked~.custom-control-label::after {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E")
}
.custom-checkbox .custom-control-input:indeterminate~.custom-control-label::before {
    background-color: #007bff
}
.custom-checkbox .custom-control-input:indeterminate~.custom-control-label::after {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23fff' d='M0 2h4'/%3E%3C/svg%3E")
}
.custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
    background-color: rgba(0, 123, 255, .5)
}
.custom-checkbox .custom-control-input:disabled:indeterminate~.custom-control-label::before {
    background-color: rgba(0, 123, 255, .5)
}
.custom-radio .custom-control-label::before {
    border-radius: 50%
}
.custom-radio .custom-control-input:checked~.custom-control-label::before {
    background-color: #007bff
}
.custom-radio .custom-control-input:checked~.custom-control-label::after {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E")
}
.custom-radio .custom-control-input:disabled:checked~.custom-control-label::before {
    background-color: rgba(0, 123, 255, .5)
}
.custom-select {
    display: inline-block;
    width: 100%;
    height: calc(2.25rem + 2px);
    padding: .375rem 1.75rem .375rem .75rem;
    line-height: 1.5;
    color: #495057;
    vertical-align: middle;
    background: #fff url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right .75rem center;
    background-size: 8px 10px;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}
.custom-select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(128, 189, 255, .5)
}
.custom-select:focus::-ms-value {
    color: #495057;
    background-color: #fff
}
.custom-select[multiple], .custom-select[size]:not([size="1"]) {
    height: auto;
    padding-right: .75rem;
    background-image: none
}
.custom-select:disabled {
    color: #6c757d;
    background-color: #e9ecef
}
.custom-select::-ms-expand {
    opacity: 0
}
.custom-select-sm {
    height: calc(1.8125rem + 2px);
    padding-top: .375rem;
    padding-bottom: .375rem;
    font-size: 75%
}
.custom-select-lg {
    height: calc(2.875rem + 2px);
    padding-top: .375rem;
    padding-bottom: .375rem;
    font-size: 125%
}
.custom-file {
    position: relative;
    display: inline-block;
    width: 100%;
    height: calc(2.25rem + 2px);
    margin-bottom: 0
}
.custom-file-input {
    position: relative;
    z-index: 2;
    width: 100%;
    height: calc(2.25rem + 2px);
    margin: 0;
    opacity: 0
}
.custom-file-input:focus~.custom-file-label {
    border-color: #80bdff;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25)
}
.custom-file-input:focus~.custom-file-label::after {
    border-color: #80bdff
}
.custom-file-input:disabled~.custom-file-label {
    background-color: #e9ecef
}
.custom-file-input:lang(en)~.custom-file-label::after {
    content: "Browse"
}
.custom-file-label {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1;
    height: calc(2.25rem + 2px);
    padding: .375rem .75rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: .25rem
}
.custom-file-label::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
    display: block;
    height: 2.25rem;
    padding: .375rem .75rem;
    line-height: 1.5;
    color: #495057;
    content: "Browse";
    background-color: #e9ecef;
    border-left: 1px solid #ced4da;
    border-radius: 0 .25rem .25rem 0
}
.custom-range {
    width: 100%;
    padding-left: 0;
    background-color: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}
.custom-range:focus {
    outline: 0
}
.custom-range:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 .2rem rgba(0, 123, 255, .25)
}
.custom-range:focus::-moz-range-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 .2rem rgba(0, 123, 255, .25)
}
.custom-range:focus::-ms-thumb {
    box-shadow: 0 0 0 1px #fff, 0 0 0 .2rem rgba(0, 123, 255, .25)
}
.custom-range::-moz-focus-outer {
    border: 0
}
.custom-range::-webkit-slider-thumb {
    width: 1rem;
    height: 1rem;
    margin-top: -.25rem;
    background-color: #007bff;
    border: 0;
    border-radius: 1rem;
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    -webkit-appearance: none;
    appearance: none
}
@media screen and (prefers-reduced-motion:reduce) {
    .custom-range::-webkit-slider-thumb {
        transition: none
    }
}
.custom-range::-webkit-slider-thumb:active {
    background-color: #b3d7ff
}
.custom-range::-webkit-slider-runnable-track {
    width: 100%;
    height: .5rem;
    color: transparent;
    cursor: pointer;
    background-color: #dee2e6;
    border-color: transparent;
    border-radius: 1rem
}
.custom-range::-moz-range-thumb {
    width: 1rem;
    height: 1rem;
    background-color: #007bff;
    border: 0;
    border-radius: 1rem;
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    -moz-appearance: none;
    appearance: none
}
@media screen and (prefers-reduced-motion:reduce) {
    .custom-range::-moz-range-thumb {
        transition: none
    }
}
.custom-range::-moz-range-thumb:active {
    background-color: #b3d7ff
}
.custom-range::-moz-range-track {
    width: 100%;
    height: .5rem;
    color: transparent;
    cursor: pointer;
    background-color: #dee2e6;
    border-color: transparent;
    border-radius: 1rem
}
.custom-range::-ms-thumb {
    width: 1rem;
    height: 1rem;
    margin-top: 0;
    margin-right: .2rem;
    margin-left: .2rem;
    background-color: #007bff;
    border: 0;
    border-radius: 1rem;
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    appearance: none
}
@media screen and (prefers-reduced-motion:reduce) {
    .custom-range::-ms-thumb {
        transition: none
    }
}
.custom-range::-ms-thumb:active {
    background-color: #b3d7ff
}
.custom-range::-ms-track {
    width: 100%;
    height: .5rem;
    color: transparent;
    cursor: pointer;
    background-color: transparent;
    border-color: transparent;
    border-width: .5rem
}
.custom-range::-ms-fill-lower {
    background-color: #dee2e6;
    border-radius: 1rem
}
.custom-range::-ms-fill-upper {
    margin-right: 15px;
    background-color: #dee2e6;
    border-radius: 1rem
}
.custom-control-label::before, .custom-file-label, .custom-select {
    transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out
}
@media screen and (prefers-reduced-motion:reduce) {
    .custom-control-label::before, .custom-file-label, .custom-select {
        transition: none
    }
}
.nav {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}
.nav-link {
    display: block;
    padding: .5rem 1rem
}
.nav-link:focus, .nav-link:hover {
    text-decoration: none
}
.nav-link.disabled {
    color: #6c757d
}
.nav-tabs {
    border-bottom: 1px solid #dee2e6
}
.nav-tabs .nav-item {
    margin-bottom: -1px
}
.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem
}
.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6
}
.nav-tabs .nav-link.disabled {
    color: #6c757d;
    background-color: transparent;
    border-color: transparent
}
.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff
}
.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}
.nav-pills .nav-link {
    border-radius: .25rem
}
.nav-pills .nav-link.active, .nav-pills .show>.nav-link {
    color: #fff;
    background-color: #007bff
}
.nav-fill .nav-item {
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    text-align: center
}
.nav-justified .nav-item {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    text-align: center
}
.tab-content>.tab-pane {
    display: none
}
.tab-content>.active {
    display: block
}
.navbar {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: .5rem 1rem
}
.navbar>.container, .navbar>.container-fluid {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: justify;
    justify-content: space-between
}
.navbar-brand {
    display: inline-block;
    padding-top: .3125rem;
    padding-bottom: .3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    line-height: inherit;
    white-space: nowrap
}
.navbar-brand:focus, .navbar-brand:hover {
    text-decoration: none
}
.navbar-nav {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}
.navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 0
}
.navbar-nav .dropdown-menu {
    position: static;
    float: none
}
.navbar-text {
    display: inline-block;
    padding-top: .5rem;
    padding-bottom: .5rem
}
.navbar-collapse {
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -ms-flex-align: center;
    align-items: center
}
.navbar-toggler {
    padding: .25rem .75rem;
    font-size: 1.25rem;
    line-height: 1;
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: .25rem
}
.navbar-toggler:focus, .navbar-toggler:hover {
    text-decoration: none
}
.navbar-toggler:not(:disabled):not(.disabled) {
    cursor: pointer
}
.navbar-toggler-icon {
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    content: "";
    background: no-repeat center center;
    background-size: 100% 100%
}
@media (max-width:575.98px) {
    .navbar-expand-sm>.container, .navbar-expand-sm>.container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}
@media (min-width:576px) {
    .navbar-expand-sm {
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -ms-flex-pack: start;
        justify-content: flex-start
    }
    .navbar-expand-sm .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }
    .navbar-expand-sm .navbar-nav .dropdown-menu {
        position: absolute
    }
    .navbar-expand-sm .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }
    .navbar-expand-sm>.container, .navbar-expand-sm>.container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }
    .navbar-expand-sm .navbar-collapse {
        display: -ms-flexbox!important;
        display: flex!important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto
    }
    .navbar-expand-sm .navbar-toggler {
        display: none
    }
}
@media (max-width:767.98px) {
    .navbar-expand-md>.container, .navbar-expand-md>.container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}
@media (min-width:768px) {
    .navbar-expand-md {
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -ms-flex-pack: start;
        justify-content: flex-start
    }
    .navbar-expand-md .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }
    .navbar-expand-md .navbar-nav .dropdown-menu {
        position: absolute
    }
    .navbar-expand-md .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }
    .navbar-expand-md>.container, .navbar-expand-md>.container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }
    .navbar-expand-md .navbar-collapse {
        display: -ms-flexbox!important;
        display: flex!important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto
    }
    .navbar-expand-md .navbar-toggler {
        display: none
    }
}
@media (max-width:991.98px) {
    .navbar-expand-lg>.container, .navbar-expand-lg>.container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}
@media (min-width:992px) {
    .navbar-expand-lg {
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -ms-flex-pack: start;
        justify-content: flex-start
    }
    .navbar-expand-lg .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }
    .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute
    }
    .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }
    .navbar-expand-lg>.container, .navbar-expand-lg>.container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }
    .navbar-expand-lg .navbar-collapse {
        display: -ms-flexbox!important;
        display: flex!important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto
    }
    .navbar-expand-lg .navbar-toggler {
        display: none
    }
}
@media (max-width:1199.98px) {
    .navbar-expand-xl>.container, .navbar-expand-xl>.container-fluid {
        padding-right: 0;
        padding-left: 0
    }
}
@media (min-width:1200px) {
    .navbar-expand-xl {
        -ms-flex-flow: row nowrap;
        flex-flow: row nowrap;
        -ms-flex-pack: start;
        justify-content: flex-start
    }
    .navbar-expand-xl .navbar-nav {
        -ms-flex-direction: row;
        flex-direction: row
    }
    .navbar-expand-xl .navbar-nav .dropdown-menu {
        position: absolute
    }
    .navbar-expand-xl .navbar-nav .nav-link {
        padding-right: .5rem;
        padding-left: .5rem
    }
    .navbar-expand-xl>.container, .navbar-expand-xl>.container-fluid {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }
    .navbar-expand-xl .navbar-collapse {
        display: -ms-flexbox!important;
        display: flex!important;
        -ms-flex-preferred-size: auto;
        flex-basis: auto
    }
    .navbar-expand-xl .navbar-toggler {
        display: none
    }
}
.navbar-expand {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start
}
.navbar-expand>.container, .navbar-expand>.container-fluid {
    padding-right: 0;
    padding-left: 0
}
.navbar-expand .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row
}
.navbar-expand .navbar-nav .dropdown-menu {
    position: absolute
}
.navbar-expand .navbar-nav .nav-link {
    padding-right: .5rem;
    padding-left: .5rem
}
.navbar-expand>.container, .navbar-expand>.container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap
}
.navbar-expand .navbar-collapse {
    display: -ms-flexbox!important;
    display: flex!important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto
}
.navbar-expand .navbar-toggler {
    display: none
}
.navbar-light .navbar-brand {
    color: rgba(0, 0, 0, .9)
}
.navbar-light .navbar-brand:focus, .navbar-light .navbar-brand:hover {
    color: rgba(0, 0, 0, .9)
}
.navbar-light .navbar-nav .nav-link {
    color: rgba(0, 0, 0, .5)
}
.navbar-light .navbar-nav .nav-link:focus, .navbar-light .navbar-nav .nav-link:hover {
    color: rgba(0, 0, 0, .7)
}
.navbar-light .navbar-nav .nav-link.disabled {
    color: rgba(0, 0, 0, .3)
}
.navbar-light .navbar-nav .active>.nav-link, .navbar-light .navbar-nav .nav-link.active, .navbar-light .navbar-nav .nav-link.show, .navbar-light .navbar-nav .show>.nav-link {
    color: rgba(0, 0, 0, .9)
}
.navbar-light .navbar-toggler {
    color: rgba(0, 0, 0, .5);
    border-color: rgba(0, 0, 0, .1)
}
.navbar-light .navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E")
}
.navbar-light .navbar-text {
    color: rgba(0, 0, 0, .5)
}
.navbar-light .navbar-text a {
    color: rgba(0, 0, 0, .9)
}
.navbar-light .navbar-text a:focus, .navbar-light .navbar-text a:hover {
    color: rgba(0, 0, 0, .9)
}
.navbar-dark .navbar-brand {
    color: #fff
}
.navbar-dark .navbar-brand:focus, .navbar-dark .navbar-brand:hover {
    color: #fff
}
.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, .5)
}
.navbar-dark .navbar-nav .nav-link:focus, .navbar-dark .navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, .75)
}
.navbar-dark .navbar-nav .nav-link.disabled {
    color: rgba(255, 255, 255, .25)
}
.navbar-dark .navbar-nav .active>.nav-link, .navbar-dark .navbar-nav .nav-link.active, .navbar-dark .navbar-nav .nav-link.show, .navbar-dark .navbar-nav .show>.nav-link {
    color: #fff
}
.navbar-dark .navbar-toggler {
    color: rgba(255, 255, 255, .5);
    border-color: rgba(255, 255, 255, .1)
}
.navbar-dark .navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E")
}
.navbar-dark .navbar-text {
    color: rgba(255, 255, 255, .5)
}
.navbar-dark .navbar-text a {
    color: #fff
}
.navbar-dark .navbar-text a:focus, .navbar-dark .navbar-text a:hover {
    color: #fff
}
.card {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: .25rem
}
.card>hr {
    margin-right: 0;
    margin-left: 0
}
.card>.list-group:first-child .list-group-item:first-child {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem
}
.card>.list-group:last-child .list-group-item:last-child {
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem
}
.card-body {
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 1.25rem
}
.card-title {
    margin-bottom: .75rem
}
.card-subtitle {
    margin-top: -.375rem;
    margin-bottom: 0
}
.card-text:last-child {
    margin-bottom: 0
}
.card-link:hover {
    text-decoration: none
}
.card-link+.card-link {
    margin-left: 1.25rem
}
.card-header {
    padding: .75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, .03);
    border-bottom: 1px solid rgba(0, 0, 0, .125)
}
.card-header:first-child {
    border-radius: calc(.25rem - 1px) calc(.25rem - 1px) 0 0
}
.card-header+.list-group .list-group-item:first-child {
    border-top: 0
}
.card-footer {
    padding: .75rem 1.25rem;
    background-color: rgba(0, 0, 0, .03);
    border-top: 1px solid rgba(0, 0, 0, .125)
}
.card-footer:last-child {
    border-radius: 0 0 calc(.25rem - 1px) calc(.25rem - 1px)
}
.card-header-tabs {
    margin-right: -.625rem;
    margin-bottom: -.75rem;
    margin-left: -.625rem;
    border-bottom: 0
}
.card-header-pills {
    margin-right: -.625rem;
    margin-left: -.625rem
}
.card-img-overlay {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    padding: 1.25rem
}
.card-img {
    width: 100%;
    border-radius: calc(.25rem - 1px)
}
.card-img-top {
    width: 100%;
    border-top-left-radius: calc(.25rem - 1px);
    border-top-right-radius: calc(.25rem - 1px)
}
.card-img-bottom {
    width: 100%;
    border-bottom-right-radius: calc(.25rem - 1px);
    border-bottom-left-radius: calc(.25rem - 1px)
}
.card-deck {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column
}
.card-deck .card {
    margin-bottom: 15px
}
@media (min-width:576px) {
    .card-deck {
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        margin-right: -15px;
        margin-left: -15px
    }
    .card-deck .card {
        display: -ms-flexbox;
        display: flex;
        -ms-flex: 1 0 0%;
        flex: 1 0 0%;
        -ms-flex-direction: column;
        flex-direction: column;
        margin-right: 15px;
        margin-bottom: 0;
        margin-left: 15px
    }
}
.card-group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column
}
.card-group>.card {
    margin-bottom: 15px
}
@media (min-width:576px) {
    .card-group {
        -ms-flex-flow: row wrap;
        flex-flow: row wrap
    }
    .card-group>.card {
        -ms-flex: 1 0 0%;
        flex: 1 0 0%;
        margin-bottom: 0
    }
    .card-group>.card+.card {
        margin-left: 0;
        border-left: 0
    }
    .card-group>.card:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0
    }
    .card-group>.card:first-child .card-header, .card-group>.card:first-child .card-img-top {
        border-top-right-radius: 0
    }
    .card-group>.card:first-child .card-footer, .card-group>.card:first-child .card-img-bottom {
        border-bottom-right-radius: 0
    }
    .card-group>.card:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0
    }
    .card-group>.card:last-child .card-header, .card-group>.card:last-child .card-img-top {
        border-top-left-radius: 0
    }
    .card-group>.card:last-child .card-footer, .card-group>.card:last-child .card-img-bottom {
        border-bottom-left-radius: 0
    }
    .card-group>.card:only-child {
        border-radius: .25rem
    }
    .card-group>.card:only-child .card-header, .card-group>.card:only-child .card-img-top {
        border-top-left-radius: .25rem;
        border-top-right-radius: .25rem
    }
    .card-group>.card:only-child .card-footer, .card-group>.card:only-child .card-img-bottom {
        border-bottom-right-radius: .25rem;
        border-bottom-left-radius: .25rem
    }
    .card-group>.card:not(:first-child):not(:last-child):not(:only-child) {
        border-radius: 0
    }
    .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-footer, .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-header, .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-img-bottom, .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-img-top {
        border-radius: 0
    }
}
.card-columns .card {
    margin-bottom: .75rem
}
@media (min-width:576px) {
    .card-columns {
        -webkit-column-count: 3;
        -moz-column-count: 3;
        column-count: 3;
        -webkit-column-gap: 1.25rem;
        -moz-column-gap: 1.25rem;
        column-gap: 1.25rem;
        orphans: 1;
        widows: 1
    }
    .card-columns .card {
        display: inline-block;
        width: 100%
    }
}
.accordion .card:not(:first-of-type):not(:last-of-type) {
    border-bottom: 0;
    border-radius: 0
}
.accordion .card:not(:first-of-type) .card-header:first-child {
    border-radius: 0
}
.accordion .card:first-of-type {
    border-bottom: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}
.accordion .card:last-of-type {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}
.breadcrumb {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding: .75rem 1rem;
    margin-bottom: 1rem;
    list-style: none;
    background-color: #e9ecef;
    border-radius: .25rem
}
.breadcrumb-item+.breadcrumb-item {
    padding-left: .5rem
}
.breadcrumb-item+.breadcrumb-item::before {
    display: inline-block;
    padding-right: .5rem;
    color: #6c757d;
    content: "/"
}
.breadcrumb-item+.breadcrumb-item:hover::before {
    text-decoration: underline
}
.breadcrumb-item+.breadcrumb-item:hover::before {
    text-decoration: none
}
.breadcrumb-item.active {
    color: #6c757d
}
.pagination {
    display: -ms-flexbox;
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: .25rem
}
.page-link {
    position: relative;
    display: block;
    padding: .5rem .75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6
}
.page-link:hover {
    z-index: 2;
    color: #0056b3;
    text-decoration: none;
    background-color: #e9ecef;
    border-color: #dee2e6
}
.page-link:focus {
    z-index: 2;
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(0, 123, 255, .25)
}
.page-link:not(:disabled):not(.disabled) {
    cursor: pointer
}
.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem
}
.page-item:last-child .page-link {
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem
}
.page-item.active .page-link {
    z-index: 1;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}
.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6
}
.pagination-lg .page-link {
    padding: .75rem 1.5rem;
    font-size: 1.25rem;
    line-height: 1.5
}
.pagination-lg .page-item:first-child .page-link {
    border-top-left-radius: .3rem;
    border-bottom-left-radius: .3rem
}
.pagination-lg .page-item:last-child .page-link {
    border-top-right-radius: .3rem;
    border-bottom-right-radius: .3rem
}
.pagination-sm .page-link {
    padding: .25rem .5rem;
    font-size: .875rem;
    line-height: 1.5
}
.pagination-sm .page-item:first-child .page-link {
    border-top-left-radius: .2rem;
    border-bottom-left-radius: .2rem
}
.pagination-sm .page-item:last-child .page-link {
    border-top-right-radius: .2rem;
    border-bottom-right-radius: .2rem
}
.badge {
    display: inline-block;
    padding: .25em .4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25rem
}
.badge:empty {
    display: none
}
.btn .badge {
    position: relative;
    top: -1px
}
.badge-pill {
    padding-right: .6em;
    padding-left: .6em;
    border-radius: 10rem
}
.badge-primary {
    color: #fff;
    background-color: #007bff
}
.badge-primary[href]:focus, .badge-primary[href]:hover {
    color: #fff;
    text-decoration: none;
    background-color: #0062cc
}
.badge-secondary {
    color: #fff;
    background-color: #6c757d
}
.badge-secondary[href]:focus, .badge-secondary[href]:hover {
    color: #fff;
    text-decoration: none;
    background-color: #545b62
}
.badge-success {
    color: #fff;
    background-color: #28a745
}
.badge-success[href]:focus, .badge-success[href]:hover {
    color: #fff;
    text-decoration: none;
    background-color: #1e7e34
}
.badge-info {
    color: #fff;
    background-color: #17a2b8
}
.badge-info[href]:focus, .badge-info[href]:hover {
    color: #fff;
    text-decoration: none;
    background-color: #117a8b
}
.badge-warning {
    color: #212529;
    background-color: #ffc107
}
.badge-warning[href]:focus, .badge-warning[href]:hover {
    color: #212529;
    text-decoration: none;
    background-color: #d39e00
}
.badge-danger {
    color: #fff;
    background-color: #dc3545
}
.badge-danger[href]:focus, .badge-danger[href]:hover {
    color: #fff;
    text-decoration: none;
    background-color: #bd2130
}
.badge-light {
    color: #212529;
    background-color: #f8f9fa
}
.badge-light[href]:focus, .badge-light[href]:hover {
    color: #212529;
    text-decoration: none;
    background-color: #dae0e5
}
.badge-dark {
    color: #fff;
    background-color: #343a40
}
.badge-dark[href]:focus, .badge-dark[href]:hover {
    color: #fff;
    text-decoration: none;
    background-color: #1d2124
}
.jumbotron {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
    background-color: #e9ecef;
    border-radius: .3rem
}
@media (min-width:576px) {
    .jumbotron {
        padding: 4rem 2rem
    }
}
.jumbotron-fluid {
    padding-right: 0;
    padding-left: 0;
    border-radius: 0
}
.alert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: .25rem
}
.alert-heading {
    color: inherit
}
.alert-link {
    font-weight: 700
}
.alert-dismissible {
    padding-right: 4rem
}
.alert-dismissible .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: .75rem 1.25rem;
    color: inherit
}
.alert-primary {
    color: #004085;
    background-color: #cce5ff;
    border-color: #b8daff
}
.alert-primary hr {
    border-top-color: #9fcdff
}
.alert-primary .alert-link {
    color: #002752
}
.alert-secondary {
    color: #383d41;
    background-color: #e2e3e5;
    border-color: #d6d8db
}
.alert-secondary hr {
    border-top-color: #c8cbcf
}
.alert-secondary .alert-link {
    color: #202326
}
.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb
}
.alert-success hr {
    border-top-color: #b1dfbb
}
.alert-success .alert-link {
    color: #0b2e13
}
.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb
}
.alert-info hr {
    border-top-color: #abdde5
}
.alert-info .alert-link {
    color: #062c33
}
.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba
}
.alert-warning hr {
    border-top-color: #ffe8a1
}
.alert-warning .alert-link {
    color: #533f03
}
.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb
}
.alert-danger hr {
    border-top-color: #f1b0b7
}
.alert-danger .alert-link {
    color: #491217
}
.alert-light {
    color: #818182;
    background-color: #fefefe;
    border-color: #fdfdfe
}
.alert-light hr {
    border-top-color: #ececf6
}
.alert-light .alert-link {
    color: #686868
}
.alert-dark {
    color: #1b1e21;
    background-color: #d6d8d9;
    border-color: #c6c8ca
}
.alert-dark hr {
    border-top-color: #b9bbbe
}
.alert-dark .alert-link {
    color: #040505
}
@-webkit-keyframes progress-bar-stripes {
    from {
        background-position: 1rem 0
    }
    to {
        background-position: 0 0
    }
}
@keyframes progress-bar-stripes {
    from {
        background-position: 1rem 0
    }
    to {
        background-position: 0 0
    }
}
.progress {
    display: -ms-flexbox;
    display: flex;
    height: 1rem;
    overflow: hidden;
    font-size: .75rem;
    background-color: #e9ecef;
    border-radius: .25rem
}
.progress-bar {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-pack: center;
    justify-content: center;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    background-color: #007bff;
    transition: width .6s ease
}
@media screen and (prefers-reduced-motion:reduce) {
    .progress-bar {
        transition: none
    }
}
.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem
}
.progress-bar-animated {
    -webkit-animation: progress-bar-stripes 1s linear infinite;
    animation: progress-bar-stripes 1s linear infinite
}
.media {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start
}
.media-body {
    -ms-flex: 1;
    flex: 1
}
.list-group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0
}
.list-group-item-action {
    width: 100%;
    color: #495057;
    text-align: inherit
}
.list-group-item-action:focus, .list-group-item-action:hover {
    color: #495057;
    text-decoration: none;
    background-color: #f8f9fa
}
.list-group-item-action:active {
    color: #212529;
    background-color: #e9ecef
}
.list-group-item {
    position: relative;
    display: block;
    padding: .75rem 1.25rem;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, .125)
}
.list-group-item:first-child {
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem
}
.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem
}
.list-group-item:focus, .list-group-item:hover {
    z-index: 1;
    text-decoration: none
}
.list-group-item.disabled, .list-group-item:disabled {
    color: #6c757d;
    background-color: #fff
}
.list-group-item.active {
    z-index: 2;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff
}
.list-group-flush .list-group-item {
    border-right: 0;
    border-left: 0;
    border-radius: 0
}
.list-group-flush:first-child .list-group-item:first-child {
    border-top: 0
}
.list-group-flush:last-child .list-group-item:last-child {
    border-bottom: 0
}
.list-group-item-primary {
    color: #004085;
    background-color: #b8daff
}
.list-group-item-primary.list-group-item-action:focus, .list-group-item-primary.list-group-item-action:hover {
    color: #004085;
    background-color: #9fcdff
}
.list-group-item-primary.list-group-item-action.active {
    color: #fff;
    background-color: #004085;
    border-color: #004085
}
.list-group-item-secondary {
    color: #383d41;
    background-color: #d6d8db
}
.list-group-item-secondary.list-group-item-action:focus, .list-group-item-secondary.list-group-item-action:hover {
    color: #383d41;
    background-color: #c8cbcf
}
.list-group-item-secondary.list-group-item-action.active {
    color: #fff;
    background-color: #383d41;
    border-color: #383d41
}
.list-group-item-success {
    color: #155724;
    background-color: #c3e6cb
}
.list-group-item-success.list-group-item-action:focus, .list-group-item-success.list-group-item-action:hover {
    color: #155724;
    background-color: #b1dfbb
}
.list-group-item-success.list-group-item-action.active {
    color: #fff;
    background-color: #155724;
    border-color: #155724
}
.list-group-item-info {
    color: #0c5460;
    background-color: #bee5eb
}
.list-group-item-info.list-group-item-action:focus, .list-group-item-info.list-group-item-action:hover {
    color: #0c5460;
    background-color: #abdde5
}
.list-group-item-info.list-group-item-action.active {
    color: #fff;
    background-color: #0c5460;
    border-color: #0c5460
}
.list-group-item-warning {
    color: #856404;
    background-color: #ffeeba
}
.list-group-item-warning.list-group-item-action:focus, .list-group-item-warning.list-group-item-action:hover {
    color: #856404;
    background-color: #ffe8a1
}
.list-group-item-warning.list-group-item-action.active {
    color: #fff;
    background-color: #856404;
    border-color: #856404
}
.list-group-item-danger {
    color: #721c24;
    background-color: #f5c6cb
}
.list-group-item-danger.list-group-item-action:focus, .list-group-item-danger.list-group-item-action:hover {
    color: #721c24;
    background-color: #f1b0b7
}
.list-group-item-danger.list-group-item-action.active {
    color: #fff;
    background-color: #721c24;
    border-color: #721c24
}
.list-group-item-light {
    color: #818182;
    background-color: #fdfdfe
}
.list-group-item-light.list-group-item-action:focus, .list-group-item-light.list-group-item-action:hover {
    color: #818182;
    background-color: #ececf6
}
.list-group-item-light.list-group-item-action.active {
    color: #fff;
    background-color: #818182;
    border-color: #818182
}
.list-group-item-dark {
    color: #1b1e21;
    background-color: #c6c8ca
}
.list-group-item-dark.list-group-item-action:focus, .list-group-item-dark.list-group-item-action:hover {
    color: #1b1e21;
    background-color: #b9bbbe
}
.list-group-item-dark.list-group-item-action.active {
    color: #fff;
    background-color: #1b1e21;
    border-color: #1b1e21
}
.close {
    float: right;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5
}
.close:not(:disabled):not(.disabled) {
    cursor: pointer
}
.close:not(:disabled):not(.disabled):focus, .close:not(:disabled):not(.disabled):hover {
    color: #000;
    text-decoration: none;
    opacity: .75
}
button.close {
    padding: 0;
    background-color: transparent;
    border: 0;
    -webkit-appearance: none
}
.modal-open {
    overflow: hidden
}
.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto
}
.modal {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    display: none;
    overflow: hidden;
    outline: 0
}
.modal-dialog {
    position: relative;
    width: auto;
    margin: .5rem;
    pointer-events: none
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out, -webkit-transform .3s ease-out;
    -webkit-transform: translate(0, -25%);
    transform: translate(0, -25%)
}
@media screen and (prefers-reduced-motion:reduce) {
    .modal.fade .modal-dialog {
        transition: none
    }
}
.modal.show .modal-dialog {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0)
}
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    min-height: calc(100% - (.5rem * 2))
}
.modal-dialog-centered::before {
    display: block;
    height: calc(100vh - (.5rem * 2));
    content: ""
}
.modal-content {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: .3rem;
    outline: 0
}
.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000
}
.modal-backdrop.fade {
    opacity: 0
}
.modal-backdrop.show {
    opacity: .5
}
.modal-header {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: start;
    align-items: flex-start;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    border-top-left-radius: .3rem;
    border-top-right-radius: .3rem
}
.modal-header .close {
    padding: 1rem;
    margin: -1rem -1rem -1rem auto
}
.modal-title {
    margin-bottom: 0;
    line-height: 1.5
}
.modal-body {
    position: relative;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 1rem
}
.modal-footer {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding: 1rem;
    border-top: 1px solid #e9ecef
}
.modal-footer>:not(:first-child) {
    margin-left: .25rem
}
.modal-footer>:not(:last-child) {
    margin-right: .25rem
}
.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll
}
@media (min-width:576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto
    }
    .modal-dialog-centered {
        min-height: calc(100% - (1.75rem * 2))
    }
    .modal-dialog-centered::before {
        height: calc(100vh - (1.75rem * 2))
    }
    .modal-sm {
        max-width: 300px
    }
}
@media (min-width:992px) {
    .modal-lg {
        max-width: 800px
    }
}
.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    margin: 0;
    font-family: "Microsoft YaHei", BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    font-size: .875rem;
    word-wrap: break-word;
    opacity: 0
}
.tooltip.show {
    opacity: .9
}
.tooltip .arrow {
    position: absolute;
    display: block;
    width: .8rem;
    height: .4rem
}
.tooltip .arrow::before {
    position: absolute;
    content: "";
    border-color: transparent;
    border-style: solid
}
.bs-tooltip-auto[x-placement^=top], .bs-tooltip-top {
    padding: .4rem 0
}
.bs-tooltip-auto[x-placement^=top] .arrow, .bs-tooltip-top .arrow {
    bottom: 0
}
.bs-tooltip-auto[x-placement^=top] .arrow::before, .bs-tooltip-top .arrow::before {
    top: 0;
    border-width: .4rem .4rem 0;
    border-top-color: #000
}
.bs-tooltip-auto[x-placement^=right], .bs-tooltip-right {
    padding: 0 .4rem
}
.bs-tooltip-auto[x-placement^=right] .arrow, .bs-tooltip-right .arrow {
    left: 0;
    width: .4rem;
    height: .8rem
}
.bs-tooltip-auto[x-placement^=right] .arrow::before, .bs-tooltip-right .arrow::before {
    right: 0;
    border-width: .4rem .4rem .4rem 0;
    border-right-color: #000
}
.bs-tooltip-auto[x-placement^=bottom], .bs-tooltip-bottom {
    padding: .4rem 0
}
.bs-tooltip-auto[x-placement^=bottom] .arrow, .bs-tooltip-bottom .arrow {
    top: 0
}
.bs-tooltip-auto[x-placement^=bottom] .arrow::before, .bs-tooltip-bottom .arrow::before {
    bottom: 0;
    border-width: 0 .4rem .4rem;
    border-bottom-color: #000
}
.bs-tooltip-auto[x-placement^=left], .bs-tooltip-left {
    padding: 0 .4rem
}
.bs-tooltip-auto[x-placement^=left] .arrow, .bs-tooltip-left .arrow {
    right: 0;
    width: .4rem;
    height: .8rem
}
.bs-tooltip-auto[x-placement^=left] .arrow::before, .bs-tooltip-left .arrow::before {
    left: 0;
    border-width: .4rem 0 .4rem .4rem;
    border-left-color: #000
}
.tooltip-inner {
    max-width: 200px;
    padding: .25rem .5rem;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: .25rem
}
.popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1060;
    display: block;
    max-width: 276px;
    font-family: "Microsoft YaHei", BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    font-size: .875rem;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: .3rem
}
.popover .arrow {
    position: absolute;
    display: block;
    width: 1rem;
    height: .5rem;
    margin: 0 .3rem
}
.popover .arrow::after, .popover .arrow::before {
    position: absolute;
    display: block;
    content: "";
    border-color: transparent;
    border-style: solid
}
.bs-popover-auto[x-placement^=top], .bs-popover-top {
    margin-bottom: .5rem
}
.bs-popover-auto[x-placement^=top] .arrow, .bs-popover-top .arrow {
    bottom: calc((.5rem + 1px) * -1)
}
.bs-popover-auto[x-placement^=top] .arrow::after, .bs-popover-auto[x-placement^=top] .arrow::before, .bs-popover-top .arrow::after, .bs-popover-top .arrow::before {
    border-width: .5rem .5rem 0
}
.bs-popover-auto[x-placement^=top] .arrow::before, .bs-popover-top .arrow::before {
    bottom: 0;
    border-top-color: rgba(0, 0, 0, .25)
}
.bs-popover-auto[x-placement^=top] .arrow::after, .bs-popover-top .arrow::after {
    bottom: 1px;
    border-top-color: #fff
}
.bs-popover-auto[x-placement^=right], .bs-popover-right {
    margin-left: .5rem
}
.bs-popover-auto[x-placement^=right] .arrow, .bs-popover-right .arrow {
    left: calc((.5rem + 1px) * -1);
    width: .5rem;
    height: 1rem;
    margin: .3rem 0
}
.bs-popover-auto[x-placement^=right] .arrow::after, .bs-popover-auto[x-placement^=right] .arrow::before, .bs-popover-right .arrow::after, .bs-popover-right .arrow::before {
    border-width: .5rem .5rem .5rem 0
}
.bs-popover-auto[x-placement^=right] .arrow::before, .bs-popover-right .arrow::before {
    left: 0;
    border-right-color: rgba(0, 0, 0, .25)
}
.bs-popover-auto[x-placement^=right] .arrow::after, .bs-popover-right .arrow::after {
    left: 1px;
    border-right-color: #fff
}
.bs-popover-auto[x-placement^=bottom], .bs-popover-bottom {
    margin-top: .5rem
}
.bs-popover-auto[x-placement^=bottom] .arrow, .bs-popover-bottom .arrow {
    top: calc((.5rem + 1px) * -1)
}
.bs-popover-auto[x-placement^=bottom] .arrow::after, .bs-popover-auto[x-placement^=bottom] .arrow::before, .bs-popover-bottom .arrow::after, .bs-popover-bottom .arrow::before {
    border-width: 0 .5rem .5rem .5rem
}
.bs-popover-auto[x-placement^=bottom] .arrow::before, .bs-popover-bottom .arrow::before {
    top: 0;
    border-bottom-color: rgba(0, 0, 0, .25)
}
.bs-popover-auto[x-placement^=bottom] .arrow::after, .bs-popover-bottom .arrow::after {
    top: 1px;
    border-bottom-color: #fff
}
.bs-popover-auto[x-placement^=bottom] .popover-header::before, .bs-popover-bottom .popover-header::before {
    position: absolute;
    top: 0;
    left: 50%;
    display: block;
    width: 1rem;
    margin-left: -.5rem;
    content: "";
    border-bottom: 1px solid #f7f7f7
}
.bs-popover-auto[x-placement^=left], .bs-popover-left {
    margin-right: .5rem
}
.bs-popover-auto[x-placement^=left] .arrow, .bs-popover-left .arrow {
    right: calc((.5rem + 1px) * -1);
    width: .5rem;
    height: 1rem;
    margin: .3rem 0
}
.bs-popover-auto[x-placement^=left] .arrow::after, .bs-popover-auto[x-placement^=left] .arrow::before, .bs-popover-left .arrow::after, .bs-popover-left .arrow::before {
    border-width: .5rem 0 .5rem .5rem
}
.bs-popover-auto[x-placement^=left] .arrow::before, .bs-popover-left .arrow::before {
    right: 0;
    border-left-color: rgba(0, 0, 0, .25)
}
.bs-popover-auto[x-placement^=left] .arrow::after, .bs-popover-left .arrow::after {
    right: 1px;
    border-left-color: #fff
}
.popover-header {
    padding: .5rem .75rem;
    margin-bottom: 0;
    font-size: 1rem;
    color: inherit;
    background-color: #f7f7f7;
    border-bottom: 1px solid #ebebeb;
    border-top-left-radius: calc(.3rem - 1px);
    border-top-right-radius: calc(.3rem - 1px)
}
.popover-header:empty {
    display: none
}
.popover-body {
    padding: .5rem .75rem;
    color: #212529
}
.carousel {
    position: relative
}
.carousel-inner {
    position: relative;
    width: 100%;
    overflow: hidden
}
.carousel-item {
    position: relative;
    display: none;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    perspective: 1000px
}
.carousel-item-next, .carousel-item-prev, .carousel-item.active {
    display: block;
    transition: -webkit-transform .6s ease;
    transition: transform .6s ease;
    transition: transform .6s ease, -webkit-transform .6s ease
}
@media screen and (prefers-reduced-motion:reduce) {
    .carousel-item-next, .carousel-item-prev, .carousel-item.active {
        transition: none
    }
}
.carousel-item-next, .carousel-item-prev {
    position: absolute;
    top: 0
}
.carousel-item-next.carousel-item-left, .carousel-item-prev.carousel-item-right {
    -webkit-transform: translateX(0);
    transform: translateX(0)
}
@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {
    .carousel-item-next.carousel-item-left, .carousel-item-prev.carousel-item-right {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.active.carousel-item-right, .carousel-item-next {
    -webkit-transform: translateX(100%);
    transform: translateX(100%)
}
@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {
    .active.carousel-item-right, .carousel-item-next {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0)
    }
}
.active.carousel-item-left, .carousel-item-prev {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%)
}
@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {
    .active.carousel-item-left, .carousel-item-prev {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0)
    }
}
.carousel-fade .carousel-item {
    opacity: 0;
    transition-duration: .6s;
    transition-property: opacity
}
.carousel-fade .carousel-item-next.carousel-item-left, .carousel-fade .carousel-item-prev.carousel-item-right, .carousel-fade .carousel-item.active {
    opacity: 1
}
.carousel-fade .active.carousel-item-left, .carousel-fade .active.carousel-item-right {
    opacity: 0
}
.carousel-fade .active.carousel-item-left, .carousel-fade .active.carousel-item-prev, .carousel-fade .carousel-item-next, .carousel-fade .carousel-item-prev, .carousel-fade .carousel-item.active {
    -webkit-transform: translateX(0);
    transform: translateX(0)
}
@supports ((-webkit-transform-style:preserve-3d) or (transform-style:preserve-3d)) {
    .carousel-fade .active.carousel-item-left, .carousel-fade .active.carousel-item-prev, .carousel-fade .carousel-item-next, .carousel-fade .carousel-item-prev, .carousel-fade .carousel-item.active {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.carousel-control-next, .carousel-control-prev {
    position: absolute;
    top: 0;
    bottom: 0;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 15%;
    color: #fff;
    text-align: center;
    opacity: .5
}
.carousel-control-next:focus, .carousel-control-next:hover, .carousel-control-prev:focus, .carousel-control-prev:hover {
    color: #fff;
    text-decoration: none;
    outline: 0;
    opacity: .9
}
.carousel-control-prev {
    left: 0
}
.carousel-control-next {
    right: 0
}
.carousel-control-next-icon, .carousel-control-prev-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: transparent no-repeat center center;
    background-size: 100% 100%
}
.carousel-control-prev-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E")
}
.carousel-control-next-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E")
}
.carousel-indicators {
    position: absolute;
    right: 0;
    bottom: 10px;
    left: 0;
    z-index: 15;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 0;
    margin-right: 15%;
    margin-left: 15%;
    list-style: none
}
.carousel-indicators li {
    position: relative;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    width: 30px;
    height: 3px;
    margin-right: 3px;
    margin-left: 3px;
    text-indent: -999px;
    cursor: pointer;
    background-color: rgba(255, 255, 255, .5)
}
.carousel-indicators li::before {
    position: absolute;
    top: -10px;
    left: 0;
    display: inline-block;
    width: 100%;
    height: 10px;
    content: ""
}
.carousel-indicators li::after {
    position: absolute;
    bottom: -10px;
    left: 0;
    display: inline-block;
    width: 100%;
    height: 10px;
    content: ""
}
.carousel-indicators .active {
    background-color: #fff
}
.carousel-caption {
    position: absolute;
    right: 15%;
    bottom: 20px;
    left: 15%;
    z-index: 10;
    padding-top: 20px;
    padding-bottom: 20px;
    color: #fff;
    text-align: center
}
.align-baseline {
    vertical-align: baseline!important
}
.align-top {
    vertical-align: top!important
}
.align-middle {
    vertical-align: middle!important
}
.align-bottom {
    vertical-align: bottom!important
}
.align-text-bottom {
    vertical-align: text-bottom!important
}
.align-text-top {
    vertical-align: text-top!important
}
.bg-primary {
    background-color: #007bff!important
}
a.bg-primary:focus, a.bg-primary:hover, button.bg-primary:focus, button.bg-primary:hover {
    background-color: #0062cc!important
}
.bg-secondary {
    background-color: #6c757d!important
}
a.bg-secondary:focus, a.bg-secondary:hover, button.bg-secondary:focus, button.bg-secondary:hover {
    background-color: #545b62!important
}
.bg-success {
    background-color: #28a745!important
}
a.bg-success:focus, a.bg-success:hover, button.bg-success:focus, button.bg-success:hover {
    background-color: #1e7e34!important
}
.bg-info {
    background-color: #17a2b8!important
}
a.bg-info:focus, a.bg-info:hover, button.bg-info:focus, button.bg-info:hover {
    background-color: #117a8b!important
}
.bg-warning {
    background-color: #ffc107!important
}
a.bg-warning:focus, a.bg-warning:hover, button.bg-warning:focus, button.bg-warning:hover {
    background-color: #d39e00!important
}
.bg-danger {
    background-color: #dc3545!important
}
a.bg-danger:focus, a.bg-danger:hover, button.bg-danger:focus, button.bg-danger:hover {
    background-color: #bd2130!important
}
.bg-light {
    background-color: #f8f9fa!important
}
a.bg-light:focus, a.bg-light:hover, button.bg-light:focus, button.bg-light:hover {
    background-color: #dae0e5!important
}
.bg-dark {
    background-color: #343a40!important
}
a.bg-dark:focus, a.bg-dark:hover, button.bg-dark:focus, button.bg-dark:hover {
    background-color: #1d2124!important
}
.bg-white {
    background-color: #fff!important
}
.bg-transparent {
    background-color: transparent!important
}
.border {
    border: 1px solid #dee2e6!important
}
.border-top {
    border-top: 1px solid #dee2e6!important
}
.border-right {
    border-right: 1px solid #dee2e6!important
}
.border-bottom {
    border-bottom: 1px solid #dee2e6!important
}
.border-left {
    border-left: 1px solid #dee2e6!important
}
.border-0 {
    border: 0!important
}
.border-top-0 {
    border-top: 0!important
}
.border-right-0 {
    border-right: 0!important
}
.border-bottom-0 {
    border-bottom: 0!important
}
.border-left-0 {
    border-left: 0!important
}
.border-primary {
    border-color: #007bff!important
}
.border-secondary {
    border-color: #6c757d!important
}
.border-success {
    border-color: #28a745!important
}
.border-info {
    border-color: #17a2b8!important
}
.border-warning {
    border-color: #ffc107!important
}
.border-danger {
    border-color: #dc3545!important
}
.border-light {
    border-color: #f8f9fa!important
}
.border-dark {
    border-color: #343a40!important
}
.border-white {
    border-color: #fff!important
}
.rounded {
    border-radius: .25rem!important
}
.rounded-top {
    border-top-left-radius: .25rem!important;
    border-top-right-radius: .25rem!important
}
.rounded-right {
    border-top-right-radius: .25rem!important;
    border-bottom-right-radius: .25rem!important
}
.rounded-bottom {
    border-bottom-right-radius: .25rem!important;
    border-bottom-left-radius: .25rem!important
}
.rounded-left {
    border-top-left-radius: .25rem!important;
    border-bottom-left-radius: .25rem!important
}
.rounded-circle {
    border-radius: 50%!important
}
.rounded-0 {
    border-radius: 0!important
}
.clearfix::after {
    display: block;
    clear: both;
    content: ""
}
.d-none {
    display: none!important
}
.d-inline {
    display: inline!important
}
.d-inline-block {
    display: inline-block!important
}
.d-block {
    display: block!important
}
.d-table {
    display: table!important
}
.d-table-row {
    display: table-row!important
}
.d-table-cell {
    display: table-cell!important
}
.d-flex {
    display: -ms-flexbox!important;
    display: flex!important
}
.d-inline-flex {
    display: -ms-inline-flexbox!important;
    display: inline-flex!important
}
@media (min-width:576px) {
    .d-sm-none {
        display: none!important
    }
    .d-sm-inline {
        display: inline!important
    }
    .d-sm-inline-block {
        display: inline-block!important
    }
    .d-sm-block {
        display: block!important
    }
    .d-sm-table {
        display: table!important
    }
    .d-sm-table-row {
        display: table-row!important
    }
    .d-sm-table-cell {
        display: table-cell!important
    }
    .d-sm-flex {
        display: -ms-flexbox!important;
        display: flex!important
    }
    .d-sm-inline-flex {
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }
}
@media (min-width:768px) {
    .d-md-none {
        display: none!important
    }
    .d-md-inline {
        display: inline!important
    }
    .d-md-inline-block {
        display: inline-block!important
    }
    .d-md-block {
        display: block!important
    }
    .d-md-table {
        display: table!important
    }
    .d-md-table-row {
        display: table-row!important
    }
    .d-md-table-cell {
        display: table-cell!important
    }
    .d-md-flex {
        display: -ms-flexbox!important;
        display: flex!important
    }
    .d-md-inline-flex {
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }
}
@media (min-width:992px) {
    .d-lg-none {
        display: none!important
    }
    .d-lg-inline {
        display: inline!important
    }
    .d-lg-inline-block {
        display: inline-block!important
    }
    .d-lg-block {
        display: block!important
    }
    .d-lg-table {
        display: table!important
    }
    .d-lg-table-row {
        display: table-row!important
    }
    .d-lg-table-cell {
        display: table-cell!important
    }
    .d-lg-flex {
        display: -ms-flexbox!important;
        display: flex!important
    }
    .d-lg-inline-flex {
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }
}
@media (min-width:1200px) {
    .d-xl-none {
        display: none!important
    }
    .d-xl-inline {
        display: inline!important
    }
    .d-xl-inline-block {
        display: inline-block!important
    }
    .d-xl-block {
        display: block!important
    }
    .d-xl-table {
        display: table!important
    }
    .d-xl-table-row {
        display: table-row!important
    }
    .d-xl-table-cell {
        display: table-cell!important
    }
    .d-xl-flex {
        display: -ms-flexbox!important;
        display: flex!important
    }
    .d-xl-inline-flex {
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }
}
@media print {
    .d-print-none {
        display: none!important
    }
    .d-print-inline {
        display: inline!important
    }
    .d-print-inline-block {
        display: inline-block!important
    }
    .d-print-block {
        display: block!important
    }
    .d-print-table {
        display: table!important
    }
    .d-print-table-row {
        display: table-row!important
    }
    .d-print-table-cell {
        display: table-cell!important
    }
    .d-print-flex {
        display: -ms-flexbox!important;
        display: flex!important
    }
    .d-print-inline-flex {
        display: -ms-inline-flexbox!important;
        display: inline-flex!important
    }
}
.embed-responsive {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    overflow: hidden
}
.embed-responsive::before {
    display: block;
    content: ""
}
.embed-responsive .embed-responsive-item, .embed-responsive embed, .embed-responsive iframe, .embed-responsive object, .embed-responsive video {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0
}
.embed-responsive-21by9::before {
    padding-top: 42.857143%
}
.embed-responsive-16by9::before {
    padding-top: 56.25%
}
.embed-responsive-4by3::before {
    padding-top: 75%
}
.embed-responsive-1by1::before {
    padding-top: 100%
}
.flex-row {
    -ms-flex-direction: row!important;
    flex-direction: row!important
}
.flex-column {
    -ms-flex-direction: column!important;
    flex-direction: column!important
}
.flex-row-reverse {
    -ms-flex-direction: row-reverse!important;
    flex-direction: row-reverse!important
}
.flex-column-reverse {
    -ms-flex-direction: column-reverse!important;
    flex-direction: column-reverse!important
}
.flex-wrap {
    -ms-flex-wrap: wrap!important;
    flex-wrap: wrap!important
}
.flex-nowrap {
    -ms-flex-wrap: nowrap!important;
    flex-wrap: nowrap!important
}
.flex-wrap-reverse {
    -ms-flex-wrap: wrap-reverse!important;
    flex-wrap: wrap-reverse!important
}
.flex-fill {
    -ms-flex: 1 1 auto!important;
    flex: 1 1 auto!important
}
.flex-grow-0 {
    -ms-flex-positive: 0!important;
    flex-grow: 0!important
}
.flex-grow-1 {
    -ms-flex-positive: 1!important;
    flex-grow: 1!important
}
.flex-shrink-0 {
    -ms-flex-negative: 0!important;
    flex-shrink: 0!important
}
.flex-shrink-1 {
    -ms-flex-negative: 1!important;
    flex-shrink: 1!important
}
.justify-content-start {
    -ms-flex-pack: start!important;
    justify-content: flex-start!important
}
.justify-content-end {
    -ms-flex-pack: end!important;
    justify-content: flex-end!important
}
.justify-content-center {
    -ms-flex-pack: center!important;
    justify-content: center!important
}
.justify-content-between {
    -ms-flex-pack: justify!important;
    justify-content: space-between!important
}
.justify-content-around {
    -ms-flex-pack: distribute!important;
    justify-content: space-around!important
}
.align-items-start {
    -ms-flex-align: start!important;
    align-items: flex-start!important
}
.align-items-end {
    -ms-flex-align: end!important;
    align-items: flex-end!important
}
.align-items-center {
    -ms-flex-align: center!important;
    align-items: center!important
}
.align-items-baseline {
    -ms-flex-align: baseline!important;
    align-items: baseline!important
}
.align-items-stretch {
    -ms-flex-align: stretch!important;
    align-items: stretch!important
}
.align-content-start {
    -ms-flex-line-pack: start!important;
    align-content: flex-start!important
}
.align-content-end {
    -ms-flex-line-pack: end!important;
    align-content: flex-end!important
}
.align-content-center {
    -ms-flex-line-pack: center!important;
    align-content: center!important
}
.align-content-between {
    -ms-flex-line-pack: justify!important;
    align-content: space-between!important
}
.align-content-around {
    -ms-flex-line-pack: distribute!important;
    align-content: space-around!important
}
.align-content-stretch {
    -ms-flex-line-pack: stretch!important;
    align-content: stretch!important
}
.align-self-auto {
    -ms-flex-item-align: auto!important;
    align-self: auto!important
}
.align-self-start {
    -ms-flex-item-align: start!important;
    align-self: flex-start!important
}
.align-self-end {
    -ms-flex-item-align: end!important;
    align-self: flex-end!important
}
.align-self-center {
    -ms-flex-item-align: center!important;
    align-self: center!important
}
.align-self-baseline {
    -ms-flex-item-align: baseline!important;
    align-self: baseline!important
}
.align-self-stretch {
    -ms-flex-item-align: stretch!important;
    align-self: stretch!important
}
@media (min-width:576px) {
    .flex-sm-row {
        -ms-flex-direction: row!important;
        flex-direction: row!important
    }
    .flex-sm-column {
        -ms-flex-direction: column!important;
        flex-direction: column!important
    }
    .flex-sm-row-reverse {
        -ms-flex-direction: row-reverse!important;
        flex-direction: row-reverse!important
    }
    .flex-sm-column-reverse {
        -ms-flex-direction: column-reverse!important;
        flex-direction: column-reverse!important
    }
    .flex-sm-wrap {
        -ms-flex-wrap: wrap!important;
        flex-wrap: wrap!important
    }
    .flex-sm-nowrap {
        -ms-flex-wrap: nowrap!important;
        flex-wrap: nowrap!important
    }
    .flex-sm-wrap-reverse {
        -ms-flex-wrap: wrap-reverse!important;
        flex-wrap: wrap-reverse!important
    }
    .flex-sm-fill {
        -ms-flex: 1 1 auto!important;
        flex: 1 1 auto!important
    }
    .flex-sm-grow-0 {
        -ms-flex-positive: 0!important;
        flex-grow: 0!important
    }
    .flex-sm-grow-1 {
        -ms-flex-positive: 1!important;
        flex-grow: 1!important
    }
    .flex-sm-shrink-0 {
        -ms-flex-negative: 0!important;
        flex-shrink: 0!important
    }
    .flex-sm-shrink-1 {
        -ms-flex-negative: 1!important;
        flex-shrink: 1!important
    }
    .justify-content-sm-start {
        -ms-flex-pack: start!important;
        justify-content: flex-start!important
    }
    .justify-content-sm-end {
        -ms-flex-pack: end!important;
        justify-content: flex-end!important
    }
    .justify-content-sm-center {
        -ms-flex-pack: center!important;
        justify-content: center!important
    }
    .justify-content-sm-between {
        -ms-flex-pack: justify!important;
        justify-content: space-between!important
    }
    .justify-content-sm-around {
        -ms-flex-pack: distribute!important;
        justify-content: space-around!important
    }
    .align-items-sm-start {
        -ms-flex-align: start!important;
        align-items: flex-start!important
    }
    .align-items-sm-end {
        -ms-flex-align: end!important;
        align-items: flex-end!important
    }
    .align-items-sm-center {
        -ms-flex-align: center!important;
        align-items: center!important
    }
    .align-items-sm-baseline {
        -ms-flex-align: baseline!important;
        align-items: baseline!important
    }
    .align-items-sm-stretch {
        -ms-flex-align: stretch!important;
        align-items: stretch!important
    }
    .align-content-sm-start {
        -ms-flex-line-pack: start!important;
        align-content: flex-start!important
    }
    .align-content-sm-end {
        -ms-flex-line-pack: end!important;
        align-content: flex-end!important
    }
    .align-content-sm-center {
        -ms-flex-line-pack: center!important;
        align-content: center!important
    }
    .align-content-sm-between {
        -ms-flex-line-pack: justify!important;
        align-content: space-between!important
    }
    .align-content-sm-around {
        -ms-flex-line-pack: distribute!important;
        align-content: space-around!important
    }
    .align-content-sm-stretch {
        -ms-flex-line-pack: stretch!important;
        align-content: stretch!important
    }
    .align-self-sm-auto {
        -ms-flex-item-align: auto!important;
        align-self: auto!important
    }
    .align-self-sm-start {
        -ms-flex-item-align: start!important;
        align-self: flex-start!important
    }
    .align-self-sm-end {
        -ms-flex-item-align: end!important;
        align-self: flex-end!important
    }
    .align-self-sm-center {
        -ms-flex-item-align: center!important;
        align-self: center!important
    }
    .align-self-sm-baseline {
        -ms-flex-item-align: baseline!important;
        align-self: baseline!important
    }
    .align-self-sm-stretch {
        -ms-flex-item-align: stretch!important;
        align-self: stretch!important
    }
}
@media (min-width:768px) {
    .flex-md-row {
        -ms-flex-direction: row!important;
        flex-direction: row!important
    }
    .flex-md-column {
        -ms-flex-direction: column!important;
        flex-direction: column!important
    }
    .flex-md-row-reverse {
        -ms-flex-direction: row-reverse!important;
        flex-direction: row-reverse!important
    }
    .flex-md-column-reverse {
        -ms-flex-direction: column-reverse!important;
        flex-direction: column-reverse!important
    }
    .flex-md-wrap {
        -ms-flex-wrap: wrap!important;
        flex-wrap: wrap!important
    }
    .flex-md-nowrap {
        -ms-flex-wrap: nowrap!important;
        flex-wrap: nowrap!important
    }
    .flex-md-wrap-reverse {
        -ms-flex-wrap: wrap-reverse!important;
        flex-wrap: wrap-reverse!important
    }
    .flex-md-fill {
        -ms-flex: 1 1 auto!important;
        flex: 1 1 auto!important
    }
    .flex-md-grow-0 {
        -ms-flex-positive: 0!important;
        flex-grow: 0!important
    }
    .flex-md-grow-1 {
        -ms-flex-positive: 1!important;
        flex-grow: 1!important
    }
    .flex-md-shrink-0 {
        -ms-flex-negative: 0!important;
        flex-shrink: 0!important
    }
    .flex-md-shrink-1 {
        -ms-flex-negative: 1!important;
        flex-shrink: 1!important
    }
    .justify-content-md-start {
        -ms-flex-pack: start!important;
        justify-content: flex-start!important
    }
    .justify-content-md-end {
        -ms-flex-pack: end!important;
        justify-content: flex-end!important
    }
    .justify-content-md-center {
        -ms-flex-pack: center!important;
        justify-content: center!important
    }
    .justify-content-md-between {
        -ms-flex-pack: justify!important;
        justify-content: space-between!important
    }
    .justify-content-md-around {
        -ms-flex-pack: distribute!important;
        justify-content: space-around!important
    }
    .align-items-md-start {
        -ms-flex-align: start!important;
        align-items: flex-start!important
    }
    .align-items-md-end {
        -ms-flex-align: end!important;
        align-items: flex-end!important
    }
    .align-items-md-center {
        -ms-flex-align: center!important;
        align-items: center!important
    }
    .align-items-md-baseline {
        -ms-flex-align: baseline!important;
        align-items: baseline!important
    }
    .align-items-md-stretch {
        -ms-flex-align: stretch!important;
        align-items: stretch!important
    }
    .align-content-md-start {
        -ms-flex-line-pack: start!important;
        align-content: flex-start!important
    }
    .align-content-md-end {
        -ms-flex-line-pack: end!important;
        align-content: flex-end!important
    }
    .align-content-md-center {
        -ms-flex-line-pack: center!important;
        align-content: center!important
    }
    .align-content-md-between {
        -ms-flex-line-pack: justify!important;
        align-content: space-between!important
    }
    .align-content-md-around {
        -ms-flex-line-pack: distribute!important;
        align-content: space-around!important
    }
    .align-content-md-stretch {
        -ms-flex-line-pack: stretch!important;
        align-content: stretch!important
    }
    .align-self-md-auto {
        -ms-flex-item-align: auto!important;
        align-self: auto!important
    }
    .align-self-md-start {
        -ms-flex-item-align: start!important;
        align-self: flex-start!important
    }
    .align-self-md-end {
        -ms-flex-item-align: end!important;
        align-self: flex-end!important
    }
    .align-self-md-center {
        -ms-flex-item-align: center!important;
        align-self: center!important
    }
    .align-self-md-baseline {
        -ms-flex-item-align: baseline!important;
        align-self: baseline!important
    }
    .align-self-md-stretch {
        -ms-flex-item-align: stretch!important;
        align-self: stretch!important
    }
}
@media (min-width:992px) {
    .flex-lg-row {
        -ms-flex-direction: row!important;
        flex-direction: row!important
    }
    .flex-lg-column {
        -ms-flex-direction: column!important;
        flex-direction: column!important
    }
    .flex-lg-row-reverse {
        -ms-flex-direction: row-reverse!important;
        flex-direction: row-reverse!important
    }
    .flex-lg-column-reverse {
        -ms-flex-direction: column-reverse!important;
        flex-direction: column-reverse!important
    }
    .flex-lg-wrap {
        -ms-flex-wrap: wrap!important;
        flex-wrap: wrap!important
    }
    .flex-lg-nowrap {
        -ms-flex-wrap: nowrap!important;
        flex-wrap: nowrap!important
    }
    .flex-lg-wrap-reverse {
        -ms-flex-wrap: wrap-reverse!important;
        flex-wrap: wrap-reverse!important
    }
    .flex-lg-fill {
        -ms-flex: 1 1 auto!important;
        flex: 1 1 auto!important
    }
    .flex-lg-grow-0 {
        -ms-flex-positive: 0!important;
        flex-grow: 0!important
    }
    .flex-lg-grow-1 {
        -ms-flex-positive: 1!important;
        flex-grow: 1!important
    }
    .flex-lg-shrink-0 {
        -ms-flex-negative: 0!important;
        flex-shrink: 0!important
    }
    .flex-lg-shrink-1 {
        -ms-flex-negative: 1!important;
        flex-shrink: 1!important
    }
    .justify-content-lg-start {
        -ms-flex-pack: start!important;
        justify-content: flex-start!important
    }
    .justify-content-lg-end {
        -ms-flex-pack: end!important;
        justify-content: flex-end!important
    }
    .justify-content-lg-center {
        -ms-flex-pack: center!important;
        justify-content: center!important
    }
    .justify-content-lg-between {
        -ms-flex-pack: justify!important;
        justify-content: space-between!important
    }
    .justify-content-lg-around {
        -ms-flex-pack: distribute!important;
        justify-content: space-around!important
    }
    .align-items-lg-start {
        -ms-flex-align: start!important;
        align-items: flex-start!important
    }
    .align-items-lg-end {
        -ms-flex-align: end!important;
        align-items: flex-end!important
    }
    .align-items-lg-center {
        -ms-flex-align: center!important;
        align-items: center!important
    }
    .align-items-lg-baseline {
        -ms-flex-align: baseline!important;
        align-items: baseline!important
    }
    .align-items-lg-stretch {
        -ms-flex-align: stretch!important;
        align-items: stretch!important
    }
    .align-content-lg-start {
        -ms-flex-line-pack: start!important;
        align-content: flex-start!important
    }
    .align-content-lg-end {
        -ms-flex-line-pack: end!important;
        align-content: flex-end!important
    }
    .align-content-lg-center {
        -ms-flex-line-pack: center!important;
        align-content: center!important
    }
    .align-content-lg-between {
        -ms-flex-line-pack: justify!important;
        align-content: space-between!important
    }
    .align-content-lg-around {
        -ms-flex-line-pack: distribute!important;
        align-content: space-around!important
    }
    .align-content-lg-stretch {
        -ms-flex-line-pack: stretch!important;
        align-content: stretch!important
    }
    .align-self-lg-auto {
        -ms-flex-item-align: auto!important;
        align-self: auto!important
    }
    .align-self-lg-start {
        -ms-flex-item-align: start!important;
        align-self: flex-start!important
    }
    .align-self-lg-end {
        -ms-flex-item-align: end!important;
        align-self: flex-end!important
    }
    .align-self-lg-center {
        -ms-flex-item-align: center!important;
        align-self: center!important
    }
    .align-self-lg-baseline {
        -ms-flex-item-align: baseline!important;
        align-self: baseline!important
    }
    .align-self-lg-stretch {
        -ms-flex-item-align: stretch!important;
        align-self: stretch!important
    }
}
@media (min-width:1200px) {
    .flex-xl-row {
        -ms-flex-direction: row!important;
        flex-direction: row!important
    }
    .flex-xl-column {
        -ms-flex-direction: column!important;
        flex-direction: column!important
    }
    .flex-xl-row-reverse {
        -ms-flex-direction: row-reverse!important;
        flex-direction: row-reverse!important
    }
    .flex-xl-column-reverse {
        -ms-flex-direction: column-reverse!important;
        flex-direction: column-reverse!important
    }
    .flex-xl-wrap {
        -ms-flex-wrap: wrap!important;
        flex-wrap: wrap!important
    }
    .flex-xl-nowrap {
        -ms-flex-wrap: nowrap!important;
        flex-wrap: nowrap!important
    }
    .flex-xl-wrap-reverse {
        -ms-flex-wrap: wrap-reverse!important;
        flex-wrap: wrap-reverse!important
    }
    .flex-xl-fill {
        -ms-flex: 1 1 auto!important;
        flex: 1 1 auto!important
    }
    .flex-xl-grow-0 {
        -ms-flex-positive: 0!important;
        flex-grow: 0!important
    }
    .flex-xl-grow-1 {
        -ms-flex-positive: 1!important;
        flex-grow: 1!important
    }
    .flex-xl-shrink-0 {
        -ms-flex-negative: 0!important;
        flex-shrink: 0!important
    }
    .flex-xl-shrink-1 {
        -ms-flex-negative: 1!important;
        flex-shrink: 1!important
    }
    .justify-content-xl-start {
        -ms-flex-pack: start!important;
        justify-content: flex-start!important
    }
    .justify-content-xl-end {
        -ms-flex-pack: end!important;
        justify-content: flex-end!important
    }
    .justify-content-xl-center {
        -ms-flex-pack: center!important;
        justify-content: center!important
    }
    .justify-content-xl-between {
        -ms-flex-pack: justify!important;
        justify-content: space-between!important
    }
    .justify-content-xl-around {
        -ms-flex-pack: distribute!important;
        justify-content: space-around!important
    }
    .align-items-xl-start {
        -ms-flex-align: start!important;
        align-items: flex-start!important
    }
    .align-items-xl-end {
        -ms-flex-align: end!important;
        align-items: flex-end!important
    }
    .align-items-xl-center {
        -ms-flex-align: center!important;
        align-items: center!important
    }
    .align-items-xl-baseline {
        -ms-flex-align: baseline!important;
        align-items: baseline!important
    }
    .align-items-xl-stretch {
        -ms-flex-align: stretch!important;
        align-items: stretch!important
    }
    .align-content-xl-start {
        -ms-flex-line-pack: start!important;
        align-content: flex-start!important
    }
    .align-content-xl-end {
        -ms-flex-line-pack: end!important;
        align-content: flex-end!important
    }
    .align-content-xl-center {
        -ms-flex-line-pack: center!important;
        align-content: center!important
    }
    .align-content-xl-between {
        -ms-flex-line-pack: justify!important;
        align-content: space-between!important
    }
    .align-content-xl-around {
        -ms-flex-line-pack: distribute!important;
        align-content: space-around!important
    }
    .align-content-xl-stretch {
        -ms-flex-line-pack: stretch!important;
        align-content: stretch!important
    }
    .align-self-xl-auto {
        -ms-flex-item-align: auto!important;
        align-self: auto!important
    }
    .align-self-xl-start {
        -ms-flex-item-align: start!important;
        align-self: flex-start!important
    }
    .align-self-xl-end {
        -ms-flex-item-align: end!important;
        align-self: flex-end!important
    }
    .align-self-xl-center {
        -ms-flex-item-align: center!important;
        align-self: center!important
    }
    .align-self-xl-baseline {
        -ms-flex-item-align: baseline!important;
        align-self: baseline!important
    }
    .align-self-xl-stretch {
        -ms-flex-item-align: stretch!important;
        align-self: stretch!important
    }
}
.float-left {
    float: left!important
}
.float-right {
    float: right!important
}
.float-none {
    float: none!important
}
@media (min-width:576px) {
    .float-sm-left {
        float: left!important
    }
    .float-sm-right {
        float: right!important
    }
    .float-sm-none {
        float: none!important
    }
}
@media (min-width:768px) {
    .float-md-left {
        float: left!important
    }
    .float-md-right {
        float: right!important
    }
    .float-md-none {
        float: none!important
    }
}
@media (min-width:992px) {
    .float-lg-left {
        float: left!important
    }
    .float-lg-right {
        float: right!important
    }
    .float-lg-none {
        float: none!important
    }
}
@media (min-width:1200px) {
    .float-xl-left {
        float: left!important
    }
    .float-xl-right {
        float: right!important
    }
    .float-xl-none {
        float: none!important
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0
}
.sr-only-focusable:active, .sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    overflow: visible;
    clip: auto;
    white-space: normal
}
.shadow-sm {
    box-shadow: 0 .125rem .25rem rgba(0, 0, 0, .075)!important
}
.shadow {
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15)!important
}
.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, .175)!important
}
.shadow-none {
    box-shadow: none!important
}
.w-25 {
    width: 25%!important
}
.w-50 {
    width: 50%!important
}
.w-75 {
    width: 75%!important
}
.w-100 {
    width: 100%!important
}
.w-auto {
    width: auto!important
}
.h-25 {
    height: 25%!important
}
.h-50 {
    height: 50%!important
}
.h-75 {
    height: 75%!important
}
.h-100 {
    height: 100%!important
}
.h-auto {
    height: auto!important
}
.mw-100 {
    max-width: 100%!important
}
.mh-100 {
    max-height: 100%!important
}

.text-monospace {
    font-family: "Microsoft YaHei", SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
}
.text-justify {
    text-align: justify!important
}
.text-nowrap {
    white-space: nowrap!important
}
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}
.text-left {
    text-align: left!important
}
.text-right {
    text-align: right!important
}
.text-center {
    text-align: center!important
}
@media (min-width:576px) {
    .text-sm-left {
        text-align: left!important
    }
    .text-sm-right {
        text-align: right!important
    }
    .text-sm-center {
        text-align: center!important
    }
}
@media (min-width:768px) {
    .text-md-left {
        text-align: left!important
    }
    .text-md-right {
        text-align: right!important
    }
    .text-md-center {
        text-align: center!important
    }
}
@media (min-width:992px) {
    .text-lg-left {
        text-align: left!important
    }
    .text-lg-right {
        text-align: right!important
    }
    .text-lg-center {
        text-align: center!important
    }
}
@media (min-width:1200px) {
    .text-xl-left {
        text-align: left!important
    }
    .text-xl-right {
        text-align: right!important
    }
    .text-xl-center {
        text-align: center!important
    }
}
.text-lowercase {
    text-transform: lowercase!important
}
.text-uppercase {
    text-transform: uppercase!important
}
.text-capitalize {
    text-transform: capitalize!important
}
.font-weight-light {
    font-weight: 300!important
}
.font-weight-normal {
    font-weight: 400!important
}
.font-weight-bold {
    font-weight: 700!important
}
.font-italic {
    font-style: italic!important
}
.text-white {
    color: #fff!important
}
.text-primary {
    color: #007bff!important
}
a.text-primary:focus, a.text-primary:hover {
    color: #0062cc!important
}
.text-secondary {
    color: #6c757d!important
}
a.text-secondary:focus, a.text-secondary:hover {
    color: #545b62!important
}
.text-success {
    color: #28a745!important
}
a.text-success:focus, a.text-success:hover {
    color: #1e7e34!important
}
.text-info {
    color: #17a2b8!important
}
a.text-info:focus, a.text-info:hover {
    color: #117a8b!important
}
.text-warning {
    color: #ffc107!important
}
a.text-warning:focus, a.text-warning:hover {
    color: #d39e00!important
}
.text-danger {
    color: #dc3545!important
}
a.text-danger:focus, a.text-danger:hover {
    color: #bd2130!important
}
.text-light {
    color: #f8f9fa!important
}
a.text-light:focus, a.text-light:hover {
    color: #dae0e5!important
}
.text-dark {
    color: #343a40!important
}
a.text-dark:focus, a.text-dark:hover {
    color: #1d2124!important
}
.text-body {
    color: #212529!important
}
.text-muted {
    color: #6c757d!important
}
.text-black-50 {
    color: rgba(0, 0, 0, .5)!important
}
.text-white-50 {
    color: rgba(255, 255, 255, .5)!important
}
.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0
}
.visible {
    visibility: visible!important
}
.invisible {
    visibility: hidden!important
}
@media print {
    *, ::after, ::before {
        text-shadow: none!important;
        box-shadow: none!important
    }
    a:not(.btn) {
        text-decoration: underline
    }
    abbr[title]::after {
        content: " (" attr(title) ")"
    }
    pre {
        white-space: pre-wrap!important
    }
    blockquote, pre {
        border: 1px solid #adb5bd;
        page-break-inside: avoid
    }
    thead {
        display: table-header-group
    }
    img, tr {
        page-break-inside: avoid
    }
    h2, h3, p {
        orphans: 3;
        widows: 3
    }
    h2, h3 {
        page-break-after: avoid
    }
    @page {
        size: a3
    }
    body {
        min-width: 992px!important
    }
    .container {
        min-width: 992px!important
    }
    .navbar {
        display: none
    }
    .badge {
        border: 1px solid #000
    }
    .table {
        border-collapse: collapse!important
    }
    .table td, .table th {
        background-color: #fff!important
    }
    .table-bordered td, .table-bordered th {
        border: 1px solid #dee2e6!important
    }
    .table-dark {
        color: inherit
    }
    .table-dark tbody+tbody, .table-dark td, .table-dark th, .table-dark thead th {
        border-color: #dee2e6
    }
    .table .thead-dark th {
        color: inherit;
        border-color: #dee2e6
    }
}
@font-face {
    font-family: 'Web Icons';
    font-weight: 400;
    font-style: normal;
    src: url(../../../../../app/system/include/static2/fonts/web-icons/./web-icons.eot?v=0.2.3);
    src: url(../../../../../app/system/include/static2/fonts/web-icons/./web-icons.eot?#iefix&v=0.2.3) format('embedded-opentype'), url(../../../../../app/system/include/static2/fonts/web-icons/./web-icons.woff2?v=0.2.3) format('woff2'), url(../../../../../app/system/include/static2/fonts/web-icons/./web-icons.woff?v=0.2.3) format('woff'), url(../../../../../app/system/include/static2/fonts/web-icons/./web-icons.ttf?v=0.2.3) format('truetype'), url(../../../../../app/system/include/static2/fonts/web-icons/./web-icons.svg?v=0.2.3#web-icons) format('svg')
}
[class*=' wb-'], [class^=wb-] {
    font-family: 'Web Icons';
    font-weight: 400;
    font-style: normal;
    position: relative;
    display: inline-block;
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    text-rendering: auto;
    speak: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}
.wb-dashboard:before {
    content: ''
}
.wb-inbox:before {
    content: ''
}
.wb-cloud:before {
    content: ''
}
.wb-bell:before {
    content: ''
}
.wb-book:before {
    content: ''
}
.wb-bookmark:before {
    content: ''
}
.wb-tag:before {
    content: ''
}
.wb-library:before {
    content: ''
}
.wb-share:before {
    content: ''
}
.wb-reply:before {
    content: ''
}
.wb-refresh:before {
    content: ''
}
.wb-move:before {
    content: ''
}
.wb-chat:before {
    content: ''
}
.wb-chat-working:before {
    content: ''
}
.wb-chat-text:before {
    content: ''
}
.wb-chat-group:before {
    content: ''
}
.wb-envelope:before {
    content: ''
}
.wb-envelope-open:before {
    content: ''
}
.wb-user:before {
    content: ''
}
.wb-user-circle:before {
    content: ''
}
.wb-users:before {
    content: ''
}
.wb-user-add:before {
    content: ''
}
.wb-grid-9:before {
    content: ''
}
.wb-grid-4:before {
    content: ''
}
.wb-menu:before {
    content: ''
}
.wb-layout:before {
    content: ''
}
.wb-fullscreen:before {
    content: ''
}
.wb-fullscreen-exit:before {
    content: ''
}
.wb-expand:before {
    content: ''
}
.wb-contract:before {
    content: ''
}
.wb-arrow-expand:before {
    content: ''
}
.wb-arrow-shrink:before {
    content: ''
}
.wb-desktop:before {
    content: ''
}
.wb-mobile:before {
    content: ''
}
.wb-signal:before {
    content: ''
}
.wb-power:before {
    content: ''
}
.wb-more-horizontal:before {
    content: ''
}
.wb-more-vertical:before {
    content: ''
}
.wb-globe:before {
    content: ''
}
.wb-map:before {
    content: ''
}
.wb-flag:before {
    content: ''
}
.wb-pie-chart:before {
    content: ''
}
.wb-stats-bars:before {
    content: ''
}
.wb-pluse:before {
    content: ''
}
.wb-home:before {
    content: ''
}
.wb-shopping-cart:before {
    content: ''
}
.wb-payment:before {
    content: ''
}
.wb-briefcase:before {
    content: ''
}
.wb-search:before {
    content: ''
}
.wb-zoom-in:before {
    content: ''
}
.wb-zoom-out:before {
    content: ''
}
.wb-download:before {
    content: ''
}
.wb-upload:before {
    content: ''
}
.wb-sort-asc:before {
    content: ''
}
.wb-sort-des:before {
    content: ''
}
.wb-graph-up:before {
    content: ''
}
.wb-graph-down:before {
    content: ''
}
.wb-replay:before {
    content: ''
}
.wb-edit:before {
    content: ''
}
.wb-pencil:before {
    content: ''
}
.wb-rubber:before {
    content: ''
}
.wb-crop:before {
    content: ''
}
.wb-eye:before {
    content: ''
}
.wb-eye-close:before {
    content: ''
}
.wb-image:before {
    content: ''
}
.wb-gallery:before {
    content: ''
}
.wb-video:before {
    content: ''
}
.wb-camera:before {
    content: ''
}
.wb-folder:before {
    content: ''
}
.wb-clipboard:before {
    content: ''
}
.wb-order:before {
    content: ''
}
.wb-file:before {
    content: ''
}
.wb-copy:before {
    content: ''
}
.wb-add-file:before {
    content: ''
}
.wb-print:before {
    content: ''
}
.wb-calendar:before {
    content: ''
}
.wb-time:before {
    content: ''
}
.wb-trash:before {
    content: ''
}
.wb-plugin:before {
    content: ''
}
.wb-extension:before {
    content: ''
}
.wb-memory:before {
    content: ''
}
.wb-settings:before {
    content: ''
}
.wb-scissor:before {
    content: ''
}
.wb-wrench:before {
    content: ''
}
.wb-hammer:before {
    content: ''
}
.wb-lock:before {
    content: ''
}
.wb-unlock:before {
    content: ''
}
.wb-volume-low:before {
    content: ''
}
.wb-volume-high:before {
    content: ''
}
.wb-volume-off:before {
    content: ''
}
.wb-pause:before {
    content: ''
}
.wb-play:before {
    content: ''
}
.wb-stop:before {
    content: ''
}
.wb-musical:before {
    content: ''
}
.wb-random:before {
    content: ''
}
.wb-reload:before {
    content: ''
}
.wb-loop:before {
    content: ''
}
.wb-text:before {
    content: ''
}
.wb-bold:before {
    content: ''
}
.wb-italic:before {
    content: ''
}
.wb-underline:before {
    content: ''
}
.wb-format-clear:before {
    content: ''
}
.wb-text-type:before {
    content: ''
}
.wb-table:before {
    content: ''
}
.wb-attach-file:before {
    content: ''
}
.wb-paperclip:before {
    content: ''
}
.wb-link-intact:before {
    content: ''
}
.wb-link:before {
    content: ''
}
.wb-link-broken:before {
    content: ''
}
.wb-indent-increase:before {
    content: ''
}
.wb-indent-decrease:before {
    content: ''
}
.wb-align-justify:before {
    content: ''
}
.wb-align-left:before {
    content: ''
}
.wb-align-center:before {
    content: ''
}
.wb-align-right:before {
    content: ''
}
.wb-list-numbered:before {
    content: ''
}
.wb-list-bulleted:before {
    content: ''
}
.wb-list:before {
    content: ''
}
.wb-emoticon:before {
    content: ''
}
.wb-quote-right:before {
    content: ''
}
.wb-code:before {
    content: ''
}
.wb-code-working:before {
    content: ''
}
.wb-code-unfold:before {
    content: ''
}
.wb-chevron-right:before {
    content: ''
}
.wb-chevron-left:before {
    content: ''
}
.wb-chevron-left-mini:before {
    content: ''
}
.wb-chevron-right-mini:before {
    content: ''
}
.wb-chevron-up:before {
    content: ''
}
.wb-chevron-down:before {
    content: ''
}
.wb-chevron-up-mini:before {
    content: ''
}
.wb-chevron-down-mini:before {
    content: ''
}
.wb-arrow-left:before {
    content: ''
}
.wb-arrow-right:before {
    content: ''
}
.wb-arrow-up:before {
    content: ''
}
.wb-arrow-down:before {
    content: ''
}
.wb-dropdown:before {
    content: ''
}
.wb-dropup:before {
    content: ''
}
.wb-dropright:before {
    content: ''
}
.wb-dropleft:before {
    content: ''
}
.wb-sort-vertical:before {
    content: ''
}
.wb-triangle-left:before {
    content: ''
}
.wb-triangle-right:before {
    content: ''
}
.wb-triangle-down:before {
    content: ''
}
.wb-triangle-up:before {
    content: ''
}
.wb-check-circle:before {
    content: ''
}
.wb-check:before {
    content: ''
}
.wb-check-mini:before {
    content: ''
}
.wb-close:before {
    content: ''
}
.wb-close-mini:before {
    content: ''
}
.wb-plus-circle:before {
    content: ''
}
.wb-plus:before {
    content: ''
}
.wb-minus-circle:before {
    content: ''
}
.wb-minus:before {
    content: ''
}
.wb-alert-circle:before {
    content: ''
}
.wb-alert:before {
    content: ''
}
.wb-help-circle:before {
    content: ''
}
.wb-help:before {
    content: ''
}
.wb-info-circle:before {
    content: ''
}
.wb-info:before {
    content: ''
}
.wb-warning:before {
    content: ''
}
.wb-heart:before {
    content: ''
}
.wb-heart-outline:before {
    content: ''
}
.wb-star:before {
    content: ''
}
.wb-star-half:before {
    content: ''
}
.wb-star-outline:before {
    content: ''
}
.wb-thumb-up:before {
    content: ''
}
.wb-thumb-down:before {
    content: ''
}
.wb-small-point:before {
    content: ''
}
.wb-medium-point:before {
    content: ''
}
.wb-large-point:before {
    content: ''
}
.pull-right {
    float: right
}
@font-face {
    font-family: 'FontAwesome';
    src: url(../../../../../app/system/include/static2/fonts/font-awesome/./fontawesome-webfont.eot?v=4.7.0);
    src: url(../../../../../app/system/include/static2/fonts/font-awesome/./fontawesome-webfont.eot?#iefix&v=4.7.0) format('embedded-opentype'), url(../../../../../app/system/include/static2/fonts/font-awesome/./fontawesome-webfont.woff2?v=4.7.0) format('woff2'), url(../../../../../app/system/include/static2/fonts/font-awesome/./fontawesome-webfont.woff?v=4.7.0) format('woff'), url(../../../../../app/system/include/static2/fonts/font-awesome/./fontawesome-webfont.ttf?v=4.7.0) format('truetype'), url(../../../../../app/system/include/static2/fonts/font-awesome/./fontawesome-webfont.svg?v=4.7.0#fontawesomeregular) format('svg');
    font-weight: normal;
    font-style: normal
}
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}
.sr-only-focusable:active, .sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto
}
body {
    overflow-x: hidden;
    color: #2a333c
}
a:focus {
    outline: none
}
.decorationnone, .dropdown-item:active, a:active, .decorationnone:active, .dropdown-item:focus, a:focus, .decorationnone:focus, .dropdown-item:hover, a:hover, .decorationnone:hover {
    text-decoration: none
}
.decoration:hover {
    text-decoration: underline
}
a:hover {
    color: #4e97d9
}
img.imgloading:not([src*="base64"]), img.slick-loading:not([src*="base64"]) {
    object-fit: scale-down;
    background: #fff
}
.table {
    margin-bottom: 0;
    color: #333
}
.table a {
    text-decoration: none
}
.table thead th, .table tfoot th, .table td {
    vertical-align: middle;
    outline: none
}
.table td .tag:not(.tag-pill) {
    line-height: inherit
}
table th {
    background-color: #f3f7f9;
    font-weight: bold !important
}
table td.text-xs-center input[type="text"], table th.text-xs-center input[type="text"] {
    text-align: center
}
@media (min-width:480px) {
    table th {
        white-space: nowrap
    }
    table.form-inline .form-group, table.form-inline .form-control {
        display: block;
        width: 100%
    }
}
div.dataTables_paginate .paginate_button.active>a, div.dataTables_paginate .paginate_button.active>a:focus, div.dataTables_paginate .paginate_button.active>a:hover {
    background: #ddd !important;
    border-color: #ddd !important;
    color: #777 !important
}
.dataTables_empty {
    text-align: center
}
.dataTables_info {
    color: #777
}
body table.table-bordered.dataTable {
    border-collapse: collapse !important
}
.table-striped.dataTable tbody tr.active td, body .table-striped.dataTable tbody tr.active:hover td {
    background: #f3f7f9 !important;
    color: inherit !important
}
.table-striped.dataTable tbody tr.active td>a, body .table-striped.dataTable tbody tr.active:hover td>a {
    color: #62a8ea !important
}
@media (max-width:479px) {
    .dataTables_info {
        float: none !important
    }
    .dataTables_paginate {
        float: none !important
    }
}
.alertify-logs {
    bottom: auto!important;
    top: 48%;
    z-index: 1701!important
}
@media (max-width:767px) {
    .nav-tabs .nav-link {
        padding: 5px 10px
    }
}
.form-group {
    position: relative
}
.form-group .small-fixed {
    position: absolute;
    left: 0;
    top: 100%
}
.form-group .small-fixed small.form-control-label {
    margin: 0;
    margin-right: 10px;
    padding: 0;
    line-height: 1.5
}
.input-group-file .btn-file input[type="file"] {
    width: 100%;
    height: 100%
}
.input-group .form-control:active, .input-group .form-control:focus, .input-group .form-control:hover {
    z-index: 3
}
.form-inline .checkbox-custom label {
    padding-left: 0
}
.form-control-label {
    color: #555
}
.form-group:before, .form-group:after {
    display: table;
    content: " "
}
.form-group:after {
    clear: both
}
table.form-inline .form-group {
    margin-right: 0
}
.met-scroll-top {
    position: fixed;
    right: 10px;
    bottom: 10px;
    z-index: 10
}
.nav-tabs .nav-link {
    color: #333
}
pre {
    overflow: visible
}
.edui-editor, .token-input {
    max-width: 100%
}
.edui-default .edui-editor-toolbarbox {
    position: relative !important
}
.clockpicker-popover .popover-title {
    font-weight: normal!important
}
.met-fixed, .fixed-b-l {
    position: fixed;
    left: 0;
    z-index: 1000
}
.met-fixed {
    top: 0
}
.fixed-b-l {
    bottom: 0
}
.overflow-visible {
    overflow: visible!important
}
.oya {
    overflow-y: auto!important
}
.oxa {
    overflow-x: auto!important
}
.h-10 {
    height: 10px
}
.h-20 {
    height: 20px
}
.h-100p {
    height: 100%
}
.hide {
    display: none !important
}
#getcode, .met-getcode {
    height: 24px
}
.opacity1, .slick-arrow:hover i {
    opacity: 1
}
.opacity0 {
    opacity: 0
}
.flex, .slick-track {
    display: flex
}
.slick-track {
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center
}
.radius0 {
    -webkit-border-radius: 0px !important;
    -moz-border-radius: 0px !important;
    -o-border-radius: 0px !important;
    border-radius: 0px !important
}
.radius3 {
    -webkit-border-radius: 3px !important;
    -moz-border-radius: 3px !important;
    -o-border-radius: 3px !important;
    border-radius: 3px !important
}
.radius5 {
    -webkit-border-radius: 5px !important;
    -moz-border-radius: 5px !important;
    -o-border-radius: 5px !important;
    border-radius: 5px !important
}
.radius10 {
    -webkit-border-radius: 10px !important;
    -moz-border-radius: 10px !important;
    -o-border-radius: 10px !important;
    border-radius: 10px !important
}
.box-shadow-none {
    box-shadow: none
}
.box-shadow1 {
    box-shadow: 0 1px 1px rgba(0, 0, 0, .05)
}
.ulstyle {
    margin: 0;
    padding: 0px;
    list-style: none
}
.border-none {
    border: none!important
}
.border-top1 {
    border-top: 1px solid #f0f2f5
}
.border-bottom1 {
    border-bottom: 1px solid #f0f2f5
}
.border-type1 {
    border: 1px solid #ccc
}
.flex-start {
    -webkit-justify-content: flex-start!important;
    justify-content: flex-start!important
}
.flex-center {
    -webkit-justify-content: center;
    justify-content: center
}
.flex-end {
    -webkit-justify-content: flex-end;
    justify-content: flex-end
}
.text-shadow-none {
    text-shadow: none
}
.dropdown-item, .transition500 {
    transition: all 0.5s!important
}
.modal-content {
    overflow: hidden
}
.page-content {
    padding: 30px 0px
}
.webui-popover {
    z-index: 1600
}
.pearl-title {
    white-space: normal
}
[class*="animation-"] {
    animation-fill-mode: none
}
@media (max-width:991px) {
    .page-content {
        padding: 20px 0px
    }
    .panel {
        margin-bottom: 20px
    }
    .panel-body {
        padding: 20px
    }
}
@media (max-width:767px) {
    .page-content {
        padding: 15px 0px
    }
    .panel, .well, .card-shadow {
        margin-bottom: 15px
    }
    .panel-body, .well, .modal-body, .modal-header, .modal-footer {
        padding: 15px
    }
    .well, .card-shadow {
        margin-bottom: 15px
    }
}
@media screen\0 {
    select.form-control {
        padding-right: 15px
    }
}
.tag-sm {
    font-size: 12px
}
.w-a {
    width: auto !important
}
[class*=animation-] {
    -webkit-animation-duration: .5s;
    -o-animation-duration: .5s;
    animation-duration: .5s;
    -webkit-animation-timing-function: ease-out;
    -o-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
    -webkit-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both
}
.animation-slide-top10 {
    -webkit-animation-name: slide-top10;
    animation-name: slide-top10
}
@-webkit-keyframes slide-top10 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -10%, 0);
        transform: translate3d(0, -10%, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
@keyframes slide-top10 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -10%, 0);
        transform: translate3d(0, -10%, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.animation-slide-bottom10 {
    -webkit-animation-name: slide-bottom10;
    animation-name: slide-bottom10
}
@-webkit-keyframes slide-bottom10 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 10%, 0);
        transform: translate3d(0, 10%, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
@keyframes slide-bottom10 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 10%, 0);
        transform: translate3d(0, 10%, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.animation-slide-left10 {
    -webkit-animation-name: slide-left10;
    animation-name: slide-left10
}
@-webkit-keyframes slide-left10 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-10%, 0, 0);
        transform: translate3d(-10%, 0, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
@keyframes slide-left10 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-10%, 0, 0);
        transform: translate3d(-10%, 0, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.animation-slide-right10 {
    -webkit-animation-name: slide-right10;
    animation-name: slide-right10
}
@-webkit-keyframes slide-right10 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(10%, 0, 0);
        transform: translate3d(10%, 0, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
@keyframes slide-right10 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(10%, 0, 0);
        transform: translate3d(10%, 0, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.animation-slide-top50 {
    -webkit-animation-name: slide-top50;
    animation-name: slide-top50
}
@-webkit-keyframes slide-top50 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -50%, 0);
        transform: translate3d(0, -50%, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
@keyframes slide-top50 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -50%, 0);
        transform: translate3d(0, -50%, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.animation-slide-bottom50 {
    -webkit-animation-name: slide-bottom50;
    animation-name: slide-bottom50
}
@-webkit-keyframes slide-bottom50 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 50%, 0);
        transform: translate3d(0, 50%, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
@keyframes slide-bottom50 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 50%, 0);
        transform: translate3d(0, 50%, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.animation-slide-left50 {
    -webkit-animation-name: slide-left50;
    animation-name: slide-left50
}
@-webkit-keyframes slide-left50 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-50%, 0, 0);
        transform: translate3d(-50%, 0, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
@keyframes slide-left50 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-50%, 0, 0);
        transform: translate3d(-50%, 0, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.animation-slide-right50 {
    -webkit-animation-name: slide-right50;
    animation-name: slide-right50
}
@-webkit-keyframes slide-right50 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(50%, 0, 0);
        transform: translate3d(50%, 0, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
@keyframes slide-right50 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(50%, 0, 0);
        transform: translate3d(50%, 0, 0)
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0)
    }
}
.met-lightgallery .lg-backdrop, .met-lightgallery .lg-toolbar {
    background: #fff
}
.met-lightgallery .lg-actions .lg-icon {
    padding: 10px 0;
    background: rgba(255, 255, 255, 0.5)
}
.met-lightgallery .lg-actions .lg-icon:before, .met-lightgallery .lg-actions .lg-icon:after {
    content: ''
}
.met-lightgallery .lg-actions .lg-icon i {
    font-size: 40px
}
.met-lightgallery .lg-sub-html {
    background: rgba(255, 255, 255, 0.7)
}
.met-lightgallery .lg-toolbar.opacity0 {
    -webkit-transform: translateY(-50px);
    -ms-transform: translateY(-50px);
    transform: translateY(-50px)
}
.met-lightgallery .lg-outer {
    z-index: 1602;
    background: #fff
}
.met-lightgallery .lg-outer.lg-pull-caption-up.lg-thumb-open .lg-sub-html {
    bottom: 84px
}
.met-lightgallery .lg-outer .lg-thumb-outer, .met-lightgallery .lg-outer .lg-toogle-thumb {
    background: #fff
}
.met-lightgallery .lg-outer .lg-thumb-outer {
    padding: 0 10px
}
.met-lightgallery .lg-outer .lg-thumb-outer .lg-thumb-item {
    margin-bottom: 0
}
.met-lightgallery .lg-outer .lg-thumb-outer .lg-thumb-item.active, .met-lightgallery .lg-outer .lg-thumb-outer .lg-thumb-item:hover {
    border-color: #62a8ea
}
.met-lightgallery .lg-toolbar .lg-icon:hover, .met-lightgallery .lg-actions .lg-next:hover, .met-lightgallery .lg-actions .lg-prev:hover, .met-lightgallery .lg-outer .lg-toogle-thumb:hover {
    color: #2a333c
}
.met-lightgallery .lg-sub-html {
    color: #5e7387
}
.swiper-navtab.swiper-container-horizontal .swiper-scrollbar {
    width: 100%;
    height: 3px;
    left: 0;
    bottom: 0
}
.swiper-navtab.swiper-container-horizontal .swiper-scrollbar-drag {
    background: rgba(0, 0, 0, 0.2)
}
.swiper-navtab .swiper-slide {
    width: auto
}
.swiper-wrapper>* {
    box-sizing: border-box
}
.slick-loading .slick-list {
    background: none
}
.slick-arrow {
    width: auto;
    height: auto;
    z-index: 1;
    color: #fff!important;
    text-align: center
}
.slick-arrow:before, .slick-arrow:after {
    display: none
}
.slick-arrow:hover {
    color: #4E97D9!important
}
.slick-arrow.slick-next {
    right: 0
}
.slick-arrow.slick-prev {
    left: 0
}
.slick-arrow i {
    font-size: 40px;
    opacity: .5
}
.slick-slide {
    width: 100%;
    display: none
}
.slick-slide:first-child {
    display: block
}
.slick-slide img[data-lazy] {
    width: auto!important;
    max-width: 100%!important
}
.met-tools .bdsharebuttonbox {
    display: inline-block;
    vertical-align: top
}
#bdshare_weixin_qrcode_dialog {
    box-sizing: content-box
}
.no-js .grid li, .no-cssanimations .met-grid li, .met-grid li.shown, #met-imgs-slick .slick-dots li.slick-active {
    opacity: 1
}
.met-lightgallery .lg-toolbar.opacity0, .met-grid li {
    opacity: 0
}
#met-imgs-slick .slick-dots {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}
.met-editor {
    width: 100%;
    max-width: 100%;
    font-size: 16px;
    line-height: 1.8
}
.met-editor p {
    margin-bottom: .8em
}
.met-editor img {
    max-width: 100%!important;
    vertical-align: top
}
.met-editor img[src]:not(.imgloading) {
    height: auto!important
}
.met-editor .table-saw {
    overflow-x: auto
}
.met-editor .metvideobox, .met-editor video, .met-editor .metvideo, .met-editor iframe, .met-editor embed {
    max-width: 100%!important;
    margin: auto;
    display: block
}
.met-editor .lg-item-box {
    display: inline-block
}
@media (max-width:767px) {
    .met-editor {
        font-size: 14px
    }
}
.met-column-nav-ul, .met-column-nav-ul.swiper-wrapper, #met-imgs-slick .slick-dots {
    -webkit-justify-content: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}
.met-index-body:nth-of-type(odd) {
    background: #f5f5f5
}
.met-showproduct.pagetype2 .met-showproduct-navtabs.swiper-wrapper {
    -webkit-justify-content: flex-end;
    justify-content: flex-end
}
.met-page .pagination li a {
    max-width: 100%
}
.black {
    color: #000
}
.bg-black {
    background: #000!important
}
.font-size-18 {
    font-size: 18px !important
}
.font-size-14 {
    font-size: 14px !important
}
@media (min-width:1400px) {
    .container {
        max-width: 1370px
    }
}
@media (min-width:1600px) {
    .container {
        max-width: 1570px
    }
}
img {
    -o-object-fit: cover;
    object-fit: cover
}
.cover {
    overflow: hidden
}
.btn-group-xs>.btn, .btn-xs {
    font-size: 12px;
    padding: .07rem .3rem;
    border-radius: .143rem
}
[role="button"] {
    cursor: pointer
}
.oxh {
    overflow-x: hidden
}