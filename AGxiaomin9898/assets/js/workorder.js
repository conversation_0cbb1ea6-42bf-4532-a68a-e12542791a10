"use strict";
$(document).ready(function() {
    listTable();
    setTimeout(function(argument) {
        getModelList();
    }, 1500);
})
var checkList = [] || new Array();
var modelList = '';

function check1(field) {
    var checkbox = field || document.getElementsByName('checkbox[]');
    for (var i = 0; i < checkbox.length; i++) {
        if (checkbox[i].checked === false) {
            checkbox[i].checked = true;
        } else {
            checkbox[i].checked = false;
        }
    }
}

function getVals(strType = false) {
    var checkbox = document.getElementsByName('checkbox[]');
    var str = "";
    checkList = [];
    for (var i = 0; i < checkbox.length; i++) {
        if (checkbox[i].checked) {
            if (strType) {
                if (str == "") {
                    str = checkbox[i].value
                } else {
                    str = str + "|" + checkbox[i].value;
                }
            } else {
                checkList.push(checkbox[i].value);
            }
        }
    }
    if (strType) {
        return str;
    }
}

function getModelList() {
    if ("" != modelList) {
        return modelList;
    } else {
        $.ajax({
            type: 'POST',
            url: 'ajax.php?act=getModelList',
            data: {},
            dataType: 'html',
            success: function(data) {
                modelList = data.replace(/class="btn btn-info btn-sm"/g, 'class="btn btn-xs btn-info"');
            },
            error: function(data) {
                layer.msg('获取快捷回复列表请求超时，请稍后再试！');
            }
        });
        return modelList;
    }
}

function change() {
    if ($("select[name='status']").val() == 2 && $("input[name='content']").val() == '') {
        if (modelList == '') {
            getModelList();
        }
        
        var laybox = layer.open({
            type: 1,
            title: '回复工单',
            area: ['360px', '260px'],
            content: '<div class="form-group" style="padding:8px 10px 5px 10px;"><div class="input-group"><div class="input-group-addon">内容</div><textarea type="text" class="form-control" style="height:90px;" id="gongdanval" name="content"></textarea></div></div><div class="form-group" style="padding:0 10px;margin-bottom:5px;"><small>快捷模板:</small><div style="max-height:70px;overflow-y:auto;" id="model-container">' + modelList + '</div></div>',
            success: function(layero, index) {
                // 为模板按钮绑定点击事件
                $(layero).find("#model-container .btn").click(function() {
                    var id = $(this).data('id');
                    setReply(id);
                });
            },
            btn: ["确定", "取消"],
            yes: function(index, layero) {
                var text = $("#gongdanval").val();
                if(text.trim() == '') {
                    layer.msg('回复内容不能为空');
                    return false;
                }
                $("input[name='content']").val(text);
                layer.close(index);
                
                setTimeout(function(){
                    submitChange();
                }, 100);
            },
            btn2: function(index) {
                $("select[name='status']").val('批量操作');
                layer.close(index);
                return false;
            }
        });
        return false;
    } else {
        submitChange();
    }
}

function submitChange() {
    var content = $("input[name='content']").val();
    var status = $("select[name='status']").val();
    var checkbox = getVals(true);
    if (checkbox == "") {
        return layer.alert("你未勾选任何工单");
    }
    var ii = layer.load(2, {
        shade: [0.1, '#fff']
    });
    $.ajax({
        type: 'POST',
        url: 'ajax.php?act=workorder_change',
        data: {
            checkbox: checkbox,
            status: status,
            content: content
        },
        dataType: 'json',
        success: function(data) {
            layer.close(ii);
            if (data.code == 0) {
                listTable();
                layer.alert(data.msg);
            } else {
                layer.alert(data.msg);
            }
        },
        error: function(data) {
            layer.close(ii);
            layer.msg('请求超时，请稍后再试！');
            listTable();
        }
    });
    return false;
}

function escape2Html(str) {
    var arrEntities = {
        "lt": "<",
        "gt": ">",
        "nbsp": " ",
        "amp": "&",
        "quot": '"'
    };
    return str.replace(/&(lt|gt|nbsp|amp|quot);/ig, function(all, t) {
        return arrEntities[t];
    });
}

function setReply(id, obj) {
    var ii = layer.load(2, {
        shade: [0.1, '#fff']
    });
    $.ajax({
        type: 'POST',
        url: 'model.php?my=getReply',
        dataType: 'json',
        data: {
            id: id
        },
        success: function(data) {
            layer.close(ii);
            if (data.code == 0) {
                var replyText = escape2Html(data.reply);
                $("#gongdanval").val(replyText);
                layer.msg('已插入模板内容', {icon: 1, time: 1000});
            } else {
                layer.msg(data.msg, {icon: 2});
            }
        },
        error: function(data) {
            layer.close(ii);
            layer.msg('服务器错误', {icon: 2});
            return false;
        }
    });
}

function listTable(query) {
    var url = window.document.location.href.toString();
    var queryString = url.split("?")[1];
    query = query || queryString;
    if (query == 'start' || query == undefined) {
        query = '';
        history.replaceState({}, null, './workorder.php');
    } else if (query != undefined) {
        history.replaceState({}, null, './workorder.php?' + query);
    }
    layer.closeAll();
    var ii = layer.load(2, {
        shade: [0.1, '#fff']
    });
    $.ajax({
        type: 'GET',
        url: 'workorder-table.php?' + query,
        dataType: 'html',
        cache: false,
        success: function(data) {
            layer.close(ii);
            $("#listTable").html(data)
        },
        error: function(data) {
            layer.msg('服务器错误');
            return false;
        }
    });
}

function orderItem(id) {
    layer.closeAll();
    var title = 'ID:' + id + ' 工单详情';
    var url = './workorder-item.php?my=view&id=' + id;
    var area = [$(window).width() > 800 ? '800px' : '90%', $(window).height() > 600 ? '600px' : '90%'];
    var options = {
        type: 2,
        title: title,
        shadeClose: true,
        shade: false,
        maxmin: true,
        moveOut: true,
        area: area,
        content: url,
        zIndex: layer.zIndex,
        success: function(layero, index) {
            var that = this;
            $(layero).data("callback", that.callback);
            layer.setTop(layero);
            if ($(layero).height() > $(window).height()) {
                layer.style(index, {
                    top: 0,
                    height: $(window).height()
                });
            }
        },
        cancel: function() {
            listTable()
        }
    }
    if ($(window).width() < 480 || (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream && top.$("body").size() > 0)) {
        options.area = [top.$("body").width() + "px", top.$("body").height() + "px"];
        options.offset = [top.$("body").scrollTop() + "px", "0px"];
    }
    layer.open(options);
}

function delworkorder(id) {
    var ii = layer.load(2, {
        shade: [0.1, '#fff']
    });
    $.ajax({
        type: 'GET',
        url: '?my=del&id=' + id,
        dataType: 'json',
        cache: false,
        success: function(data) {
            layer.close(ii);
            if (data.code == 0) {
                layer.msg(data.msg);
                $("#tr_" + id).fadeOut();
            } else {
                layer.alert(data.msg)
            }
        },
        error: function(data) {
            layer.close(ii);
            return layer.msg('服务器错误');
        }
    });
}

function setActive(id, status) {
    var ii = layer.load(2, {
        shade: [0.1, '#fff']
    });
    $.ajax({
        type: 'GET',
        url: '?my=setActive&id=' + id + '&status=' + status,
        dataType: 'json',
        cache: false,
        success: function(data) {
            layer.close(ii);
            if (data.code == 0) {
                layer.msg(data.msg);
                listTable();
            } else {
                layer.alert(data.msg)
            }
        },
        error: function(data) {
            layer.close(ii);
            return layer.msg('服务器错误');
        }
    });
}