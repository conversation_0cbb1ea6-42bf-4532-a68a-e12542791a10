<?php

/**
 * name   后台请求接口
 * author  神话
 */

use core\Db;
use core\Ems;
use core\Sms;

define('AJAX_VERSION', '2.8.4');

$goodLibMethods = ['getGoodsList', 'getGoodsParam', 'djOrder', 'operation', 'setToolSort', 'setClassSort', 'setArticleSort', 'getGoodsclass', 'getkxyGoodslist', 'getkyxgoods', 'kyxmoney', 'kyxmoney', 'kyxmoney', 'kyxmoney', 'kyxlogin', 'setResult'];
if (in_array($_GET['act'], $goodLibMethods)) {
    $loadGoodLib = true;
}

include "../includes/common.php";
checkLogin();

$act = isset($_GET['act']) ? daddslashes(input('get.act')) : null;

@header('Content-Type: application/json; charset=UTF-8');

switch ($act) {
    case 'getData':
        $thtime  = date("Y-m-d") . ' 00:00:00';
        $thtime2 = date("Y-m-d", strtotime("-1 day", time())) . ' 00:00:00';
        $count1  = 0;
        $count2  = 0;
        $count3  = 0;
        $count4  = 0;
        $count5  = 0;
        $count15 = 0;
        $countzs = $DB->count("SELECT COUNT(*) AS total_rows FROM pre_tools");
        $count1  = $DB->count("SELECT count(id) from pre_orders");
        $sjsp    = $DB->count("SELECT count(*) FROM pre_tools WHERE `active` = 1");
        $xjsp    = $DB->count("SELECT count(*) FROM pre_tools WHERE `active` = 0");
        $wpts    = $DB->count("SELECT count(*) FROM pre_pro_link WHERE `status` = '处理中'");
        $yljb    = $DB->count("SELECT count(*) FROM pre_pro_report WHERE `status` = '处理中'");
        $countghs = $DB->count("SELECT COUNT(*) AS countghs FROM `pre_master`");
        $count2  = $DB->count("SELECT count(id) from pre_orders where status=1");
        $count3  = $DB->count("SELECT count(id) from pre_orders where status=0");
        $count15 = $DB->count("SELECT count(id) from pre_orders where status=3");
        $count_ghsh = $DB->count("SELECT COUNT(*) FROM `pre_tools` WHERE `condition` = 0");
        $count_ghsp = $DB->count("SELECT COUNT(*) FROM `pre_tools` WHERE `zid` > 1 AND `condition` = 1");
        $count_clgd = $DB->count("SELECT COUNT(*) FROM `pre_workorder` WHERE `status` = 0");
        $count_kcgj = $DB->count("SELECT COUNT(*) FROM `pre_tools` WHERE `condition` = 1 AND `active` = 1 AND `close` = 0 AND `stock`< 2");
        $strtotime = strtotime($conf['build']); //获取开始统计的日期的时间戳
        $count6 = $DB->count("SELECT count(zid) FROM pre_site WHERE power > 0");
        $daili_site  = $DB->count("SELECT count(zid) from pre_site where `power`>0 and addtime>='$thtime'");
        $daili_user  = $DB->count("SELECT count(zid) from pre_site where `power`=0 and addtime>='$thtime'");
        $daili_money = 0;
        if ($conf['data_type'] == 2) {
            $daili_money = $DB->count("SELECT sum(money) from pre_site where 1");
        }
        $count8 = $DB->count("SELECT sum(point) from pre_points where `action`='提成' and addtime>='$thtime'");
        $count22 = $DB->count("SELECT sum(point) from pre_master_points where `action`='提成' and addtime>='$thtime'");
        $daili_point = 0;
        $daili_point = 0;
        if ($conf['data_type'] == 2) {
            $daili_point = $DB->count("SELECT sum(point) from pre_points where action='提成'");
        }
        $gonghuo_point = 0;
        if ($conf['data_type'] == 2) {
            $gonghuo_point = $DB->count("SELECT sum(point) from pre_master_points where action='提成'");
        }
        $countgh  = round($DB->count("SELECT sum(realmoney) from pre_master_tixian WHERE 1"), 2);
        $countdl  = round($DB->count("SELECT sum(realmoney) from `pre_tixian` WHERE 1"), 2);
        $daili_buy = 0;
        //$daili_buy=$DB->count("SELECT sum(point) from pre_points where action='消费'");

        $count11 = $DB->count("SELECT sum(realmoney) FROM `pre_tixian` WHERE `status` = 0");

        $clzorder = $DB->count("SELECT count(*) from pre_orders where status='2'");
        $fz_xf    = $DB->count("SELECT sum(point) from pre_points where action='消费' and addtime>='$thtime'");
        $fz_cz    = $DB->count("SELECT sum(point) from pre_points where action='充值' and addtime>='$thtime'");

        $result = array("code" => 0, "yxts" => $yxts, "count1" => round($count1, 0), "count2" => round($count2, 0), "count3" => round($count3, 0), "count5" => round($count5, 2), "count6" => round($count6, 0), "count7" => intval($daili_site), "daili_user" => intval($daili_user), "count8" => round($count8, 2), "daili_point" => round($daili_point, 2), "daili_buy" => round($daili_buy, 2), "daili_money" => round($daili_money, 2), "count11" => round($count11, 2), "count14" => round($count14, 2), "count15" => round($count15, 0),"count21" => round($count21, 2), "count22" => round($count22, 2),"gonghuo_point" => round($gonghuo_point, 2), "countgh" => round($countgh, 2), "countdl" => round($countdl, 2), 'clzorder' => round($clzorder, 0),"countzs" => round($countzs, 0),"count_ghsh" => round($count_ghsh, 0),"count_ghsp" => round($count_ghsp, 0),"countghs" => round($countghs, 0),"count_clgd" => round($count_clgd, 0),"sjsp" => round($sjsp, 0),"xjsp" => round($xjsp, 0),"wpts" => round($wpts, 0),"yljb" => round($yljb, 0),"count_kcgj" => round($count_kcgj, 2),  'fz_xf' => round($fz_xf, 2), 'fz_cz' => round($fz_cz, 2), 'lastdata' => getLastData(), 'todaydata' => getTodayData());
        exit(json_encode($result));
        break;
    case 'getPointData':
        $type = intval(input('get.type'));
        $data = getDatePoint($type);
        if (is_array($data)) {
            $result = array("code" => 0, "msg" => 'succ', "data" => $data);
        } else {
            $result = array("code" => 0, "msg" => '最近销售数据获取失败', "data" => $data);
        }
        exit(json_encode($result));
        break;
    case 'checkupdate':
        $data = checkUpdate(1, 0);
        unset($data['file']);
        unset($data['uplog']);
        //$update_msg = $checkupdate['msg'];
        //$notice_msg = $checkupdate['notice'];
        if ($webConfig['test'] == true) {
            $data['notice'] = '';
        }
        $result = array("code" => 0, "msg" => 'succ', "data" => $data);
        exit(json_encode($result));
        break;
    case 'edit_cj':
        $id = $_POST['id'];
        if (!$id) {
            exit('{"code":-1,"msg":"请输入完整！"}');
        }
        $sql = $DB->getRow("SELECT * FROM pre_gift where id='{$id}'");
        if ($sql) {
            $cid = $DB->getColumn("select cid from pre_tools where tid='{$sql['tid']}' limit 1");
            exit('{"code":0,"msg":"查询成功","id":"' . $id . '","name":"' . $sql['name'] . '","cid":"' . $cid . '","tid":"' . $sql['tid'] . '","rate":"' . $sql['rate'] . '"}');
        } else {
            exit('{"code":1,"msg":"查询失败，' . $DB->error() . '"}');
        }
        break;
    case 'edit_cj_ok':
        $id = $_POST['id'];
        $name = $_POST['name'];
        $tid = $_POST['tid'];
        $rate = $_POST['rate'];
        if (!$id) {
            exit('{"code":-1,"msg":"请输入完整！"}');
        }
        $sql = $DB->exec("UPDATE pre_gift set name='{$name}',tid='{$tid}',rate='{$rate}' where id='{$id}'");
        if ($sql !== false) {
            exit('{"code":0,"msg":"修改成功"}');
        } else {
            exit('{"code":1,"msg":"修改失败，' . $DB->error() . '"}');
        }
        break;
    case 'del_member':
        $id = $_POST['id'];
        if (!$id) {
            exit('{"code":-1,"msg":"请输入完整！"}');
        }
        $sql = $DB->exec("DELETE FROM pre_gift WHERE id='{$id}'");
        if ($sql !== false) {
            exit('{"code":0,"msg":"删除成功"}');
        } else {
            exit('{"code":1,"msg":"删除失败，' . $DB->error() . '"}');
        }
        break;
    case 'add_member':
        $name = $_POST['name'];
        $tid = $_POST['tid'];
        $rate = str_replace('%', '', $_POST['rate']);
        if (!$name || !$tid || !$rate) {
            exit('{"code":-1,"msg":"请输入完整！"}');
        }
        $sql = $DB->exec("INSERT INTO `pre_gift`(`name`,`tid`,`rate`,`ok`) VALUES ('{$name}','{$tid}',{$rate},0)");
        if ($sql) {
            exit('{"code":0,"msg":"添加成功"}');
        } else {
            exit('{"code":1,"msg":"添加失败，' . $DB->error() . '"}');
        }
        break;
    case 'getWorkData':
        $count = $DB->count("SELECT count(*) from `pre_workorder` where status='0'");
        if ($conf['work_notice_speed'] < 10) {
            $conf['work_notice_speed'] = 10;
        }

        $kucunlist        = [];
        $notify_stock_num = intval($conf['notify_stock_num']) > 0 ? $conf['notify_stock_num'] : 5;
        if ($conf['notify_stock_open'] == 1) {
            $kucunlist = Db::name('tools')->where([
                'active'     => 1,
                'stock'      => ['<=', $notify_stock_num],
                'stock_time' => ['>=', time() - 1200],
            ])->field('name,tid,stock,active')->select();
        }

        $online = $DB->count("SELECT count(*) from `pre_session`");
        $data   = array(
            'dcl'              => $count,
            'open'             => $conf['work_notice'] > 0 ? '1' : '0',
            'speed'            => $conf['work_notice_speed'],
            'new_reply'        => $conf['work_new_reply'],
            'online'           => $online,
            'kucunlist'        => $kucunlist,
            'notify_stock_num' => $notify_stock_num,
        );
        saveSetting('work_new_reply', '0');
        $CACHE->clear();
        $result = array("code" => 0, "msg" => 'succ', "data" => $data);
        exit(json_encode($result));
        break;
    case 'getMasterShopNums':

        $check_nums = Db::name('tools')->where([
            'zid'       => ['>', 1],
            'condition' => 0,
            'is_curl'   => ['!=', 2],
        ])->count('tid');

        $master_check_nums = Db::name('tools')->where([
            'zid'       => ['>', 1],
            'condition' => 0,
            'is_curl'   => ['!=', 2],
        ])->count('tid');

        $notify_stock_num = intval($conf['notify_stock_num']) > 0 ? intval($conf['notify_stock_num']) : 0;

        $stock_nums = Db::name('tools')->where([
            'active'     => 1,
            'is_curl'    => ['!=', 2],
            'condition'  => 1,
            'close'      => 0,
            'stock'      => ['<=', $notify_stock_num],
            'stock_time' => ['>=', time() - 86400],
        ])->count('tid');

        $work_nums = Db::name('workorder')->where([
            'status' => 0,
        ])->count('id');

        $order_nums = Db::name('orders')->where([
            'status' => 0,
        ])->count('id');

        $master_tixian_nums = Db::name('master_tixian')->where([
            'status' => 0,
        ])->count('id');

        $tixian_nums = Db::name('tixian')->where([
            'status' => 0,
        ])->count('id');
        
        $pro_link_nums = $DB->count("SELECT count(*) FROM pre_pro_link WHERE status = '待处理' OR status = '处理中'");

        $pro_report_nums = $DB->count("SELECT count(*) FROM pre_pro_report WHERE status = '待处理' OR status = '处理中'");
        
        $pro_complaint_nums = $DB->count("SELECT count(*) FROM pre_pro_complaint WHERE status = '待处理' OR status = '处理中'");

        $result = array("code" => 0, "msg" => 'succ', "data" => [
            'check_nums'         => $check_nums,
            'stock_nums'         => $stock_nums,
            'work_nums'          => $work_nums,
            'order_nums'         => $order_nums,
            'master_check_nums'  => $master_check_nums,
            'master_tixian_nums' => $master_tixian_nums,
            'tixian_nums'        => $tixian_nums,
            'pro_link_nums'      => $pro_link_nums,
            'pro_report_nums'    => $pro_report_nums,
            'pro_complaint_nums'    => $pro_complaint_nums,
        ]);
        exit(json_encode($result));
        break;

    case 'getArticleList':
        $arr  = getGfArticle();
        $data = '';
        if (is_array($arr)) {
            if ($arr['code'] == 1) {
                foreach ($arr['data'] as $key => $item) {
                    if (!$item['url']) {
                        $item['url'] = 'javascript:;';
                    }
                    $data .= '
            <li class="swiper-slide slide">
                <a class="read-notice" href="' . $item['url'] . '" data-jump="' . $item['jump'] . '" data-content="' . $item['content'] . '" data-title="' . $item['title'] . '">' . $item['title'] . '</a>
            </li>';
                }
            } else {
                $data = ' <li class="swiper-slide slide">
                <a class="" href="javascript:;" data-jump="" data-content="" data-title="">' . $arr['msg'] . '</a>
            </li>';
            }
        } else {
            $data = ' <li class="swiper-slide slide">
                <a class="" href="javascript:;" data-jump="" data-content="" data-title="">获取公告结果异常，请稍后再试</a>
            </li>';
        }
        $result = array("code" => 0, "msg" => 'succ', "data" => $data);
        exit(json_encode($result));
        break;
    case 'qdcount':
        $day     = date("Y-m-d");
        $lastday = date("Y-m-d", strtotime("-1 day"));
        $count1  = $DB->count("SELECT count(*) FROM pre_qiandao WHERE date='$day'");
        $count2  = $DB->count("SELECT count(*) FROM pre_qiandao WHERE date='$lastday'");
        $count3  = $DB->count("SELECT count(*) FROM pre_qiandao");
        $count4  = $DB->count("SELECT sum(reward) FROM pre_qiandao WHERE date='$day'");
        $count5  = $DB->count("SELECT sum(reward) FROM pre_qiandao WHERE date='$lastday'");
        $count6  = $DB->count("SELECT sum(reward) FROM pre_qiandao");
        $result  = array("count1" => $count1, "count2" => $count2, "count3" => $count3, "count4" => round($count4, 2), "count5" => round($count5, 2), "count6" => round($count6, 2));
        exit(json_encode($result));
        break;
    case 'sendCode': //获取验证码
        $tel = input('post.tel');
        if (preg_match('/^1[0-9]{10}/', $conf['adm_tel']) && $tel != $conf['adm_tel']) {
            $tel = $conf['adm_tel'];
        }

        if (empty($tel)) {
            $result = array("code" => -1, "msg" => "手机号不能为空！");
        } elseif (!preg_match('/^1[0-9]{10}/', $tel)) {
            $result = array("code" => -1, "msg" => "手机号格式不正确！");
        } else {
            if (empty($conf['adm_tel'])) {
                $type = 1;
            } else {
                $type = 2;
            }
            $result = sendCode($tel, $type);
        }

        exit(json_encode($result));
        break;
    case 'tool':
        $tid  = intval(input('post.tid'));
        $rows = $DB->get_row("SELECT * from pre_tools where tid='$tid' limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"商品ID不存在"}');
        }

        exit('{"code":0,"name":"' . $rows['name'] . '"}');
        break;
    case 'addClass':
        checkAuthority('class');
        $type  = intval(input('post.type'));
        $upcid = intval(input('post.upcid'));
        $name  = strFilter(input('name', 1));
        if ($name == null) {
            exit('{"code":-1,"msg":"分类名不能为空"}');
        }

        if ($type == 2) {
            $rows = $DB->get_row("SELECT * from pre_class where cid='" . $upcid . "' limit 1");
            if (!$rows) {
                exit('{"code":-1,"msg":"当前的上级分类不存在"}');
            }

            $sql = "insert into `pre_class` (`upcid`,`name`,`active`) values ('" . $upcid . "','" . $name . "','1')";
        } else {
            $rows = $DB->get_row("SELECT * from pre_class where name='$name' limit 1");
            if ($rows) {
                exit('{"code":-1,"msg":"当前分类名称已存在"}');
            }

            $sql = "insert into `pre_class` (`name`,`active`) values ('" . $name . "','1')";
        }

        if ($cid = $DB->insert($sql)) {
            $DB->query("UPDATE `pre_class` SET `sort`='$cid' WHERE `cid`='{$cid}'");
            exit('{"code":0,"msg":"添加分类成功！"}');
        } else {
            exit('{"code":-1,"msg":"添加分类失败！' . $DB->error() . '"}');
        }

        break;
    case 'editClass':
        checkAuthority('class');
        $cid  = intval(input('get.cid', 1));
        $rows = $DB->get_row("SELECT * from pre_class where cid='$cid' limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"分类不存在"}');
        }

        $name = strFilter(input('name', 1));
        $color = strFilter(input('color', '#000000'));
        if ($name == null) {
            exit('{"code":-1,"msg":"分类名不能为空"}');
        }

        if ($DB->query("UPDATE pre_class set name='$name',color='{$color}' where cid='{$cid}'")) {
            exit('{"code":0,"msg":"修改分类成功！"}');
        } else {
            exit('{"code":-1,"msg":"修改分类失败！' . $DB->error() . '"}');
        }

        break;
    case 'delClass':
        checkAuthority('class');
        $cid = intval(input('get.cid', 1));
        $DB->query("DELETE FROM pre_tools WHERE cid='{$cid}'");
        $sql = "DELETE FROM pre_class WHERE cid='$cid'";
        if ($DB->query($sql)) {
            exit('{"code":0,"msg":"删除分类成功！"}');
        } else {
            exit('{"code":-1,"msg":"删除分类失败！' . $DB->error() . '"}');
        }

        break;
    case 'editClassAll':
        checkAuthority('class');
        foreach ($_POST as $k => $v) {
            $v = strFilter($v);
            if (substr($k, 0, 4) == 'name') {
                $cid = intval(substr($k, 4));
                $DB->query("UPDATE pre_class set name='$v' where cid='{$cid}'");
            }
        }
        exit('{"code":0,"msg":"修改分类成功！"}');
        break;
    case 'editClassImages':
        checkAuthority('class');
        foreach ($_POST as $k => $v) {
            if (substr($k, 0, 3) == 'img') {
                $cid = intval(substr($k, 3));
                $DB->query("UPDATE pre_class set shopimg='$v' where cid='{$cid}'");
            }
        }
        exit('{"code":0,"msg":"修改分类成功！"}');
        break;
    case 'getClassImage':
        checkAuthority('class');
        $cid  = intval(input('get.cid', 1));
        $rows = $DB->get_row("SELECT shopimg from pre_tools where cid='$cid' and shopimg is not null limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"分类不存在"}');
        }

        exit('{"code":0,"msg":"succ","url":"' . $rows['shopimg'] . '"}');
        break;
    case 'uploadimg':
        checkAuthority('shops');
        $tid = intval(input('post.tid'));
        if (input('post.do') == 'upload') {
            $type   = input('post.type', 1);
            $type   = $type ? $type : 'shop';
            $oldurl = '';
            if ($type == 'shopimg') {
                $tool = $DB->get_row("SELECT * from shua_tools where tid='" . $tid . "' limit 1");
                if ($tool['shopimg'] && file_exists(ROOT . $tool['shopimg'])) {
                    unlink(ROOT . $tool['shopimg']); //删除历史文件
                } else {
                    $oldurl = $tool['shopimg'];
                }
            }
            $filename = $type . '_' . substr(MD5(rand(1, 999999)), 0, 28) . '.png';
            $result   = uploadFile('file', $filename, $type, $oldurl);
            if ($result['code'] == 0) {
                exit('{"code":0,"msg":"succ","url":"' . $result['path'] . '"}');
            } else {
                exit('{"code":-1,"msg":"上传失败，' . $result['msg'] . '"}');
            }
        }
        exit('{"code":-1,"msg":"null"}');
        break;
    case 'getTool':
        checkAuthority('shops');
        $tid  = intval(input('get.tid'));
        $rows = $DB->get_row("SELECT * from pre_tools where tid='$tid' limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"商品不存在"}');
        }

        $crow = $DB->get_row("SELECT * from pre_class where cid='" . $rows['cid'] . "' limit 1");
        if ($crow['upcid'] > 0) {
            $rows['link'] = 'http://' . $_SERVER['HTTP_HOST'] . '/?cid=' . $crow['upcid'] . '&sub_cid=' . $rows['cid'] . '&tid=' . $rows['tid'];
        } else {
            $rows['link'] = 'http://' . $_SERVER['HTTP_HOST'] . '/?cid=' . $rows['cid'] . '&tid=' . $rows['tid'];
        }

        $result = array("code" => 0, "msg" => "succ", "data" => $rows);
        exit(json_encode($result));
        break;
    case 'getPrice':
        checkAuthority('shops');
        $tid  = intval(input('get.tid'));
        $rows = $DB->get_row("SELECT * from pre_tools where tid='$tid' limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"商品不存在"}');
        }

        if ($_SESSION['priceselect']) {
            $priceselect = $_SESSION['priceselect'];
        } else {
            $rs          = $DB->query("SELECT * FROM pre_price order by id asc");
            $priceselect = '<option value="0">不使用加价模板</option>';
            while ($res = $DB->fetch($rs)) {
                $kind = $res['kind'] == 1 ? '元' : '%';
                $priceselect .= '<option value="' . $res['id'] . '" kind="' . $res['kind'] . '" p_2="' . $res['p_2'] . '" p_1="' . $res['p_1'] . '" p_0="' . $res['p_0'] . '">' . $res['name'] . '(+' . $res['p_2'] . $kind . '|+' . $res['p_1'] . $kind . '|+' . $res['p_0'] . $kind . ')</option>';
            }
        }
        $data = '<div class="form-group"><div class="input-group"><div class="input-group-addon">成本价格</div><input type="text" id="edit_price1_' . $rows['tid'] . '" value="' . ($rows['price1'] ? $rows['price1'] : '0.00') . '" class="form-control" required onkeyup="changePrice(' . $rows['tid'] . ')"/></div></div>
    <div class="form-group"><div class="input-group"><div class="input-group-addon">商品名称</div><input type="text" id="edit_name_' . $rows['tid'] . '" value="' . $rows['name'] . '" class="form-control" required/></div></div>
    <div class="form-group"><div class="input-group"><div class="input-group-addon">对接数量</div><input type="text" id="edit_value_' . $rows['tid'] . '" value="' . $rows['value'] . '" class="form-control" required/></div></div>
    <div class="form-group"><div class="input-group"><div class="input-group-addon">加价模板</div><select class="form-control" id="edit_prid_' . $rows['tid'] . '"  default="' . $rows['prid'] . '" onchange="changePrice(' . $rows['tid'] . ')">' . $priceselect . '</select></div></div>
<table class="table table-striped table-bordered table-condensed">
<tbody>
<tr align="center"><td>销售价格</td><td>专业版价格</td><td>旗舰版价格</td></tr>
<tr>
<td><input type="text" id="price_s" value="' . $rows['price'] . '" class="form-control input-sm" disabled/></td>
<td><input type="text" id="cost_s" value="' . $rows['cost'] . '" class="form-control input-sm" disabled/></td>
<td><input type="text" id="cost2_s" value="' . $rows['cost2'] . '" class="form-control input-sm" disabled/></td>
</tr>
</table>
    <input type="submit" id="save" onclick="editPrice(' . $tid . ')" class="btn btn-primary btn-block" value="保存">
    <script>$("#prid").val(' . $rows['prid'] . ');</script>';
        $result = array("code" => 0, "msg" => "succ", "data" => $data);
        exit(json_encode($result));
        break;
    case 'editPrice':
        checkAuthority('shops');
        $tid  = intval(input('post.tid'));
        $rows = $DB->get_row("SELECT * from pre_tools where tid='$tid' limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"商品不存在"}');
        }

        $prid   = intval(input('post.prid'));
        $name   = input('post.name', 1);
        $value  = intval(input('post.value'));
        $price1 = input('price1');
        $price  = input('price_s');
        $cost   = input('cost_s');
        $cost2  = input('cost2_s');
        if ($DB->query("UPDATE `pre_tools` SET `name`='$name',`value`='$value',`price1`='$price1',`price`='$price',`cost`='$cost',`cost2`='$cost2',`prid`='$prid' WHERE `tid`='{$tid}'")) {
            exit('{"code":0,"msg":"succ"}');
        } else {
            exit('{"code":-1,"msg":"修改商品失败！' . $DB->error() . '"}');
        }

        break;
    case 'getAllPrice':
        checkAuthority('shops');
        if ($_SESSION['priceselect']) {
            $priceselect = $_SESSION['priceselect'];
        } else {
            $rs          = $DB->query("SELECT * FROM pre_price order by id asc");
            $priceselect = '<option value="0">不使用加价模板</option>';
            while ($res = $DB->fetch($rs)) {
                $kind = $res['kind'] == 1 ? '元' : '倍';
                $priceselect .= '<option value="' . $res['id'] . '" kind="' . $res['kind'] . '" p_2="' . $res['p_2'] . '" p_1="' . $res['p_1'] . '" p_0="' . $res['p_0'] . '" >' . $res['name'] . '(' . $res['p_2'] . $kind . '|' . $res['p_1'] . $kind . '|' . $res['p_0'] . $kind . ')</option>';
            }
        }
        $data = '<div class="form-group"><div class="input-group"><select class="form-control" name="prid_n">' . $priceselect . '</select></div></div>
    <input type="submit" id="save" onclick="editAllPrice()" class="btn btn-primary btn-block" value="保存">';
        $result = array("code" => 0, "msg" => "succ", "data" => $data);
        exit(json_encode($result));
        break;

    case 'getPridPrice': //加价模板加价
        checkAuthority('shops');
        $prid   = intval(input('post.prid'));
        $price1 = floatval(input('post.price1'));
        $row    = $DB->get_row("SELECT * from pre_price where id='{$prid}' limit 1");

        if ($row) {
            if ($price1 <= 0) {
                $result = array("code" => -1, "msg" => "成本价不正确！", "price1" => $price1);
                exit(json_encode($result));
            }
            $kind          = $row['kind'];
            $data['price'] = 0;
            $data['cost']  = 0;
            $data['cost2'] = 0;
            if ($row['p_0'] > 0) {
                $data['price'] = priceFormat($kind == 2 ? $price1 + $price1 * $row['p_0'] / 100 : $row['p_0'] + $price1);
            }

            if ($row['p_1'] > 0) {
                $data['cost'] = priceFormat($kind == 2 ? $price1 + $price1 * $row['p_1'] / 100 : $row['p_1'] + $price1);
            }

            if ($row['p_2'] > 0) {
                $data['cost2'] = priceFormat($kind == 2 ? $price1 + $price1 * $row['p_2'] / 100 : $row['p_2'] + $price1);
            }

            $result = array("code" => 0, "msg" => "succ", "data" => $data, "price1" => $price1);
        } else {
            $result = array("code" => -1, "msg" => "该加价模板不存在！", "prid" => $prid);
        }
        exit(json_encode($result));
        break;
    case 'editAllPrice':
        checkAuthority('shops');
        $prid     = intval(input('post.prid'));
        $checkbox = input('checkbox');
        $i        = 0;
        foreach ($checkbox as $tid) {
            $DB->query("UPDATE `pre_tools` SET prid={$prid},`cost`='0',`cost2`='0' where tid='$tid' limit 1");
            $i++;
        }
        exit('{"code":0,"msg":"成功改变' . $i . '个商品"}');
        break;
    case 'shop_move':
        checkAuthority('shops');
        $cid = intval(input('post.cid'));
        if (!$cid) {
            exit('{"code":-1,"msg":"请选择分类"}');
        }

        $checkbox = input('checkbox');
        $i        = 0;
        foreach ($checkbox as $tid) {
            $DB->query("UPDATE `pre_tools` SET cid='$cid' where tid='$tid' limit 1");
            $i++;
        }
        exit('{"code":0,"msg":"成功移动' . $i . '个商品"}');
        break;
    case 'shop_operation':
        checkAuthority('shops');
        $aid = input('aid');

        $tids = input('post.checkbox', 1);
        if (is_string($tids)) {
            $tids = explode(',', $tids);
        }
        !is_array($tids) && $tids = [];

        $i    = 0;
        $succ = 0;
        switch ($aid) {
            case 1:
                $rep0 = input('rep0', 1);
                $rep1 = input('rep1', 1);
                if (!$rep0) {
                    exit('{"code":-1,"msg":"被替换的内容不能为空"}');
                }

                if (count($tids) == 0) {
                    exit('{"code":-1,"msg":"操作的商品列表不能为空"}');
                }
                foreach ($tids as $tid) {
                    $succ += $DB->exec("UPDATE `pre_tools` SET `name` = REPLACE(`name`, '{$rep0}', '{$rep1}')  where tid='$tid' limit 1");
                    $i++;
                }
                break;
            case 2:
                $rep0 = input('rep0', 1);
                $rep1 = input('rep1', 1);
                if (!$rep0) {
                    exit('{"code":-1,"msg":"被替换的内容不能为空"}');
                }

                $succ = $DB->exec("UPDATE `pre_tools` SET `name` = REPLACE(`name`, '{$rep0}', '{$rep1}')");
                break;
            default:
                exit('{"code":-1,"msg":"操作类型不存在"}');
                break;
        }

        exit('{"code":0,"msg":"成功影响修改' . $succ . '个商品"}');
        break;
    case 'site_operation':
        checkAuthority('site');
        $aid  = input('aid');
        $zids = input('post.checkbox', 1);
        if (is_string($zids)) {
            $zids = explode(',', $zids);
        }
        !is_array($zids) && $zids = [];

        $i    = 0;
        $succ = 0;
        switch ($aid) {
            case 1:
                $rep0 = input('rep0', 1);
                $rep1 = input('rep1', 1);
                if (!$rep0) {
                    exit('{"code":-1,"msg":"被替换的名称不能为空"}');
                }

                if (count($zids) == 0) {
                    exit('{"code":-1,"msg":"操作的分站列表不能为空"}');
                }
                foreach ($zids as $zid) {
                    $succ += $DB->exec("UPDATE `pre_site` SET `sitename` = REPLACE(`sitename`, '{$rep0}', '{$rep1}')  where zid='$zid' limit 1");
                    $i++;
                }
                exit('{"code":0,"msg":"成功替换' . $succ . '个分站站点名称"}');
                break;
            case 2:
                $rep0 = input('rep0', 1);
                $rep1 = input('rep1', 1);
                if (!$rep0) {
                    exit('{"code":-1,"msg":"被替换的域名部分不能为空"}');
                }

                if (count($zids) == 0) {
                    exit('{"code":-1,"msg":"操作的分站列表不能为空"}');
                }
                foreach ($zids as $zid) {
                    $succ += $DB->exec("UPDATE `pre_site` SET `siteurl` = REPLACE(`siteurl`, '{$rep0}', '{$rep1}'), `siteurl2` = REPLACE(`siteurl2`, '{$rep0}', '{$rep1}')  where zid='$zid' limit 1");
                    $i++;
                }
                exit('{"code":0,"msg":"成功替换' . $succ . '个分站域名"}');
                break;
            case 3:
                $rep0 = input('rep0', 1);
                $rep1 = input('rep1', 1);
                if (!$rep0) {
                    exit('{"code":-1,"msg":"被替换的域名部分不能为空"}');
                }
                $succ = $DB->exec("UPDATE `pre_site` SET `siteurl` = REPLACE(`siteurl`, '{$rep0}', '{$rep1}'), `siteurl2` = REPLACE(`siteurl2`, '{$rep0}', '{$rep1}') WHERE 1");
                exit('{"code":0,"msg":"成功替换' . $succ . '个分站域名"}');
                break;
            case 4:
                if (count($zids) == 0) {
                    exit('{"code":-1,"msg":"操作的分站列表不能为空"}');
                }

                foreach ($zids as $zid) {
                    $succ += $DB->exec("UPDATE `pre_site` SET `mid`=0,`iprice` = NULL where zid='$zid' limit 1");
                    $i++;
                }
                break;
            case 5:
                $succ = intval($DB->exec("UPDATE `pre_site` SET `mid`=0,`iprice` = NULL WHERE 1"));
                break;
            default:
                exit('{"code":-1,"msg":"操作类型不存在"}');
                break;
        }

        exit('{"code":0,"msg":"成功影响修改' . $succ . '个分站"}');
        break;
    case 'shop_change':
        checkAuthority('shops');
        $aid       = (int) input('post.aid');
        $checkbox  = input('post.checkbox', true);
        $result    = input('post.result');
        $check_val = input('post.check_val');
        $hide_val  = input('post.hide_val');
        $pay_val   = input('post.pay_val');
        if ($pay_val === 'null') {
            $pay_val = "";
        }

        if ($aid == 20) {
            $pay['alipay'] = intval(input('pay_alipay'));
            $pay['wxpay']  = intval(input('pay_wxpay'));
            $pay['qqpay']  = intval(input('pay_qqpay'));
            $pay['rmb']    = intval(input('pay_rmb'));
        } elseif ($aid == 21) {
            $shopimg = input('post.shopimg');
        } elseif ($aid == 22) {
            $input  = input('post.input');
            $inputs = input('post.inputs');
        }

        $prid = intval(input('prid'));
        $i    = 0;
        $ok   = 0;
        if ($aid == 1) {
            $type = "显示";
        } elseif ($aid == 2) {
            $type = "隐藏";
        } elseif ($aid == 3) {
            $type = "上架";
        } elseif ($aid == 4) {
            $type = "下架";
        } elseif ($aid == 5) {
            $type = "删除";
        } elseif ($aid == 6) {
            $type = "复制商品";
        } elseif ($aid == 7) {
            $type = "设置Q钻类型";
        } elseif ($aid == 8) {
            $type = "开启计入排行";
        } elseif ($aid == 9) {
            $type = "取消计入排行";
        } elseif ($aid == 12) {
            $type = "设置处理信息";
        } elseif ($aid == 13) {
            $type = "设置下架原因";
        } elseif ($aid == 16) {
            $type = "设置加价模板";
        } elseif ($aid == 17) {
            $type = "显示并上架";
        } elseif ($aid == 18) {
            $type = "隐藏并下架";
        } elseif ($aid == 19) {
            $type = "设置支付名称";
            if (empty($pay_val)) {
                $type = "清空支付名称";
            }
        } elseif ($aid == 20) {
            $type = "设置支付方式";
        } elseif ($aid == 21) {
            $type = "设置商品图片";
        } elseif ($aid == 22) {
            $type = "设置输入框标题";
        } elseif ($aid == 23) {
            $desc = input('post.desc', 0);
            $type = "设置商品简介";
        } elseif ($aid == 24) {
            $alert = input('post.alert', 0);
            $type  = "设置提示内容";
        } elseif ($aid == 25) {
            $type = "开启邮件通知";
        } elseif ($aid == 26) {
            $type = "关闭邮件通知";
        } elseif ($aid == 29) {
            $type = "开启库存控制";
        } elseif ($aid == 30) {
            $type = "关闭库存控制";
        } elseif ($aid == 28) {
            $type = "清除商品关联";
        } else {
            $type = "(未知操作)";
        }

        foreach ($checkbox as $tid) {
            if ($aid == 1) {
                $DB->query("UPDATE `pre_tools` SET `close`='0' where `tid`=:tid limit 1", [':tid' => $tid]);
            } elseif ($aid == 2) {
                $DB->query("UPDATE `pre_tools` SET `close`='1' where `tid`=:tid limit 1", [':tid' => $tid]);
            } elseif ($aid == 3) {
                $DB->query("UPDATE `pre_tools` SET `active`='1' where `tid`=:tid limit 1", [':tid' => $tid]);
            } elseif ($aid == 4) {
                $DB->query("UPDATE `pre_tools` SET `active`='0' where `tid`=:tid limit 1", [':tid' => $tid]);
            } elseif ($aid == 5) {
                $DB->query("DELETE FROM pre_tools WHERE tid=:tid", [':tid' => $tid]);
            } elseif ($aid == 8) {
                $DB->query("UPDATE `pre_tools` SET `is_rank`=1 where tid=:tid limit 1", [':tid' => $tid]);
            } elseif ($aid == 9) {
                $DB->query("UPDATE `pre_tools` SET `is_rank`=0 where tid=:tid limit 1", [':tid' => $tid]);
            } elseif ($aid == 12) {
                $DB->query("UPDATE `pre_tools` SET `result`=:result where tid=:tid limit 1", [':result' => $result, ':tid' => $tid]);
            } elseif ($aid == 13) {
                $DB->query("UPDATE `pre_tools` SET `close_alert`=:hide_val where tid=:tid limit 1", [':hide_val' => $hide_val, ':tid' => $tid]);
            } elseif ($aid == 16) {
                $DB->query("UPDATE `pre_tools` SET `prid`=:prid where tid=:tid limit 1", [':prid' => $prid, ':tid' => $tid]);
            } elseif ($aid == 17) {
                $DB->query("UPDATE `pre_tools` SET `active`='1',`close`='0' where tid=:tid limit 1", [':tid' => $tid]);
            } elseif ($aid == 18) {
                $DB->query("UPDATE `pre_tools` SET `active`='0',`close`='1' where tid=:tid limit 1", [':tid' => $tid]);
            } elseif ($aid == 19) {
                $DB->query("UPDATE `pre_tools` SET `title`=:title where tid=:tid limit 1", [':title' => $pay_val, ':tid' => $tid]);
            } elseif ($aid == 20) {
                $sqlData = [':alipay' => $pay['alipay'], ':qqpay' => $pay['qqpay'], ':wxpay' => $pay['wxpay'], ':rmb' => $pay['rmb'], ':tid' => $tid];
                $DB->query("UPDATE `pre_tools` SET `pay_alipay`=:alipay,`pay_qqpay`=:qqpay,`pay_wxpay`=:wxpay,`pay_rmb`=:rmb where tid=:tid limit 1", $sqlData);
            } elseif ($aid == 21) {
                $sqlData = [':shopimg' => $shopimg, ':tid' => $tid];
                $DB->query("UPDATE `pre_tools` SET `shopimg`=:shopimg where tid=:tid limit 1", $sqlData);
            } elseif ($aid == 22) {
                $sqlData = [':input' => $input, ':inputs' => $inputs, ':tid' => $tid];
                $DB->query("UPDATE `pre_tools` SET `input`=:input,`inputs`=:inputs where tid=:tid limit 1", $sqlData);
            } elseif ($aid == 23) {
                $sqlData = [':desc' => $desc, ':tid' => $tid];
                $DB->query("UPDATE `pre_tools` SET `desc`=:desc where tid=:tid limit 1", $sqlData);
            } elseif ($aid == 24) {
                $sqlData = [':alert' => $alert, ':tid' => $tid];
                $DB->query("UPDATE `pre_tools` SET `alert`=:alert where tid=:tid limit 1", $sqlData);
            } elseif ($aid == 25) {
                $sqlData = [':tid' => $tid];
                $DB->query("UPDATE `pre_tools` SET `is_email`=1 where tid=:tid limit 1", $sqlData);
            } elseif ($aid == 26) {
                $sqlData = [':tid' => $tid];
                $DB->query("UPDATE `pre_tools` SET `is_email`=0 where tid=:tid limit 1", $sqlData);
            } elseif ($aid == 29 || $aid == 30) {
                $sqlData = [':open' => ($aid == 29 ? 1 : 0), ':tid' => $tid];
                $DB->query("UPDATE `pre_tools` SET `stock_open`=:open where tid=:tid limit 1", $sqlData);
            } elseif ($aid == 28) {
                $sqlData = [':tid' => $tid];
                $DB->query("UPDATE `pre_tools` SET `mesh_list`='' where tid=:tid limit 1", $sqlData);
            } elseif ($aid == 6) {
                $DB->query("insert into `pre_tools` (`cid`,`name`,`price`,`prid`,`cost`,`cost2`,`input`,`inputs`,`alert`,`desc`,`value`,`is_curl`,`curl`,`shequ`,`goods_id`,`goods_type`,`goods_param`,`repeat`,`multi`,`sort`,`active`) select `cid`,`name`,`price`,`prid`,`cost`,`cost2`,`input`,`inputs`,`alert`,`desc`,`value`,`is_curl`,`curl`,`shequ`,`goods_id`,`goods_type`,`goods_param`,`repeat`,`multi`,`sort`,`active` from `pre_tools` where `tid` = :tid", [':tid' => $tid]);
            } elseif ($aid == 7) {
                if ($check_val == (0 - 1)) {
                    $DB->query("UPDATE `pre_tools` SET check_val='0' where tid='$tid' limit 1");
                } elseif ($check_val != "") {
                    if ($DB->query("UPDATE `pre_tools` SET check_val='" . $check_val . "' where tid='{$tid}' limit 1")) {
                        $ok++;
                    }
                } elseif (empty($check_val)) {
                    exit('{"code":-1,"msg":"请先选择Q钻类型，也可以选择关闭Q钻自带检测"}');
                }
            }
            $i++;
        }
        if ($aid == 7) {
            exit('{"code":0,"msg":"成功设置' . $ok . '个商品的Q钻自带检测，失败' . ($i - $ok) . '个！' . $DB->error() . '","check_val":"' . $check_val . '"}');
        }
        exit('{"code":0,"msg":"成功' . $type . $i . '个商品"}');
        break;
    case 'class_change':
        checkAuthority('class');
        $aid         = (int) input('post.checkAction');
        $checkedList = input('post.checkedList', 1);

        if ($aid == 1) {
            $type = "显示选中";
        } elseif ($aid == 2) {
            $type = "隐藏选中";
        } elseif ($aid == 3) {
            $type = "保存所有";
        } elseif ($aid == 10) {
            $type = "删除选中";
        } elseif ($aid == 4) {
            $type = "设置登录可见";
        } elseif ($aid == 5) {
            $type = "取消登录可见";
        } else {
            exit('{"code":-1,"msg":"无效操作"}');
        }
        if ($aid == 3) {
            $classNameList = input('post.classNameList', 1);
            foreach ($classNameList as $cid => $name) {
                $name = addslashes($name);
                $data = [
                    ':name' => $name,
                    ':cid'  => $cid,
                ];
                $sql = "UPDATE `pre_class` SET `name`=:name where cid=:cid limit 1";
                if ($DB->query($sql, $data)) {
                    $i++;
                }
            }
        } else {
            foreach ($checkedList as $cid) {
                if ($aid == 1) {
                    $DB->query("UPDATE `pre_class` SET `active`='1' where `cid`=:cid limit 1", [':cid' => $cid]);
                } elseif ($aid == 2) {
                    $DB->query("UPDATE `pre_class` SET `active`='0' where `cid`=:cid limit 1", [':cid' => $cid]);
                } elseif ($aid == 4) {
                    $DB->query("UPDATE `pre_class` SET `islogin`='1' where `cid`=:cid limit 1", [':cid' => $cid]);
                } elseif ($aid == 5) {
                    $DB->query("UPDATE `pre_class` SET `islogin`='0' where `cid`=:cid limit 1", [':cid' => $cid]);
                } elseif ($aid == 10) {
                    $DB->query("DELETE FROM `pre_class` where `cid`=:cid", [':cid' => $cid]);
                    $DB->query("DELETE FROM `pre_tools` where `cid`=:cid", [':cid' => $cid]);
                }
                $i++;
            }
        }
        exit('{"code":0,"msg":"成功' . $type . $i . '个分类"}');
        break;
    case 'delTool':
        checkAuthority('shops');
        $tid = intval(input('get.tid'));
        $sql = "DELETE FROM pre_tools WHERE tid='$tid' limit 1";
        if ($DB->query($sql)) {
            exit('{"code":0,"msg":"删除商品成功！"}');
        } else {
            exit('{"code":-1,"msg":"删除商品失败！' . $DB->error() . '"}');
        }

        break;
    case 'setStatus':
        checkAuthority('orders');
        $id     = intval(input('get.name'));
        $status = intval(input('get.status'));
        if ($status == 5) {
            if ($DB->query("DELETE FROM pre_orders WHERE id='$id'")) {
                exit('{"code":200}');
            } else {
                exit('{"code":400,"msg":"删除订单失败！' . $DB->error() . '"}');
            }
        } else {
            if ($DB->query("UPDATE pre_orders set status='$status',result=NULL where id='{$id}'")) {
                exit('{"code":200}');
            } else {
                exit('{"code":400,"msg":"修改订单失败！' . $DB->error() . '"}');
            }
        }
        break;
    case 'order':
        checkAuthority('orders');
        $id   = intval(input('get.id'));
        $rows = $DB->get_row("SELECT * from pre_orders where id= ? limit 1", [$id]);
        if (!$rows) {
            exit('{"code":-1,"msg":"当前订单不存在！"}');
        }

        $tool = $DB->get_row("SELECT * from pre_tools where tid= ? limit 1", [$rows['tid']]);
        if (strpos($rows['payorder'], 'kid') !== false) {
            $kid           = explode(':', $rows['payorder']);
            $kid           = $kid[1];
            $trade         = $DB->get_row("SELECT * from pre_kms where kid= ? limit 1", [$kid]);
            $trade['type'] = '卡密';
            $addstr        = '<li class="list-group-item"><b>使用卡密：</b>' . $trade['km'] . '</li>';
        } elseif (strpos($rows['payorder'], 'invite') !== false) {
            $trade['type'] = '推广赠送';
        } elseif (!empty($rows['payorder'])) {
            $trade  = $DB->get_row("SELECT * from pre_pay where trade_no= ? limit 1", [$rows['payorder']]);
            $addstr = '<li class="list-group-item"><b style="color:blue">支付金额：</b><span style="color:red;">' . $trade['money'] . '</span>&nbsp;元' . ($trade['tid'] == -3 ? '（' . $trade['num'] . '件商品）' : null) . '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>支付IP：</b><a href="http://m.ip138.com/ip.asp?ip=' . $trade['ip'] . '" target="_blank">' . $trade['ip'] . '</a></li>';
            if (stripos($trade['type'], 'rmb') !== false) {
                $addstr .= '<li class="list-group-item"><b>支付用户UID：</b>' . (is_numeric($rows['userid']) && $rows['userid'] != $rows['zid'] ? '<a href ="userlist.php?zid=' . $rows['userid'] . '" target="_blank">' . $rows['userid'] . '</a>' : '<a href ="sitelist.php?zid=' . $rows['zid'] . '" target="_blank">' . $rows['zid'] . '</a>') . '</li>';
            }
        } else {
            $trade['type'] = '游客';
        }
        if ($rows['zid'] > 1) {
            $addstr .= '<li class="list-group-item"><b style="color:green">分站成本：</b><span style="color:red;">' . $rows['cost'] . '</span>&nbsp;元</li>';
        }
        $siterow = $DB->get_row("SELECT * from pre_site where zid= ? limit 1", [$rows['zid']]);
        $input   = $tool['input'] ? $tool['input'] : '下单QQ';
        $inputs  = explode('|', $tool['inputs']);
        $value   = $tool['value'] > 0 ? $tool['value'] : 1;
        if ($siterow) {
            if (stripos($trade['type'], "rmb") !== false) {
                if ($siterow['power'] == 2) {
                    $orderType = ' <span style="color:#e605b8">旗舰版分站</span>';
                } else if ($siterow['power'] == 1) {
                    $orderType = ' <span style="color:#e6053b">专业版分站</span>';
                } else {
                    $orderType = ' <span style="color:#e63a05">注册普通用户</span>';
                }
            } else {
                $orderType = ' <span style="color:#4169E1">分站游客用户</span>';
            }
        } else {
            $orderType = ' <span style="color:#008000">主站游客用户</span>';
        }

        $shequ_url = '';
        if ($tool['is_curl'] == 2) {
            $shequ = $DB->get_row("SELECT * from `pre_shequ` where `id`= ? limit 1", [$tool['shequ']]);
            if (is_array($shequ)) {
                $shequ_url = '<li class="list-group-item"><b>对接社区地址：</b><a href="http://' . $shequ['url'] . '" target="_blank">' . $shequ['url'] . '</a></li>';
                $shequ_url .= '<li class="list-group-item"><b>对接社区类型：</b><span style="color:#e605b8;">' . getShequTypeName($shequ['type']) . '</span></li>';
            } else {
                $shequ_url = '<li class="list-group-item"><b>对接社区地址：</b>社区数据不存在</li>';
                $shequ_url .= '<li class="list-group-item"><b>对接社区类型：</b>社区数据不存在</li>';
            }
        }

        if ($rows['cost'] > 0 && $rows['money'] > 0) {
            $profit = round($rows['cost'] - $rows['price1'], 2);
        } else {
            $profit = round($rows['money'] - $rows['price1'], 2);
        }

        if ($rows["stock_id"] > 0) {
            $stock_row = $DB->get_row("SELECT * FROM `pre_stock` where id='" . $rows["stock_id"] . "'");
            $num       = $stock_row['value'] > 1 ? intval($stock_row["value"] * $rows["value"]) : $value;
        } else {
            $num = $rows['value'] * $value;
        }

        $addstr .= '<li class="list-group-item"><b style="color:red">预计利润：</b><span style="color:red;">' . $profit . '</span>&nbsp;元 [理论，未扣除手续费]</li>';
        $shopurl     = './shoplist.php?tid=' . $tool['tid'];
        $shopurl_btn = '&nbsp;<a href="./shopedit.php?my=edit&tid=' . $tool['tid'] . '" class="btn btn-danger btn-xs">编辑</a>';
        $data        = '<li class="list-group-item"><b>商户订单号：</b><span style="color:#0000ff;">' . $rows["payorder"] . '</span></li>'
        . $data .= '<li class="list-group-item"><b>订单编号：</b><span style="color:#ff0000;">' . $rows['id'] . '</span>';
        if ($tool['is_curl'] == 2) {
        $data .= ' <b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;对接订单号：</b><span style="color:#e605b8;">' . $rows['djorder'] . '</span>';
        }
        $data .= '</li>';
        $data .= '<li class="list-group-item"><b>商品名称：</b>' . $shopurl_btn . '&nbsp;<a href="' . $shopurl . '">' . $tool['name'] . '</a>&nbsp;</li>'
    . '<li class="list-group-item"><b>订单信息：</b>' . $input . '：<span style="color:#e605b8;">' . $rows['input']
    . ($rows['input2'] ? '，' . $inputs[0] . '：' . $rows['input2'] : null)
    . ($rows['input3'] ? '，' . $inputs[1] . '：' . $rows['input3'] : null)
    . ($rows['input4'] ? '，' . $inputs[2] . '：' . $rows['input4'] : null)
    . ($rows['input5'] ? '，' . $inputs[3] . '：' . $rows['input5'] : null)
    . '</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>下单数量：</b><span style="color:#0000ff;">' . $num . '</span></li>' 
    . '<li class="list-group-item"><b>订单卡密：</b><span style="color:#ff0000;">' . $rows['result'] . '</span></li>'
    .  '<li class="list-group-item"><b>站点UID：</b>'
          . '<a href="sitelist.php?zid=' . $rows['zid'] . '" target="_blank">' . $rows['zid'] . '</a>'
          . '</li>'
    . '<li class="list-group-item"><b>下单时间：</b><span style="color:#5bc0de;">' . $rows['addtime'] . '</span></li>'
    . '<li class="list-group-item"><b>上次订单同步时间:</b><span style="color:#5bc0de;">' . ($rows['uptime'] > 0 ? date('Y-m-d H:i:s', $rows['uptime']) : '&nbsp;无') . '</span></li>'
    . '<li class="list-group-item"><b>购买方式：</b>' . $trade['type'] . $orderType . '</li>'
    . $addstr
    . ($trade['siteurl'] ? '<li class="list-group-item"><b>订单来源地址：</b><a href="http://' . $trade['siteurl'] . '" target="_blank">http://' . $trade['siteurl'] . '</a></li>' : '')
    . $shequ_url;
        $result      = array("code" => 0, "msg" => "succ", "data" => $data);
        exit(json_encode($result));
        break;
    case 'order2':
        checkAuthority('orders');
        $id   = intval(input('get.id'));
        $rows = $DB->get_row("SELECT * from pre_orders where id='$id' limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"当前订单不存在！"}');
        }

        $tool = $DB->get_row("SELECT * from pre_tools where tid='{$rows['tid']}' limit 1");
        $data = getInputsHtml($rows, $tool);
        if ($rows['inputattr']) {
            $data .= '<div class="form-group"><div class="input-group"><div class="input-group-addon" id="inputname5">类型属性</div><input type="text" id="inputattr" value="' . $rows['inputattr'] . '" class="form-control" required/></div></div>';
        }
        $data .= '<div class="form-group"><div class="input-group"><div class="input-group-addon" id="">份数</div><input type="text" id="value" value="' . $rows['value'] . '" class="form-control" required/></div></div>';
        if ($tool['is_curl'] == 2) {
            $data .= '<div class="form-group"><div class="input-group"><div class="input-group-addon" id="inputname5">对接订单号</div><input type="text" id="djorder" value="' . $rows['djorder'] . '" class="form-control" required/></div></div>';
        }

        $data .= '<div class="form-group"><div class="input-group"><div class="input-group-addon" id="inputname5">物流单号</div><input type="text" id="exporder" placeholder="无物流单号可留空" value="' . $rows['exporder'] . '" class="form-control" required/></div></div>';
        $data .= '<input type="submit" id="save" onclick="saveOrder(' . $id . ')" class="btn btn-primary btn-block" value="保存">';
        $result = array("code" => 0, "msg" => "succ", "data" => $data);
        exit(json_encode($result));
        break;
    case 'order3':
        checkAuthority('orders');
        $id   = intval(input('get.id'));
        $rows = $DB->get_row("SELECT * from pre_orders where id='$id' limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"当前订单不存在！"}');
        }

        $data = '<div class="form-group"><div class="input-group"><div class="input-group-addon">份数</div><input type="text" id="num" value="' . $rows['value'] . '" class="form-control" required/></div></div>';
        $data .= '<input type="submit" id="save" onclick="saveOrderNum(' . $id . ')" class="btn btn-primary btn-block" value="保存">';
        $result = array("code" => 0, "msg" => "succ", "data" => $data);
        exit(json_encode($result));
        break;
    case 'editOrder':
        checkAuthority('orders');
        $id       = intval(input('post.id'));
        $value    = input('post.inputvalue');
        $value2   = input('post.inputvalue2');
        $value3   = input('post.inputvalue3');
        $value4   = input('post.inputvalue4');
        $value5   = input('post.inputvalue5');
        $exporder = input('post.exporder');
        $num      = intval(input('post.value'));
        $djorder  = input('post.djorder');
        $sqlData  = [":input" => $value, ":input2" => $value2, ":input3" => $value3, ":input4" => $value4, ":input5" => $value5, ":exporder" => $exporder, ":num" => $num, ":djorder" => $djorder, ":id" => $id];
        $sql      = "UPDATE `pre_orders` SET `input`=:input,`input2`=:input2,`input3`=:input3,`input4`=:input4,`input5`=:input5,`exporder`=:exporder,`value`=:num,`djorder`=:djorder where `id`=:id";
        if ($DB->query($sql, $sqlData)) {
            exit('{"code":0,"msg":"修改订单成功！"}');
        } else {
            exit('{"code":-1,"msg":"修改订单失败！' . $DB->error() . '"}');
        }

        break;
    case 'editOrderNum':
        checkAuthority('orders');
        $id  = intval(input('post.id'));
        $num = intval(input('post.num'));
        $sds = $DB->query("UPDATE `pre_orders` SET `value`='$num' where `id`='$id'");
        if ($sds) {
            exit('{"code":0,"msg":"修改订单成功！"}');
        } else {
            exit('{"code":-1,"msg":"修改订单失败！' . $DB->error() . '"}');
        }

        break;
    case 'setBatch': //同步商品基本参数配置
        $tid = intval(input('get.tid'));
        $row = $DB->get_row("SELECT * FROM pre_tools where tid='{$tid}'");
        if (stripos($row['mesh_list'], ",") !== false || stripos($row['mesh_list'], "|") !== false) {
            if (stripos($row['mesh_list'], ",") !== false) {
                $mesh_arr = explode(",", $row['mesh_list']);
            } else {
                $mesh_arr = explode("|", $row['mesh_list']);
            }

            foreach ($mesh_arr as $tids) {
                if ($DB->query("UPDATE `pre_tools` SET input='" . $row['input'] . "',inputs='" . $row['inputs'] . "',alert='" . addslashes($row['alert']) . "',`multi`='" . $row['multi'] . "',shopimg='" . $row['shopimg'] . "',validate='" . $row['validate'] . "',`desc`='" . addslashes($row['desc']) . "',shequ='" . $row['shequ'] . "',goods_id='" . $row['goods_id'] . "',goods_type='" . $row['goods_type'] . "',goods_param='" . $row['goods_param'] . "',is_curl='" . $row['is_curl'] . "',`curl`='" . $row['curl'] . "',`prid`='" . $row['prid'] . "',min='" . $row['min'] . "',`check`='" . $row['check'] . "',`result`='" . addslashes($row['result']) . "',`check_val`='" . $row['check_val'] . "',`is_rank`='" . $row['is_rank'] . "',`repeat`='" . $row['repeat'] . "' where tid='" . $tids . "'")) {
                } else {
                    exit('{"code":-1,"msg":"操作失败，' . $DB->error() . '"}');
                }
            }

            exit('{"code":0,"msg":"同步关联商品基本配置成功！"}');
        } else {
            exit('{"code":-1,"msg":"该商品未和其他商品关联！","name":"' . $row['name'] . '"}');
        }
        break;
    case 'operation':
        checkAuthority('orders');
        $status     = daddslashes(input('post.status'));
        $checkbox   = daddslashes(input('post.checkbox'));
        $is_djorder = (int) daddslashes(input('post.is_djorder'));
        $i          = 0;
        $ok         = 0;
        $warnlist   = 0;
        $result     = trim(input('post.result_all'));
        foreach ($checkbox as $id) {
            if (intval($id) <= 0) {
                continue;
            }

            if ($status == (0 - 1)) {
                $DB->query("DELETE FROM pre_orders WHERE id='$id'");
                $ok++;
            } elseif ($status == 5) {
                if ($is_djorder) {
                    $result = do_orders_all($id, true);
                } else {
                    $result = do_orders_all($id, false);
                }
                if (strpos($result, '成功') !== false) {
                    $ok++;
                } else {
                    if ($id) {
                        $warnlist .= $id . ',';
                    }
                }
            } elseif ($status == 6) {
                $row = $DB->get_row("SELECT * from pre_orders where id=:id limit 1", [':id' => $id]);
                if ($row && $row['zid'] > 1 && $row['status'] == 3 && strstr($row['type'], 'rmb') !== false) {

                    $tc_point = $DB->get_column("SELECT point from pre_points where zid=:zid and action='提成' and orderid=:orderid limit 1", [':zid' => $row['zid'], ':orderid' => $id]);
                    $money    = $row['money'];
                    if ($tc_point > 0) {
                        $money -= $tc_point;
                    }

                    $DB->query("UPDATE `pre_site` SET `money`=`money`+{$money} where `zid`='{$row['zid']}'");
                    addPointLogs($row['zid'], $money, '退款', '订单(ID' . $id . ')已退款到分站余额');

                    // 供货订单扣提成
                    if ($row['sid'] > 0) {
                        // 避免多次扣款
                        $find = Db::name('master_points')->where(['action' => '扣款', 'orderid' => $orderid])->find();
                        if (!$find) {
                            $price1 = $row['price1'];
                            $DB->query("UPDATE `pre_master` SET `income`=`income`-{$price1} where `zid`='{$row['sid']}'");
                            addMasterPointLogs($row['sid'], $price1, '扣款', '订单(ID' . $id . ')已退款, 扣除订单提成', $orderid);
                        }
                    }

                    $update = Db::name('orders')->where(['id' => $id])->update([
                        'status' => 4,
                        'result' => '',
                    ]);

                    if ($update !== false) {
                        $ok++;
                    }
                    $DB->query("UPDATE pre_orders set status='4',result=NULL where id='{$id}'");
                } else {
                    if ($id) {
                        $warnlist .= $id . '（非分站下单）,';
                    }
                }
            } else {
                //$row=$DB->get_row("SELECT * from pre_orders where id= ? limit 1",array($id));
                if ($status == 7) {
                    if ($result != "") {
                        $q = ",result='" . $result . "'";
                    }

                    $DB->query("UPDATE pre_orders set status='" . $status . "'" . $q . " where id='$id' limit 1");
                } else {
                    $DB->query("UPDATE pre_orders set status='" . $status . "' where id='" . $id . "' limit 1");
                }
            }
            $i++;
        }

        if ($status == 5) {
            $warnlist = trim($warnlist, ',');
            exit('{"code":0,"msg":"成功补单' . $ok . '条订单，失败订单（' . $warnlist . '）"}');
        } elseif ($status == (0 - 1)) {
            exit('{"code":0,"msg":"成功删除' . $ok . '条订单"}');
        } elseif ($status == 6) {
            exit('{"code":0,"msg":"成功退款' . $ok . '条订单，失败订单（' . $warnlist . '）"}');
        } else {
            exit('{"code":0,"msg":"成功改变' . $i . '条订单状态"}');
        }
        break;
    case 'result':
        $id   = intval(input('post.id'));
        $rows = $DB->get_row("SELECT * from pre_orders where id='$id' limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"当前订单不存在！"}');
        }

        exit('{"code":0,"result":"' . $rows['result'] . '"}');
        break;
    case 'kms':
        $id   = intval(input('get.id'));
        $rows = $DB->get_row("SELECT * from pre_faka where kid='$id' limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"当前卡密不存在！"}');
        }

        $data   = '<li class="list-group-item" style="word-break:break-all;"><b>卡号：</b>' . $rows['km'] . '</li><li class="list-group-item" style="word-break:break-all;"><b>密码：</b>' . $rows['pw'] . '</li>';
        $result = array("code" => 0, "msg" => "succ", "data" => $data);
        exit(json_encode($result));
        break;
    case 'checkshequ':
        $url = input('post.url');
        if (gethostbyname($url) == '127.0.0.1') {
            exit('{"code":0}');
        } else {
            exit('{"code":1}');
        }
        break;
    case 'checkclone':
        $url     = input('post.url');
        $url_arr = parse_url($url);
        if ($url_arr['host'] == $_SERVER['HTTP_HOST']) {
            exit('{"code":2}');
        }

        $data = get_curl($url . 'api.php?act=clone');
        $arr  = json_decode($data, true);
        if (array_key_exists('code', $arr) && array_key_exists('msg', $arr)) {
            exit('{"code":1}');
        } elseif (substr(bin2hex($data), 0, 6) == 'efbbbf') {
            exit('{"code":3}');
        } else {
            exit('{"code":0}');
        }
        break;
    case 'checkdwz':
        $url  = input('post.url');
        $data = get_curl($url);
        if (json_decode($data, true)) {
            exit('{"code":1}');
        } elseif ($data) {
            exit('{"code":2}');
        } else {
            exit('{"code":0}');
        }
        break;
    case 'getTixian': //查看提现信息
        checkAuthority('tixian');
        $id   = intval(input('get.id'));
        $rows = $DB->get_row("SELECT * from pre_tixian where id='$id' limit 1");
        if (!$rows) {
            exit('{"code":-1,"msg":"当前提现记录不存在！"}');
        }

        $data = '<div class="form-group"><div class="input-group"><div class="input-group-addon">提现方式</div><select class="form-control" id="pay_type" default="' . $userrow['pay_type'] . '"><option value="0">支付宝</option><option value="1">微信</option><option value="2">QQ钱包</option></select></div></div>';
        $data .= '<div class="form-group"><div class="input-group"><div class="input-group-addon">提现账号</div><input type="text" id="pay_account" value="' . $rows['pay_account'] . '" class="form-control" required/></div></div>';
        $data .= '<div class="form-group"><div class="input-group"><div class="input-group-addon">提现姓名</div><input type="text" id="pay_name" value="' . $rows['pay_name'] . '" class="form-control" required/></div></div>';
        $data .= '<input type="submit" id="save" onclick="saveInfo(' . $id . ')" class="btn btn-primary btn-block" value="保存">';
        $result = array("code" => 0, "msg" => "succ", "data" => $data);
        exit(json_encode($result));
        break;
    case 'editTixian': //修改提现信息
        checkAuthority('tixian');
        $id          = intval(input('post.id'));
        $pay_type    = trim(daddslashes(input('post.pay_type')));
        $pay_account = trim(daddslashes(input('post.pay_account')));
        $pay_name    = trim(daddslashes(input('post.pay_name')));
        $sds         = $DB->query("UPDATE `pre_tixian` SET `pay_type`='$pay_type',`pay_account`='$pay_account',`pay_name`='$pay_name' where `id`='$id'");
        if ($sds) {
            exit('{"code":0,"msg":"修改记录成功！"}');
        } else {
            exit('{"code":-1,"msg":"修改记录失败！' . $DB->error() . '"}');
        }

        break;
    case 'opTixian': //操作提现
        checkAuthority('tixian');
        $id = intval(input('post.id'));
        $op = input('op');
        if ($op == 'delete') {
            $sql = "DELETE FROM pre_tixian WHERE id='$id'";
            if ($DB->query($sql)) {
                exit('{"code":0,"msg":"删除成功！"}');
            } else {
                exit('{"code":-1,"msg":"删除失败！' . $DB->error() . '"}');
            }
        } elseif ($op == 'complete') {
            if ($DB->query("UPDATE pre_tixian set status=1,endtime=NOW() where id='$id'")) {
                exit('{"code":0,"msg":"已变更为已提现状态"}');
            } else {
                exit('{"code":-1,"msg":"变更失败！' . $DB->error() . '"}');
            }
        } elseif ($op == 'reset') {
            if ($DB->query("UPDATE pre_tixian set status=0 where id='$id'")) {
                exit('{"code":0,"msg":"已变更为未提现状态"}');
            } else {
                exit('{"code":-1,"msg":"变更失败！' . $DB->error() . '"}');
            }
        } elseif ($op == 'back') {
            $rows = $DB->get_row("SELECT * from pre_tixian where id='$id' limit 1");
            $DB->query("UPDATE pre_site set money=money+{$rows['money']} where zid='{$rows['zid']}'");
            addPointLogs($rows['zid'], $rows['money'], '退回', '提现被退回到分站余额' . $rows['money'] . '元，请检查提现方式是否正确');
            if ($DB->query("DELETE FROM pre_tixian WHERE id='$id'")) {
                exit('{"code":0,"msg":"已成功退回到分站余额"}');
            } else {
                exit('{"code":-1,"msg":"退回失败！' . $DB->error() . '"}');
            }
        }
        break;
    case 'operation':
        adminpermission('order', 2);
        $status=$_POST['status'];
        $checkbox=$_POST['checkbox'];
        $i=0;
        $statuss=$conf['shequ_status']?$conf['shequ_status']:1;
        foreach($checkbox as $id){
	        if($status=='操作订单')continue;
	        if($status==4)$DB->exec("DELETE FROM pre_orders WHERE id='$id'");
		elseif($status==5){
			$result = do_goods($id);
		}elseif($status==6){
			$row=$DB->getRow("select * from pre_orders where id='$id' limit 1");
			if($row && $row['zid']>1 && $row['status']==3 && is_numeric($row['userid'])){
				$zid = intval($row['userid']);
				changeUserMoney($zid, $row['money'], true, '退款', '订单(ID'.$id.')已退款到余额');
				rollbackPoint($id);
				$DB->exec("update pre_orders set status='4',result=NULL where id='{$id}'");
			}
		}
		else $DB->exec("update pre_orders set status='$status' where id='$id' limit 1");
		$i++;
        }
        exit('{"code":0,"msg":"成功改变'.$i.'条订单状态"}');
        break;
    case 'djOrder2': //重新下单
        adminpermission('order', 2);
        $id=intval($_GET['id']);
        $row=$DB->getRow("select status from pre_orders where id='$id' limit 1");
        if(!$row) exit('{"code":-1,"msg":"当前订单不存在！"}');
        if($row['status']==1)exit('{"code":-1,"msg":"该订单状态为已完成,无需重复提交"}');
        $url=$_POST['url'];
        $post=$_POST['post'];
        $result = do_goods($id,$url,$post);
        if(strpos($result,'成功')!==false){
        exit('{"code":0,"msg":"下单成功！"}');
        }else{
        exit('{"code":-1,"msg":"'.$result.'"}');
        }
        break;
    case 'djOrder': //重新下单
        checkAuthority('orders');
        $id     = intval(input('get.id'));
        $result = do_orders_all($id);
        if (strpos($result, '成功') !== false) {
            $result = array('code' => 0, 'msg' => $result);
        } else {
            $result = array('code' => -1, 'msg' => $result);
        }
        exit(json_encode($result));
        break;
    case 'reShop': //同步订单商品对接信息
        checkAuthority('shops');
        $id  = intval(input('get.id'));
        $row = $DB->get_row("SELECT * from pre_orders where id='{$id}' limit 1");
        if (!$row) {
            exit('{"code":-1,"msg":"当前订单不存在！"}');
        }

        $count = $DB->count("SELECT count(*) FROM `pre_faka` WHERE `orderid`= ?", [$id]);

        if ($count > 0) {
            $DB->query("UPDATE pre_orders set djzt='3' where id= ?", [$id]);
        } else {
            $tool = $DB->get_row("SELECT * from pre_tools where tid= ? limit 1", [$row['tid']]);
            if ($tool['is_curl'] == 2) {
                if ($row['djorder']) {
                    $DB->query("UPDATE pre_orders set djzt=1,result='" . $tool['result'] . "' where id='{$id}'");
                } else {
                    $DB->query("UPDATE pre_orders set djzt=2,result='' where id='{$id}'");
                }
            } elseif ($tool['is_curl'] == 4) {
                if ($row['status'] == 1) {
                    $DB->query("UPDATE pre_orders set djzt=3,result='" . $tool['result'] . "' where id='{$id}'");
                } else {
                    $DB->query("UPDATE pre_orders set djzt=4,result='' where id='{$id}'");
                }
            }
        }

        exit('{"code":0,"msg":"succ"}');
        break;
    case 'showStatus': //订单进度查询
        checkAuthority('orders');
        $id  = intval(input('get.id'));
        $row = $DB->get_row("SELECT * from pre_orders where id='{$id}' limit 1");
        if (!$row) {
            exit('{"code":-1,"msg":"当前订单不存在！"}');
        }

        $tool  = $DB->get_row("SELECT * from pre_tools where tid= ? limit 1", [$row['tid']]);
        $shequ = $DB->get_row("SELECT * from pre_shequ where id= ? limit 1", [$tool['shequ']]);
        if (!preg_match('/^(https|http):\/\/[\w\.\-]+\.[\w\:\/]+\/$/', $shequ["url"])) {
            preg_match('/[\w\.\-]+\.[\w\:\/]+/', $shequ["url"], $arr);
            if ($shequ["ssl"] == 1) {
                $shequ["url"] = 'https://' . $arr[0] . '/';
            } else {
                $shequ["url"] = 'http://' . $arr[0] . '/';
            }
        }

        $InfoControler = new \core\InfoControler();
        if ($shequ['type'] == 2) {
            $shequ['type'] = 0;
        }

        if ($shequ['type'] == 4) {
            $list = $InfoControler->query_jiuliu($row, $shequ);
        } elseif ($shequ['type'] == 23) {
            $list = $InfoControler->query_chengzi($row, $shequ);
        } elseif ($shequ['type'] == 24) {
            $list = $InfoControler->query_guakebao($row, $shequ);
        } else {
            $list = $InfoControler->query_extend($row, $shequ);
        }

        if (($list['order_state'] == '已完成' || $list['order_state'] == '订单已完成') && $row['status'] == 2) {
            $DB->query("UPDATE pre_orders set status=1 where id='{$id}'");
        }
        if (is_array($list)) {
            $list['domain']  = $shequ['url'];
            $list['djorder'] = $row['djorder'];
            $result          = $list;
        } elseif ($list) {
            $result = array('code' => -1, 'msg' => $list);
        } else {
            $result = array('code' => -1, 'msg' => '获取数据失败', 'list' => $list);
        }
        exit(json_encode($result));
        break;
    case 'setResult': //修改订单状态和信息
        checkAuthority('orders');
        $orderid = intval(input('post.id'));
        $status  = intval(input('post.status'));
        $result  = trim(input('result'));
        $row     = $DB->get_row("SELECT * from pre_orders where id= ? limit 1", [$orderid]);
        if (!$row) {
            exit('{"code":-1,"msg":"当前订单不存在！"}');
        }

        if ($row['value'] < 1) {
            $row['value'] = 1;
        }

        if ($status == 9) {
            if ($row['refund'] == 1 || $row['status'] == 4) {
                $result = [
                    'code' => -1,
                    'msg'  => !$row['refund_price'] ? '该订单已退款' : "该订单已退款，退款金额" . $row['refund_price'] . "元",
                ];
            } else {
                $refund_type = input('post.refund_type');
                $tkbz        = input('post.tkbz');
                $bz          = $tkbz != "" ? '（' . $tkbz . '）' : '';
                $num         = input('post.num');
                $addnum      = input('post.addnum') ? input('post.addnum') : 0;

                $refund_fenzhan_type = $conf['refund_fenzhan_type'];
                if ($row['zid'] > 0 && isset($_POST['refund_fenzhan_type'])) {
                    $refund_fenzhan_type = intval(input('post.refund_fenzhan_type'));
                }
                //$payrow = $DB->get_row("SELECT * from pre_pay where trade_no='" . $row['payorder'] . "' limit 1");
                $tool = $DB->get_row("SELECT * from pre_tools where tid='" . $row['tid'] . "' limit 1");
                //$money  = $row['money'] ? $row['money'] : $payrow['money'];
                $site = $DB->get_row("SELECT * from pre_site where `zid`= ? limit 1", [$row['zid']]);
                //$buyPrice = $row['cost']>0?$row['cost']:$money;
                $orderPrice = $row['money'];
                //获取应退款金额
                $refund = get_refund($orderPrice, $row, $num, $addnum, $refund_type);
                $up_bz  = '';
                if ($row['zid'] > 1 && $refund_fenzhan_type >= 0) {
                    //退款到分站
                    $msg = processRefund($row, $tool, $site, $refund);
                } else {
                    //手动退款给客户
                    $msg = processRefundByUser($row, $tool, $site, $refund);
                }

                // 供货订单扣提成
                if ($row['sid'] > 0) {
                    // 避免多次扣款
                    $find = Db::name('master_points')->where(['action' => '扣款', 'orderid' => $orderid])->find();
                    if (!$find) {
                        $price1 = $row['price1'];
                        $DB->query("UPDATE `pre_master` SET `income`=`income`-{$price1} where `zid`='{$row['sid']}'");
                        addMasterPointLogs($row['sid'], $price1, '扣款', '订单(ID' . $id . ')已退款, 扣除订单提成', $orderid);
                    }
                }

                $update = Db::name('orders')->where(['id' => $orderid])->update([
                    'status'       => 4,
                    'refund'       => 1,
                    'refund_price' => $refund,
                    'endtime'      => $date,
                    'status'       => 4,
                    'status'       => 4,
                ]);

                if ($update !== false) {
                    $result = [
                        'code'       => 0,
                        'msg'        => $msg . $up_bz,
                        'orderPrice' => $orderPrice,
                    ];
                } else {
                    $result = [
                        'code'       => 0,
                        'msg'        => $msg . $up_bz . '修改订单状态失败, ' . Db::error(),
                        'orderPrice' => $orderPrice,
                    ];
                }

            }
            exit(json_encode($result));
        } else {
            if ($status == 5) {
                $sql = "DELETE FROM pre_orders where id='{$orderid}'";
            } else {
                if ($result != "") {
                    $q = ",result='" . $result . "'";
                }

                if ($row['status'] == 3 && $status == 2) {
                    $q = ",result=''";
                }

                $sql = "UPDATE pre_orders set status='" . $status . "',`endtime`='{$date}'" . $q . " where id='{$orderid}'";
            }

            if ($DB->query($sql)) {
                $result = array('code' => 0, 'msg' => '操作成功！');
            } else {
                $result = array('code' => -1, 'msg' => '操作失败！' . $DB->error());
            }
        }
        exit(json_encode($result));
        break;
    case 'checkPwd': //验证社区加密数据
        checkAuthority('shequs');
        $pwd = input('post.pwd');
        $str = strSafeEnCode($pwd, "DECODE");
        exit('{"code":0,"msg":"解密内容为：' . $str . '"}');
        break;
    case 'rePrice': //分站重置商品价格
        checkAuthority('fenzhan');
        $zid = intval(input('get.zid'));
        $DB->query("UPDATE pre_site set price='' where zid= ?", array($zid));
        exit('{"code":0,"msg":"succ"}');
        break;
    case 'getModelList': //快捷模板列表
        $html = '';
        if (function_exists('getModelList')) {
            $html = getModelList();
        }
        exit($html);
        break;
    case 'getPridListSelect': //加价模板列表下拉框文本
        $html = '';
        if (function_exists('getPridList')) {
            $html = getPridList();
        }
        exit($html);
        break;
    case 'getClassListSelect': //分类列表下拉框文本
        $html = '';
        if (function_exists('getClassList')) {
            $html = getClassList();
        }
        exit($html);
        break;
    case 'setTools': // 商品上下架
        checkAuthority('shops');
        $tid = intval(input('get.tid'));
        $row = $DB->getRow("SELECT * FROM pre_tools WHERE tid= ?", [$tid]);
        if (!$row) {
            exit('{"code":-1,"msg":"该商品不存在，tid: ' . $tid . '"}');
        }
        if (isset($_GET['active'])) {
            $active = intval(input('get.active'));
            $downtime = 0;
            if ($active == 0) {
            $downtime = time();
        }
        addToolLogs($tid, $row['name'], $row['price'], $row['price'], $active == 1 ? '上架' : '下架');
        $DB->query("UPDATE pre_tools SET active='$active', downtime='$downtime' WHERE tid='{$tid}'");

        } else {
        $close = intval(input('get.close'));
        $downtime = 0;
        if ($close == 1) {
            $downtime = time();
        }
        $DB->query("UPDATE pre_tools SET close='$close', downtime='$downtime' WHERE tid='{$tid}'");
        }
    exit('{"code":0,"msg":"succ"}');
    break;
    case 'setRepeat': //商品重复下单
        checkAuthority('shops');
        $tid    = intval(input('tid'));
        $repeat = intval(input('repeat'));
        if ($DB->query("UPDATE `pre_tools` SET `repeat` = ? where tid= ?", [$repeat, $tid])) {
            exit('{"code":0,"msg":"succ"}');
        } else {
            exit('{"code":-1,"msg":"操作失败，' . $DB->error() . '","tid":"' . $tid . '"}');
        }
        break;
    case 'setToolSort': //商品排序操作
        checkAuthority('shops');
        $cid  = intval(input('get.cid', 1));
        $tid  = intval(input('get.tid'));
        $sort = intval(input('get.sort'));
        if (setToolSort($cid, $tid, $sort)) {
            exit('{"code":0,"msg":"succ"}');
        } else {
            exit('{"code":-1,"msg":"失败"}');
        }
        break;
    case 'setToolSortN': //商品排序操作新版
        checkAuthority('shops');
        $tids = input('tids');
        $DB->transaction();
        try {
            foreach ($tids as $i => $tid) {
                $DB->query("UPDATE pre_tools set sort='" . $i . "' where tid=?", [$tid]);
            }
            $DB->commit();
            exit('{"code":0,"msg":"succ"}');
        } catch (\Throwable $th) {
            $DB->rollback();
            exit('{"code":0,"msg":"' . $th->getMessage() . '"}');
        }
        break;
    case 'setClass': //分类上下架
        checkAuthority('class');
        $cid    = intval(input('get.cid', 1));
        $active = intval(input('get.active'));
        $DB->query("UPDATE pre_class set active='$active' where cid='{$cid}'");
        exit('{"code":0,"msg":"succ"}');
        break;
    case 'setClassSort': //分类排序操作
        checkAuthority('class');
        $cid  = intval(input('get.cid', 1));
        $sort = intval(input('get.sort'));
        if (setClassSort($cid, $sort)) {
            exit('{"code":0,"msg":"succ"}');
        } else {
            exit('{"code":-1,"msg":"失败"}');
        }
        break;
    case 'setClassSortN': //分类排序操作新版
        checkAuthority('class');
        $cids = input('cids');
        $DB->transaction();
        try {
            foreach ($cids as $i => $cid) {
                $DB->query("UPDATE pre_class set sort='" . $i . "' where cid=?", [$cid]);
            }
            $DB->commit();
            exit('{"code":0,"msg":"succ"}');
        } catch (\Throwable $th) {
            $DB->rollback();
            exit('{"code":0,"msg":"' . $th->getMessage() . '"}');
        }
        break;
    case 'setArticleSort': //文章排序操作
        checkAuthority('message');
        $id   = intval(input('get.id'));
        $sort = intval(input('get.sort'));
        if (setArticleSort($id, $sort)) {
            exit('{"code":0,"msg":"succ"}');
        } else {
            exit('{"code":-1,"msg":"失败,' . $DB->error() . '"}');
        }
        break;
    case 'article_operation': //文章排序操作
        checkAuthority('message');
        $aid   = intval(input('post.aid'));
        $list  = input('post.checkbox', 1);
        $count = 0;
        $ok    = 0;
        foreach ($list as $key => $id) {
            $count++;
            if ($aid == 4) {
                //删除
                $sql = $DB->query("DELETE FROM `pre_message` where `id`='{$id}'");
            } elseif ($aid == 3) {
                //显示
                $sql = $DB->query("UPDATE `pre_message` SET `active`=1 where `id`='{$id}'");
            } elseif ($aid == 2) {
                //隐藏
                $sql = $DB->query("UPDATE `pre_message` SET `active`=0 where `id`='{$id}'");
            }
            if ($sql) {
                $ok++;
            }
        }
        $result = array("code" => 0, "msg" => "共" . $count . "条，成功操作" . $ok . "条数据");
        exit(json_encode($result));
        break;
    case 'link_operation': 
        checkAuthority('wplink');
        $aid   = intval(input('post.aid'));
        $count = 0;
        $ok    = 0;
        if ($aid == 4) {

            $list  = input('post.checkbox', 1);
            foreach ($list as $key => $id) {
                $count++;
                if ($aid == 4) {
                    //删除
                    $sql = $DB->query("DELETE FROM `pre_pro_link` where `id`='{$id}'");
                }
                if ($sql) {
                    $ok++;
                }
            }
            $result = array("code" => 0, "msg" => "共" . $count . "条，成功操作" . $ok . "条数据");
            exit(json_encode($result));
        }elseif ($aid == 1) {
            $status  = input('post.status', 1);
            $id    = input('post.id', 1);
            $id=$id+0;
            if($id<1){
                exit('{"code":-1,"msg":"参数错误"}');
            }
            if(empty($status)){
                exit('{"code":-1,"msg":"状态不能为空"}');
            }
            if ($DB->query("UPDATE `pre_pro_link` set updatetime = ?,status=? where id= ?", [time(),$status, $id])) {
                exit('{"code":0,"msg":"处理成功"}');
            } else {
                exit('{"code":0,"msg":"操作失败，' . $DB->error() . '"}');
            }
        }

        $result = array("code" => 0, "msg" => "共" . $count . "条，成功操作" . $ok . "条数据");
        exit(json_encode($result));
        break;

    case 'report_operation': 
        checkAuthority('report');
        $aid   = intval(input('post.aid'));
        $count = 0;
        $ok    = 0;
        if ($aid == 4) {

            $list  = input('post.checkbox', 1);
            foreach ($list as $key => $id) {
                $count++;
                if ($aid == 4) {
                    //删除
                    $sql = $DB->query("DELETE FROM `pre_pro_report` where `id`='{$id}'");
                }
                if ($sql) {
                    $ok++;
                }
            }
            $result = array("code" => 0, "msg" => "共" . $count . "条，成功操作" . $ok . "条数据");
            exit(json_encode($result));
        }elseif ($aid == 1) {
            $status  = input('post.status', 1);
            $id    = input('post.id', 1);
            $id=$id+0;
            if($id<1){
                exit('{"code":-1,"msg":"参数错误"}');
            }
            if(empty($status)){
                exit('{"code":-1,"msg":"状态不能为空"}');
            }
            if ($DB->query("UPDATE `pre_pro_report` set updatetime = ?,status=? where id= ?", [time(),$status, $id])) {
                exit('{"code":0,"msg":"处理成功"}');
            } else {
                exit('{"code":0,"msg":"操作失败，' . $DB->error() . '"}');
            }
        }

        $result = array("code" => 0, "msg" => "共" . $count . "条，成功操作" . $ok . "条数据");
        exit(json_encode($result));
        break;

    case 'tools_complaint': 
        checkAuthority('complaint');
        $aid   = intval(input('post.aid'));
        $count = 0;
        $ok    = 0;
        if ($aid == 4) {
            $list  = input('post.checkbox', 1);
            foreach ($list as $key => $id) {
                $count++;
                if ($aid == 4) {
                    //删除
                    $sql = $DB->query("DELETE FROM `pre_pro_complaint` where `id`='{$id}'");
                }
                if ($sql) {
                    $ok++;
                }
            }
            $result = array("code" => 0, "msg" => "共" . $count . "条，成功操作" . $ok . "条数据");
            exit(json_encode($result));
        }elseif ($aid == 1) {
            $status  = input('post.status', 1);
            $id    = input('post.id', 1);
            $id=$id+0;
            if($id<1){
                exit('{"code":-1,"msg":"参数错误"}');
            }
            if(empty($status)){
                exit('{"code":-1,"msg":"状态不能为空"}');
            }
            if ($DB->query("UPDATE `pre_pro_complaint` set updatetime = ?,status=? where id= ?", [time(),$status, $id])) {
                exit('{"code":0,"msg":"处理成功"}');
            } else {
                exit('{"code":0,"msg":"操作失败，' . $DB->error() . '"}');
            }
        }

        $result = array("code" => 0, "msg" => "共" . $count . "条，成功操作" . $ok . "条数据");
        exit(json_encode($result));
        break;
        
    case 'getPridList':
        if ($_SESSION['priceselectJson'] && $data = json_decode($_SESSION['priceselectJson'])) {
            $result = array("code" => 0, "msg" => "succ", "data" => $data);
        } else {
            $rs   = $DB->query('SELECT * FROM pre_price order by id desc');
            $data = [];
            if ($rs) {
                $data = $DB->fetchAll($rs);
            }
            $result = array("code" => 0, "msg" => "succ", "data" => $data);
        }
        exit(json_encode($result));
        break;
    case 'getSpecsInfo':
        $tid               = intval(input('post.tid'));
        $tid > 0 && $count = $DB->count("SELECT count(*) FROM pre_stock where tid= ?", [$tid]);
        if ($tid > 0 && $count > 0) {
            $rs   = $DB->query("SELECT * FROM pre_stock where tid= ?", [$tid]);
            $data = [];
            if ($rs) {
                $data = $DB->fetchAll($rs);
            }
        } else {
            $data      = [];
            $specs_id  = intval(input('post.specs_id', 1));
            $specs_row = $DB->get_row("SELECT * FROM `pre_specs` where id= ?", [$specs_id]);
            if ($specs_row) {
                $data = json_decode($specs_row['obj'], true);
            }
        }
        $result = array("code" => 0, "msg" => "succ", "data" => $data);
        exit(json_encode($result));
        break;

    case 'getGoodsList': //获取对接商品列表
        checkAuthority('shops');
        $shequ = intval(input('post.shequ'));
        $cid   = input('post.cid', 1);
        $page  = intval(input('post.page', 1));
        if (!$cid) {
            $cid = input('post.category_id', 1);
        }

        $row = $DB->get_row("SELECT * from pre_shequ where id=:shequ limit 1", [':shequ' => $shequ]);
        if (!$row) {
            exit('{"code":-1,"msg":"该社区不存在或已被删除！"}');
        }

        if (!preg_match('/^(https|http):\/\/[\w\.\-]+\.[\w\:\/]+\/$/', $row["url"])) {
            preg_match('/[\w\.\-]+\.[\w\:]+/', $row["url"], $arr);
            if ($row["ssl"] == 1) {
                $row["url"] = 'https://' . $arr[0] . '/';
            } else {
                $row["url"] = 'http://' . $arr[0] . '/';
            }
        }

        try {
            $list = getGoods_extend($row, $cid, $page);
            if (is_array($list)) {
                $list['type'] = getShequType($row['type']);
                exit(json_encode($list));
            } else {
                exit($list);
            }
        } catch (\Throwable $th) {
            exit(json_encode(['code' => -1, 'msg' => '插件错误，' . $th->getMessage()], 256));
        }
        break;
    case 'getGoodsParam': //获取对接参数名
        checkAuthority('shops');
        $shequ   = intval(input('shequ', 1));
        $tid     = intval(input('post.tid', 1));
        $goodsid = input('post.goodsid', 1);

        $row = $DB->get_row("SELECT * from pre_shequ where id= ? limit 1", [$shequ]);
        if (!$goodsid) {
            $result = ['code' => -1, 'msg' => "商品ID不能为空！"];
        } elseif (is_array($row)) {
            $result = null;
            if (!preg_match('/^(https|http):\/\/[\w\.\-]+\.[\w\:\/]+\/$/', $row["url"])) {
                preg_match('/[\w\.\-]+\.[\w\:]+/', $row["url"], $arr);
                if ($row["ssl"] == 1) {
                    $row["url"] = 'https://' . $arr[0] . '/';
                } else {
                    $row["url"] = 'http://' . $arr[0] . '/';
                }
            }

            try {
                $result = getGoodsParams_extend($row, $goodsid);
                if (is_array($result)) {
                    $result['type'] = getShequType($row['type']);
                } else {
                    if ($result === null) {
                        $result = ['code' => -1, 'msg' => "该货源平台类型不支持！"];
                    } else {
                        $result = [
                            'code'   => -1,
                            'msg'    => "获取商品详情参数失败，请检查对接站是否异常",
                            'result' => $result,
                        ];
                    }
                }
            } catch (\Throwable $th) {
                $result = ['code' => -1, 'msg' => '插件错误，' . $th->getMessage()];
            }
        } else {
            $result = ['code' => -1, 'msg' => "该社区数据不存在或缺少[shequ]参数！"];
        }

        exit(json_encode($result));
        break;
    case 'getGoodsclass': //获取卡易信分类列表
        checkAuthority('shops');
        $shequ = intval(input('post.shequ'));
        $row   = $DB->get_row("SELECT * from pre_shequ where id= ? limit 1", array($shequ));
        if ($row) {
            if (!preg_match('/^(https|http):\/\/[\w\.\-]+\.[\w\:\/]+\/$/', $row["url"])) {
                preg_match('/[\w\.\-]+\.[\w\:]+/', $row["url"], $arr);
                if ($row["ssl"] == 1) {
                    $row["url"] = 'https://' . $arr[0] . '/';
                } else {
                    $row["url"] = 'http://' . $arr[0] . '/';
                }
            }
            try {
                if ($row['type'] == 6) {
                    $result = getkayixingoods($row);
                    $result = filtertext($result);
                    preg_match_all("/<td(.*)<\/td>/U", $result, $data1);
                    $data1 = $data1[1];
                    $data2 = [];
                    foreach ($data1 as $value) {
                        preg_match("/id=\"(.*)\"/U", $value, $id);
                        preg_match("/href=\"(.*)\"/U", $value, $herf);
                        preg_match("/id=(.*)<\/a>/U", $value, $title);
                        $id    = $id[1] ? $id[1] : "";
                        $herf  = $herf[1] ? $herf[1] : "";
                        $title = $title[0] ? $title[0] : "";
                        if ($title) {
                            preg_match("/>(.*)<\/a>/U", $title, $title);
                        }

                        $title = $title[1] ? $title[1] : "";

                        if ($id && $herf && $title) {
                            $data2[] = array("id" => $id, "name" => $title, "url" => $herf);
                        }
                    }
                    $num = count($data2);
                    if ($num) {
                        $result = ['code' => 0, 'msg' => '获取成功，共' . $num . '个目录', "data" => $data2];
                    } else {
                        $result = ['code' => -1, 'msg' => '数据解析失败，' . htmlspecialchars($result)];
                    }
                } else {
                    $result         = getGoodsCategory_extend($row);
                    $result['type'] = getShequType($row['type']);
                }
            } catch (\Throwable $th) {
                $result         = ['code' => -1, 'msg' => '插件错误，' . $th->getMessage()];
                $result['type'] = getShequType($row['type']);
            }
        } else {
            $result = ['code' => -1, 'msg' => '该社区数据不存在！'];
        }
        exit(json_encode($result));
        break;
    case 'getkxyGoodslist': //获取卡易信商品列表
        checkAuthority('shops');
        $shequ = intval(input('post.shequ'));
        $row   = $DB->get_row("SELECT * from pre_shequ where id= ? limit 1", array($shequ));
        if (!preg_match('/^(https|http):\/\/[\w\.\-]+\.[\w\:\/]+\/$/', $row["url"])) {
            preg_match('/[\w\.\-]+\.[\w\:]+/', $row["url"], $arr);
            if ($row["ssl"] == 1) {
                $row["url"] = 'https://' . $arr[0] . '/';
            } else {
                $row["url"] = 'http://' . $arr[0] . '/';
            }
        }
        $dirId = "dirId=" . daddslashes(input('post.dirId'));
        if ($row['type'] != 6) {
            exit('{"code":-1,"msg":"社区类型错误，此接口为卡易信专用"}');
        }

        $data2 = [];
        $num1  = 20;
        $page  = 1;
        while ($num1 > 19) {
            $num1 = 0;
            $data = getkayixinlist($row, $dirId, $page);
            $data = filtertext($data);
            preg_match_all("/<tr(.*)<\/tr>/U", $data, $data1);
            $data1 = $data1[1];
            foreach ($data1 as $value) {
                preg_match("/OpenTemplate\(\'(.*)\'/U", $value, $post1);
                $post1 = $post1[1] ? $post1[1] : "";
                preg_match("/<font(.*)<\/font>/U", $value, $title);
                $title = $title[0] ? $title[0] : "";
                if ($title) {
                    preg_match("/>(.*)<\/font>/U", $title, $title);
                }

                $title = $title[1] ? $title[1] : "";

                preg_match("/aid=\"(.*)\"/U", $value, $id);
                $id = $id[1];
                if ($post1 && $id && $title && $dirId) {
                    $num1++;
                    $data2[] = array("name" => $title, "url" => $row['url'] . "front/inter/" . $post1 . "&" . $dirId, "id" => $id);
                }
            }
            $page++;
        }
        $num = count($data2);
        if ($num) {
            exit(json_encode(array("code" => 0, "msg" => "商品数据获取成功！,共" . $num . "个商品！", "type" => "kayixin", "data" => $data2)));
        } else {
            exitmsg(-1, "获取失败");
        }

        break;
    case 'getkyxgoods': //获取卡易信商品详情
        checkAuthority('shops');
        $shequ    = intval(input('post.shequ'));
        $val_tool = $shequ;
        $row      = $DB->get_row("SELECT * from pre_shequ where id= ? limit 1", array($shequ));
        if (!preg_match('/^(https|http):\/\/[\w\.\-]+\.[\w\:\/]+\/$/', $row["url"])) {
            preg_match('/[\w\.\-]+\.[\w\:]+/', $row["url"], $arr);
            if ($row["ssl"] == 1) {
                $row["url"] = 'https://' . $arr[0] . '/';
            } else {
                $row["url"] = 'http://' . $arr[0] . '/';
            }
        }
        $url = daddslashes(input('post.url'));
        if ($row['type'] != 6) {
            exit('{"code":-1,"msg":"社区类型错误，此接口为卡易信专用"}');
        }

        $text = getkayixingoodsinfo($row, $url);
        $data = filtertext($text);
        preg_match("/body(.*)<script/U", $data, $data1);
        $data = $data1[1];
        if (preg_match("/td-righttd-buy-good-name\">(.*)<\/td>/U", $data, $name)) {
            preg_match("/\"content\">(.*)<\/div>/U", $data, $content);
            preg_match("/saleprice\"value=\"(.*)\"/U", $data, $price);
            preg_match("/充值账号(.*)accountName/U", $data, $input);
            preg_match("/td-left\">(.*)</U", $input[1], $input);
            $input = $input[1] ? $input[1] : "";
            preg_match("/充值信息(.*)充值数量/U", $data, $inputs);
            preg_match_all("/id=\"temptypeName(.*)<\/td>/U", $inputs[1], $inputs);
            $inputtxt  = "";
            $inputtxt1 = [];
            $inputs    = $inputs[1];
            if (is_array($inputs)) {
                foreach ($inputs as $value) {
                    $value .= "\"";
                    preg_match("/>(.*)\"/U", $value, $inputtxt1);
                    $inputtxt = $inputtxt1[1] ? $inputtxt . "|" . $inputtxt1[1] : $inputtxt;
                }
            }
            $inputtxt = ltrim($inputtxt, "|");
            $name     = $name[1] ? $name[1] : "";
            $price    = $price[1] ? $price[1] : "";
            $content  = $content[1] ? $content[1] : "";
        }

        if ($name && $price) {
            $result = ["name" => $name, "price" => $price, "content" => $content, "code" => 0, "msg" => "succ", "url" => $url, "input" => $input, "inputs" => $inputtxt];
        } else {
            $text   = mb_substr($text, 0, 300);
            $result = ["code" => -1, "获取失败，" . $text, "url" => $url];
        }
        exit(json_encode($result));
        break;
    case 'kyxmoney': //获取卡易信余额
        checkAuthority('shops');
        $shequ    = intval(input('get.shequ'));
        $val_tool = $shequ;
        $row      = $DB->get_row("SELECT * from pre_shequ where id= ? limit 1", array($shequ));
        if (!preg_match('/^(https|http):\/\/[\w\.\-]+\.[\w\:\/]+\/$/', $row["url"])) {
            preg_match('/[\w\.\-]+\.[\w\:]+/', $row["url"], $arr);
            if ($row["ssl"] == 1) {
                $row["url"] = 'https://' . $arr[0] . '/';
            } else {
                $row["url"] = 'http://' . $arr[0] . '/';
            }
        }
        if ($row['type'] != 6) {
            exit('{"code":-1,"msg":"社区类型错误，此接口为卡易信专用"}');
        }

        $data = getkayixinmoney($row);
        $data = json_decode($data);
        if (!$data->money) {
            exitmsg(-1, "未查询到余额");
        }
        exit(json_encode(array("code" => 0, "msg" => round($data->money, 2))));
        break;
    case 'cancelWorks': //撤销工单回复
        checkAuthority('works');
        $id  = intval(input('get.id'));
        $row = $DB->get_row("SELECT * from pre_workorder where id= ? limit 1", array($id));
        if (!$row) {
            $result = array("code" => -1, "msg" => "当前工单记录不存在！");
        } else {
            $contents = explode('*', $row['content']);
            $num      = count($contents);
            foreach ($contents as $i => $value) {
                if ($i == $num - 1) {
                    continue;
                }

                $content .= $value . '*';
            }
            $content = trim($content, '*');
            if ($DB->query("UPDATE pre_workorder set `content`= ? where id= ?", array($content, $id))) {
                $result = array("code" => 0, "msg" => "succ");
            } else {
                $result = array("code" => 0, "msg" => '操作失败，' . $DB->error());
            }
        }

        exit(json_encode($result));
        break;
    case 'getfakatool': //获取发卡商品
        $cid  = intval(input('get.cid', 1));
        $rs   = $DB->query("SELECT * FROM pre_tools WHERE cid= ? and is_curl=4 and active=1 order by sort asc", [$cid]);
        $data = array();
        while ($res = $DB->fetch($rs)) {
            $data[] = array('tid' => $res['tid'], 'name' => $res['name']);
        }
        $result = array("code" => 0, "msg" => "succ", "data" => $data);
        exit(json_encode($result));
        break;
    case 'orderInfo':
        checkAuthority('orders');
        $id  = intval(input('get.id'));
        $row = $DB->get_row("SELECT * from pre_orders where id= ? limit 1", [$id]);
        if ($row) {
            $result = array("code" => 0, "msg" => "succ", "row" => $row);
        } else {
            $result = array("code" => -1, "msg" => "该订单数据不存在！", "id" => $id);
        }
        exit(json_encode($result));
        break;
    case 'setHidePays':
        checkAuthority('shops');
        $cid      = intval(input('cid', true));
        $paytype  = input('paytype', true);
        $hidepays = implode(',', $paytype);
        $DB->query("UPDATE pre_class set hidepays = ? where cid= ?", [$hidepays, $cid]);
        exit('{"code":0,"msg":"操作成功"}');
        break;
    case 'getHidePays':
        checkAuthority('shops');
        $cid = intval(input('post.cid'));
        $row = $DB->get_row("SELECT * from pre_class where cid= ? limit 1", [$cid]);
        if (!$row) {
            exit('{"code":-1,"msg":"当前分类不存在！"}');
        }

        $hidepays = explode(',', $row['hidepays']);
        $result   = array("code" => 0, "msg" => "succ", "data" => $hidepays);
        exit(json_encode($result));
        break;
    case 'setBlockCitys':
        checkAuthority('shops');
        $cid       = intval(input('post.cid'));
        $blockcity = input('blockcity', true);
        if ($DB->query("UPDATE pre_class set blockcity = ? where cid= ?", [$blockcity, $cid])) {
            exit('{"code":0,"msg":"操作成功"}');
        } else {
            exit('{"code":0,"msg":"操作失败，' . $DB->error() . '"}');
        }
        break;
    case 'getBlockCitys':
        checkAuthority('shops');
        $cid = intval(input('post.cid'));
        $row = $DB->get_row("SELECT * from pre_class where cid= ? limit 1", [$cid]);
        if (!$row) {
            exit('{"code":-1,"msg":"当前分类不存在！"}');
        }

        $result = array("code" => 0, "msg" => "succ", "content" => $row['blockcity']);
        exit(json_encode($result));
        break;
    case 'setLoginShow':
        checkAuthority('shops');
        $cid = intval(input('post.cid'));
        $row = $DB->get_row("SELECT * from pre_class where cid= ? limit 1", [$cid]);
        if (!$row) {
            exit('{"code":-1,"msg":"当前分类不存在！"}');
        }
        $sql  = "UPDATE pre_class set `islogin`=:islogin where `cid`=:cid";
        $data = [
            ':islogin' => $row['islogin'] == 1 ? 0 : 1,
            ':cid'     => $cid,
        ];
        if ($DB->query($sql, $data)) {
            $result = [
                "code" => 0,
                "msg"  => $row['islogin'] == 1 ? '取消登录可见成功' : '设置登录可见成功',
            ];
        } else {
            $result = [
                "code" => -1,
                "msg"  => "操作失败，" . $DB->error(),
            ];
        }
        exit(json_encode($result));
        break;
    case 'setDisabled':
        checkAuthority('shops');
        $cid = intval(input('post.cid'));
        $row = $DB->get_row("SELECT * from pre_class where cid= ? limit 1", [$cid]);
        if (!$row) {
            exit('{"code":-1,"msg":"当前分类不存在！"}');
        }
        $sql  = "UPDATE pre_class set `isdisabled`=:isdisabled where `cid`=:cid";
        $data = [
            ':isdisabled' => $row['isdisabled'] == 1 ? 0 : 1,
            ':cid'     => $cid,
        ];
        if ($DB->query($sql, $data)) {
            $result = [
                "code" => 0,
                "msg"  => $row['isdisabled'] == 1 ? '取消禁用成功' : '设置禁用成功',
            ];
        } else {
            $result = [
                "code" => -1,
                "msg"  => "操作失败，" . $DB->error(),
            ];
        }
        exit(json_encode($result));
        break;
    case 'setDis_banned':
        checkAuthority('shops');
        $tid = intval(input('post.tid'));
        $row = $DB->get_row("SELECT * from pre_tools where tid= ? limit 1", [$tid]);
        if (!$row) {
            exit('{"code":-1,"msg":"当前分类不存在！"}');
        }
        $sql  = "UPDATE pre_tools set `is_banned`=:is_banned where `tid`=:tid";
        $data = [
            ':is_banned' => $row['is_banned'] == 1 ? 0 : 1,
            ':tid'     => $tid,
        ];
        if ($DB->query($sql, $data)) {
            $result = [
                "code" => 0,
                "msg"  => $row['is_banned'] == 1 ? '取消禁用成功' : '设置禁用成功',
            ];
        } else {
            $result = [
                "code" => -1,
                "msg"  => "操作失败，" . $DB->error(),
            ];
        }
        exit(json_encode($result));
        break;
    case 'setAccount': //开启关闭管理员
        checkAuthority('super');
        $aid    = intval(input('get.aid'));
        $status = intval(input('get.status'));
        $DB->query("UPDATE pre_admin set status= ? where aid= ?", [$status, $aid]);
        exit('{"code":0,"msg":"succ"}');
        break;
    case 'setSite': //开启关闭站点
        checkAuthority('fenzhan');
        $zid    = intval(input('get.zid'));
        $active = intval(input('get.active'));
        $DB->query("UPDATE pre_site set status= ? where zid= ?", [$active, $zid]);
        exit('{"code":0,"msg":"succ"}');
        break;
    case 'setSuper': //切换站点版本
        checkAuthority('fenzhan');
        $zid = intval(input('get.zid'));
        $row = $DB->get_row("SELECT * from pre_site where zid= ? limit 1", [$zid]);
        if ($row['power'] == 2) {
            $power = 1;
        } else {
            $power = 2;
        }

        $DB->query("UPDATE pre_site set power= ? where zid= ?", [$power, $zid]);
        exit('{"code":0,"msg":"succ"}');
        break;
    case 'setPriced': //切换站点设置差价状态
        checkAuthority('fenzhan');
        $zid       = intval(input('get.zid'));
        $is_priced = intval(input('get.is_priced'));
        $DB->query("UPDATE pre_site set is_priced= ? where zid= ?", array($is_priced, $zid));
        exit('{"code":0,"msg":"succ"}');
        break;
    case 'delSuperPirce': //删除密价
        $mid = intval(input('get.mid'));
        if ($DB->query("DELETE from `pre_price_super` where mid= ?", [$mid])) {
            $result = array("code" => 0, "msg" => "succ");
        } else {
            $result = array("code" => 0, "msg" => "删除失败，" . $DB->error());
        }
        exit(json_encode($result));
        break;
    case 'superPriceList': //获取密价列表
        checkAuthority('super');
        $rs   = $DB->query("SELECT * from `pre_price_super` where `status`='1'");
        $data = [];
        $list = '';
        if ($rs) {
            $data = $rs->fetchAll();
            foreach ($data as $row) {
                $list .= '<tr><td>
                <input class="inp-cmckb-xs" name="superPirceId" id="mid' . $row['mid'] . '" type="checkbox" onclick="selectMid(this.value)" value="' . $row['mid'] . '" style="display: none;"/>
                    <label class="cmckb-xs" for="mid' . $row['mid'] . '"><span>
                      <svg width="12px" height="10px">
                        <use xlink:href="#check"></use>
                      </svg>
                    </span><span>' . $row['mid'] . '</span></label>
                </div>
                </td>
                <td>' . $row['name'] . '</td>
                <td>成本价' . ($row['type'] == 1 ? '+' . $row['cost'] . '元' : '+' . $row['cost'] . '%') . '</td>
                <td>' . $row['bz'] . '</td>
                </tr>';
            }
            $result = array("code" => 0, "msg" => "succ", "list" => $list);
        } else {
            $result = array("code" => -1, "msg" => "获取密价列表失败，" . $DB->error(), "list" => $list);
        }
        exit(json_encode($result));
        break;
    case 'setSuperPrice': //设置密价
        checkAuthority('fenzhan');
        $mid = intval(input('mid', true));
        $zid = intval(input('zid', true));
        $row = $DB->get_row("SELECT * from pre_site where zid= ? limit 1", [$zid]);
        if (!$row) {
            exit('{"code":-1,"msg":"当前分站不存在！"}');
        }
        if ($mid < 1) {
            $mid = 0;
        }
        $sql = $DB->query("UPDATE pre_site set `mid`=:mid where zid=:zid", [':mid' => $mid, ':zid' => $zid]);
        if ($sql) {
            exit('{"code":0,"msg":"设置密价成功"}');
        } else {
            exit('{"code":-1,"msg":"设置密价失败，' . $DB->error() . '"}');
        }

        break;
    case 'setEndtime': //分站延时
        checkAuthority('fenzhan');
        $zid   = intval(input('post.zid'));
        $month = intval(input('post.month'));
        $row   = $DB->get_row("SELECT * from pre_site where zid='$zid' limit 1");
        if ($row['endtime'] > date("Y-m-d")) {
            $endtime = date("Y-m-d", strtotime("+ {$month} months", strtotime($row['endtime'])));
        } else {
            $endtime = date("Y-m-d", strtotime("+ {$month} months"));
        }

        $DB->query("UPDATE pre_site set endtime='$endtime' where zid='{$zid}'");
        exit('{"code":0,"msg":"succ"}');
        break;
    case 'siteRecharge': //分站充值
        checkAuthority('fenzhan');
        $zid    = intval(input('post.zid'));
        $action = intval(input('post.action'));
        $do     = intval(input('post.actdo'));
        $money  = floatval(input('post.money'));
        $bz     = trim(input('post.bz'));
        $row    = $DB->get_row("SELECT * from pre_site where zid='$zid' limit 1");
        if (!$row) {
            exit('{"code":-1,"msg":"当前分站不存在！"}');
        }

        if ($do == 1 && $action == 1 && $money > $row['money']) {
            $money = $row['money'];
        }

        if ($do == 1 && $action == 2 && $money > $row['point']) {
            $money = $row['point'];
        }

        if ($do == 0) {
            if ($bz) {
                $bz = ',原因：' . $bz;
            }

            if ($action == 2) {
                $bz = '后台加款到提成' . $money . '元' . $bz . '！当前提成' . ($row['point'] + $money) . '元';
                $DB->query("UPDATE pre_site set point=point+{$money} where zid='{$zid}'");
            } else {
                $bz = '后台加款到余额' . $money . '元' . $bz . '！当前余额' . ($row['money'] + $money) . '元';
                $DB->query("UPDATE pre_site set money=money+{$money} where zid='{$zid}'");
            }

            addPointLogs($zid, $money, '加款', $bz, null);
        } else {
            if ($bz) {
                $bz = ',原因：' . $bz;
            }

            if ($action == 2) {
                $bz = '后台扣款到提成' . $money . '元' . $bz . '！当前提成' . ($row['point'] - $money) . '元';
                $DB->query("UPDATE pre_site set point=point-{$money} where zid='{$zid}'");
            } else {
                $bz = '后台扣款到余额' . $money . '元' . $bz . '！当前余额' . ($row['money'] - $money) . '元';
                $DB->query("UPDATE pre_site set money=money-{$money} where zid='{$zid}'");
            }

            addPointLogs($zid, $money, '扣除', $bz, null);
        }
        exit('{"code":0,"msg":"succ"}');
        break;
    case 'setMessage': //站内通知状态
        checkAuthority('message');
        $id     = intval(input('get.id'));
        $active = intval(input('get.active'));
        $DB->query("UPDATE pre_message set active='$active' where id='{$id}'");
        exit('{"code":0,"msg":"succ"}');
        break;
    case 'getMessage': //查看站内通知
        checkAuthority('message');
        $id  = intval(input('get.id'));
        $row = $DB->get_row("SELECT * from pre_message where id='$id' limit 1");
        if (!$row) {
            exit('{"code":-1,"msg":"当前通知不存在！"}');
        }

        $result = array("code" => 0, "msg" => "succ", "title" => $row['title'], "type" => $row['type'], "content" => $row['content'], "date" => $row['addtime']);
        exit(json_encode($result));
        break;
    case 'delFakaKms': //删除发卡卡密
        checkAuthority('fakas');
        $tid  = intval(input('post.tid'));
        $list = explode(',', trim(input('post.list')));
        $ok   = 0;
        foreach ($list as $kid) {
            if ($kid < 1) {
                continue;
            }

            $sql = "delete from `pre_faka` where kid = ?";
            if ($DB->query($sql, array($kid))) {
                $ok++;
            }
        }
        exit('{"code":0,"msg":"共删除' . $ok . '个**卡密"}');
        break;
    case 'addPriceRule': //添加加价模板
        $name = trim(daddslashes(input('post.name')));
        $kind = intval(input('post.kind'));
        $p_2  = trim(daddslashes(input('post.p_2')));
        $p_1  = trim(daddslashes(input('post.p_1')));
        $p_0  = trim(daddslashes(input('post.p_0')));
        if ($name == null || $p_2 == null || $p_1 == null || $p_0 == null) {
            exit('{"code":-1,"msg":"请确保各项不能为空！"}');
        } elseif ($p_2 > $p_1) {
            exit('{"code":-1,"msg":"旗舰版加价不能高于专业版加价"}');
        } elseif ($p_2 > $p_0) {
            exit('{"code":-1,"msg":"旗舰版加价不能高于普通用户加价"}');
        } elseif ($p_1 > $p_0) {
            exit('{"code":-1,"msg":"专业版加价不能高于普通用户加价"}');
        } elseif ($DB->get_row("SELECT * from pre_price where name='$name' limit 1")) {
            exit('{"code":-1,"msg":"模板名称已存在"}');
        }
        $sql = "insert into `pre_price` (`kind`,`name`,`p_0`,`p_1`,`p_2`) values ('" . $kind . "','" . $name . "','" . $p_0 . "','" . $p_1 . "','" . $p_2 . "')";
        if ($DB->query($sql)) {
            $CACHE->clear('pricerules');
            exit('{"code":0,"msg":"添加加价模板成功！"}');
        } else {
            exit('{"code":-1,"msg":"添加加价模板失败！' . $DB->error() . '"}');
        }
        break;
    case 'editPriceRule': //修改加价模板
        $id   = intval(input('post.prid'));
        $name = trim(daddslashes(input('post.name')));
        $kind = intval(input('post.kind'));
        $p_2  = trim(daddslashes(input('post.p_2')));
        $p_1  = trim(daddslashes(input('post.p_1')));
        $p_0  = trim(daddslashes(input('post.p_0')));
        if ($name == null || $p_2 == null || $p_1 == null || $p_0 == null) {
            exit('{"code":-1,"msg":"请确保各项不能为空！"}');
        } elseif ($p_2 > $p_1) {
            exit('{"code":-1,"msg":"旗舰版加价不能高于专业版加价"}');
        } elseif ($p_2 > $p_0) {
            exit('{"code":-1,"msg":"旗舰版加价不能高于普通用户加价"}');
        } elseif ($p_1 > $p_0) {
            exit('{"code":-1,"msg":"专业版加价不能高于普通用户加价"}');
        } elseif ($DB->get_row("SELECT * from pre_price where id!=$id and name='$name' limit 1")) {
            exit('{"code":-1,"msg":"模板名称已存在"}');
        }
        $sql = "UPDATE pre_price set kind='$kind',name='$name',p_2='$p_2',p_1='$p_1',p_0='$p_0' where id='{$id}'";
        if ($DB->query($sql)) {
            $CACHE->clear('pricerules');
            exit('{"code":0,"msg":"修改加价模板成功！"}');
        } else {
            exit('{"code":-1,"msg":"修改加价模板失败！' . $DB->error() . '"}');
        }
        break;
    case 'getPriceRule':
        $id          = intval(input('get.id'));
        $row         = $DB->get_row("SELECT * from pre_price where id= ? limit 1", [$id]);
        $row['code'] = 0;
        exit(json_encode($row));
        break;
    case 'delPriceRule':
        $id  = intval(input('get.id'));
        $sql = "DELETE FROM pre_price WHERE id='$id' limit 1";
        if ($DB->query($sql)) {
            $CACHE->clear('pricerules');
            exit('{"code":0,"msg":"删除成功！"}');
        } else {
            exit('{"code":-1,"msg":"删除失败！' . $DB->error() . '"}');
        }
        break;
    case 'workorder_change':
        checkAuthority('works');
        $aid      = input('status');
        $checkbox = explode("|", trim(input('post.checkbox')));
        $content  = daddslashes(input('post.content'));
        $i        = 0;
        foreach ($checkbox as $id) {
            $i++;
            if ($aid == 1) {
                if ($DB->query("UPDATE pre_workorder set status=1,endtime= ? where id= ? limit 1", [$date, $id])) {
                    $ok++;
                }
            } elseif ($aid == 2) {
                $rows    = $DB->get_row("SELECT * from pre_workorder where `id`=:id limit 1", [':id' => $id]);
                $content = input('post.content', 0);
                $content = str_replace(array('*', '^', '|'), '', $content);
                if ($rows && $rows['status'] != 1 && !empty($content)) {
                    $content = addslashes($rows['content']) . '*1^' . $date . '^' . $content;
                    $sql     = "UPDATE pre_workorder set `content`=:content,`endtime`=:endtime,`status`=:status where `id`=:id";
                    $param   = [
                        ':content' => $content,
                        ':endtime' => $date,
                        ':status'  => 2,
                        ':id'      => $id,
                    ];
                    if ($DB->exec($sql, $param)) {
                        $ok++;
                    }
                }
            } elseif ($aid == 0) {
                if ($DB->query("UPDATE pre_workorder set status=0 where id= ? limit 1", [$id])) {
                    $ok++;
                }
            } elseif ($aid == 4) {
                if ($DB->query("DELETE FROM pre_workorder WHERE id= ? limit 1", [$id])) {
                    $ok++;
                }
            }
        }
        exit('{"code":0,"msg":"共' . $i . '条工单数据，成功改变' . $ok . '条"}');
        break;
    case 'delworkorder':
        checkAuthority('works');
        $id  = intval(input('get.id'));
        $sql = "DELETE FROM pre_workorder WHERE id= ? limit 1";
        if ($DB->query($sql, [$id])) {
            exit('{"code":0,"msg":"删除成功！"}');
        } else {
            exit('{"code":-1,"msg":"删除失败！' . $DB->error() . '"}');
        }
        break;
    case 'setTemplate':
        checkAuthority('super');
        $template = input('post.template');
        saveSetting('template', $template);
        $ad = $CACHE->clear();
        if ($ad) {
            exit('{"code":0,"msg":"succ"}');
        } else {
            exit('{"code":-1,"msg":"更换失败，' . $DB->error() . '"}');
        }
        break;
    case 'setTemplateMobile':
        checkAuthority('super');
        $template = input('post.template');
        saveSetting('template_mobile', $template);
        $ad = $CACHE->clear();
        if ($ad) {
            exit('{"code":0,"msg":"succ"}');
        } else {
            exit('{"code":-1,"msg":"更换失败，' . $DB->error() . '"}');
        }
        break;
    case 'pay_filler':
        $trade_no = input('post.trade_no', 1);
        $row      = $DB->get_row("SELECT * FROM `pre_pay` where `trade_no`=:trade_no", [':trade_no' => $trade_no]);

        if (is_array($row)) {
            $row2 = $DB->get_row("SELECT * FROM `pre_orders` where `payorder`=:trade_no", [':trade_no' => $trade_no]);
        }

        if (!$row) {
            $result = ['code' => -1, 'msg' => '该条数据不存在！'];
        } elseif (empty($trade_no)) {
            $result = ['code' => -1, 'msg' => '支付订单号不能为空！'];
        } elseif ($row['status'] == 1 && isset($row2['id'])) {
            $result = ['code' => -1, 'msg' => '该订单无需补单，订单编号' . $row2['id']];
        } else {
            $sql = "UPDATE `pre_pay` SET `status`='1' where `trade_no`=:trade_no";
            if ($DB->query($sql, [':trade_no' => $trade_no])) {
                require_once SYSTEM_ROOT . 'ajax.class.php';
                $row['status'] = 1;
                try {
                    $orderid = processOrderAll($row);
                    if ($orderid) {
                        $result = [
                            'code' => 0,
                            'msg'  => '补单成功，订单编号' . $orderid,
                        ];
                    } else {
                        $result = [
                            'code' => -1,
                            'msg'  => '补单失败，请查看对接日志和明细',
                        ];
                    }
                } catch (\Exception $e) {
                    $result = [
                        'code' => -1,
                        'msg'  => '补单失败，' . $e->getMessage(),
                    ];
                }
            } else {
                $result = [
                    'code' => -1,
                    'msg'  => '补单失败，' . $DB->error(),
                ];
            }
        }
        exit(json_encode($result));
        break;
    case 'tools.get':
        $result['code']  = 0;
        $result['msg']   = 'succ';
        $result['upcid'] = 0;
        $result['class'] = 0;
        $fields          = '*';
        $price_obj       = new \core\Price(1);
        if (isset($_POST['kw'])) {
            $kw = trim(input('kw'));
            if ($kw == 'random') {
                $rs = $DB->query("SELECT * FROM pre_tools WHERE `close`=0 order by rand() asc LIMIT 10");
            } else {
                if ($conf['search_replace_open'] == 2 || ($conf['search_replace_open'] == 1 && $isLogin2 == 1)) {
                    $search_replace = explode(',', $conf['search_replace_list']);
                    foreach ($search_replace as $value) {
                        $arr = explode('|', $value);
                        $kw  = str_replace($arr[0], $arr[1], $kw);
                    }
                }
                $rs = $DB->query("SELECT " . $fields . " FROM `pre_tools` WHERE name LIKE ? and `close`=0 order by sort asc", array('%' . $kw . '%'));
            }
        } else {
            if (isset($_GET['tid'])) {
                $tid = intval(input('get.tid', 1));
                $rs  = $DB->query("SELECT " . $fields . " FROM `pre_tools` WHERE `tid` = :tid", [':tid' => $tid]);
            } else {
                $cid      = intval(input('get.cid', 1));
                $subClass = $DB->count("SELECT count(*) FROM `pre_class` WHERE active=1 and upcid= ? order by sort asc", [$cid]);
                if ($subClass > 0) {
                    $rs   = $DB->query("SELECT * FROM `pre_class` WHERE active=1 and upcid= ? order by sort asc", [$cid]);
                    $data = array();
                    $list = array();
                    if ($rs) {
                        $list = $DB->fetchAll($rs);
                    }

                    foreach ($list as $res) {
                        if ($is_fenzhan && in_array($res['cid'], $classhide)) {
                            continue;
                        }
                        $data[] = $res;
                    }
                    $result['data']  = $data;
                    $result['class'] = 1;
                    exit(json_encode($result));
                } else {
                    $class = $DB->get_row("SELECT upcid FROM pre_class WHERE active=1 and cid= ? order by sort asc", array($cid));
                    if ($class['upcid'] > 0) {
                        $result['upcid'] = (int) $class['upcid'];
                    }

                    //$rs = $DB->query("SELECT " . $fields . " FROM pre_tools WHERE (`cid`= ? OR ? IN (`cids`)) AND `close` != '1' order by sort asc", [$cid, $cid]); //多目录模式，较耗性能一点
                    $rs = $DB->query("SELECT * FROM `pre_tools` WHERE (`cid`= ? OR ? IN (`cids`)) AND `close` != '1' order by sort asc", [$cid, $cid]);
                }
            }
            $result['info'] = null;
            if (isset($_GET['info']) && $_GET['info'] == 1) {
                $info           = $DB->get_row("SELECT * FROM pre_class WHERE cid= ?", array($cid));
                $result['info'] = $info;
            }
        }

        $defaultImg = $cdnserver . 'assets/img/Product/default.png';

        $data = array();
        $rows = [];
        if ($rs) {
            $rows = $DB->fetchAll($rs);
        }

        foreach ($rows as $res) {
            $cids = explode(',', $res['cids']);
            if ($conf['hide_rmbpay'] == 1 && !$isLogin2 && $res['pay_rmb'] == 1) {
                continue;
            } elseif ($res['close_login'] == 1 && $isLogin2 !== 1) {
                //登录可见
                continue;
            } elseif ($res['cid'] != $cid && !in_array($cid, $cids)) {
                continue;
            }
            $stock_count = 0;
            if ($res['prid'] == 0) {
                $stock_count = intval($DB->count("SELECT count(*) FROM `pre_stock` WHERE `tid`=:tid", [$res['tid']]));
            }

            if ($stock_count > 0) {
                $rs2        = $DB->query("SELECT * FROM `pre_stock` WHERE `tid`=:tid order by value ASC", [$res['tid']]);
                $stock_rows = [];
                if ($rs2) {
                    $stock_rows = $DB->fetchAll($rs2);
                    foreach ($stock_rows as $item) {
                        if ($res['is_curl'] == 4) {
                            $isfaka       = 1;
                            $res['input'] = getFakaInput();
                        } else {
                            $isfaka = 0;
                        }
                        $tool           = $res;
                        $tool['price1'] = $item['price1'];
                        $tool['prid']   = $item['prid'];
                        $price_obj->setToolInfo($tool['tid'], $tool);
                        if ($isLogin2 == 1) {
                            $tool['price'] = $price_obj->getBuyPrice($tool['tid']);
                        } else {
                            $tool['price'] = $price_obj->getToolPrice($tool['tid']);
                        }

                        if (empty($tool['shopimg'])) {
                            $tool['shopimg'] = $defaultImg;
                        }

                        if (!empty($tool['close_alert'])) {
                            $tool['close_alert'] = htmlspecialchars_decode($tool['close_alert']);
                        }

                        $tool['isfaka'] = $isfaka;
                        $tool['value']  = $item['value'];
                        if ($item['value'] > 1) {
                            $tool['name'] = $tool['name'] . '☛' . $item['value'] . '个';
                        }
                        $tool['stock_id'] = $item['id'];
                        $tool['desc']     = htmlspecialchars_decode($tool['desc']);
                        $tool['alert']    = htmlspecialchars_decode($tool['alert']);
                        $data[]           = $tool;
                    }
                }
            } else {
                if (isset($_SESSION['gift_id']) && isset($_SESSION['gift_tid']) && $_SESSION['gift_tid'] == $res['tid']) {
                    $price = $conf["cjmoney"] ? $conf["cjmoney"] : 0;
                } else {

                    $price_obj->setToolInfo($res['tid']);

                    if ($isLogin2 == 1) {
                        if ($userrow['power'] == 0 && $price_obj->getToolDel($res['tid']) == 1) {
                            continue;
                        }
                        $price = $price_obj->getBuyPrice($res['tid']);
                    } else {
                        if ($is_fenzhan == true && $price_obj->getToolDel($res['tid']) == 1) {
                            continue;
                        }
                        $price = $price_obj->getToolPrice($res['tid']);
                    }
                }

                if ($res['is_curl'] == 4) {
                    $isfaka       = 1;
                    $res['input'] = getFakaInput();
                } else {
                    $isfaka = 0;
                }

                $tool = $res;

                if (empty($tool['shopimg'])) {
                    $tool['shopimg'] = $defaultImg;
                }

                if (!empty($tool['close_alert'])) {
                    $tool['close_alert'] = htmlspecialchars_decode($tool['close_alert']);
                }

                $tool['price']  = $price;
                $tool['isfaka'] = $isfaka;

                $tool['desc']  = htmlspecialchars_decode($tool['desc']);
                $tool['alert'] = htmlspecialchars_decode($tool['alert']);
                $data[]        = $tool;
            }
        }
        $result['data'] = $data;
        exit(json_encode($result));
        break;

    case 'getProxyIp':
        checkAuthority('super');
        if ($conf['proxy'] == 2 && $conf['proxy_host']) {
            $ip = $conf['proxy_host'];
        } else {
            $ip = getProxyIp();
        }
        $result = ['code' => 0, 'msg' => 'succ', 'ip' => $ip];
        exit(json_encode($result));
        break;
    case 'create_url':
        $force = input('get.force');
        $url   = input('get.longurl');
        if (!preg_match('/http/', $url)) {
            $url = 'http://' . $url . '/?' . rand(1, 999);
        }
        $result = getUrlDwz($url);
        exit(json_encode($result, JSON_UNESCAPED_UNICODE));
        break;
    case 'create_dwz':
        $url = input('get.longurl');
        if (!preg_match('/http/', $url)) {
            $url = 'http://' . $url . '/?' . rand(1, 999);
        }
        $result = getUrlDwz($url);
        exit(json_encode($result, JSON_UNESCAPED_UNICODE));
        break;
    case 'getServerIp':
        $ip = getServerIp();
        exit('{"code":0,"ip":"' . $ip . '"}');
        break;
    case 'onPluginConfigSave':
        $plugin_id = input('plugin_id', 1, 1);
        if (!$plugin_id) {
            $result = ['code' => -1, 'msg' => '插件ID不能为空'];
        } else {
            $row = $DB->get_row("SELECT * FROM `pre_plugin` where `id`='{$plugin_id}'");
            if ($row) {
                $config = input('config', 1, 0);
                if (is_array($config)) {
                    $config = json_encode($config, 256);
                }
                $update = $DB->update("UPDATE `pre_plugin` SET `config`='{$config}' where `id`='{$plugin_id}'");
                if ($update !== false) {
                    $result = ['code' => 0, 'msg' => '成功', 'data' => [
                        'config' => $config,
                    ]];
                } else {
                    $result = ['code' => -1, 'msg' => '保存失败, ' . $DB->error()];
                }
            } else {
                $result = ['code' => -1, 'msg' => '插件不存在'];
            }
        }
        exit(json_encode($result));
        break;
    case 'getPluginConfigList':
        $plugin_id = input('plugin_id', 1, 1);
        if (!$plugin_id) {
            $result = ['code' => -1, 'msg' => '插件ID不能为空'];
        } else {
            $row = $DB->get_row("SELECT * FROM `pre_plugin` where `id`='{$plugin_id}'");
            if ($row) {
                $list = '不支持的插件类型';
                try {
                    if ($row['type'] == 'sms') {
                        $list = Sms::loadForConfigList($row['dirname']);
                    } elseif ($row['type'] == 'mails') {
                        $list = Ems::loadForConfigList($row['dirname']);
                    }
                } catch (\Throwable $th) {
                    $list = $th->getMessage() . '[' . $th->getLine() . ']';
                }

                if (is_array($list)) {

                    $config = [];

                    if ($row['config']) {
                        $config = json_decode($row['config'], true);
                    }

                    $fields = [];
                    foreach ($list as $key => $value) {
                        $fields[$value['field']] = $value;
                    }

                    // 隐藏数据
                    if (is_array($config)) {
                        foreach ($config as $key => $value) {
                            if (isset($fields[$key]) && $fields[$key]['encrypt'] == 1) {
                                $config[$key] = substr($value, 0, 3) . '****' . substr($value, -3);
                            }
                        }
                    }

                    $result = ['code' => 0, 'msg' => '成功', 'data' => [
                        'list'   => $list,
                        'config' => $config ? $config : [],
                    ]];
                } else {
                    $result = ['code' => -1, 'msg' => '插件不支持获取自定义配置列表', 'data' => $list, 'type' => $row['type']];
                }
            } else {
                $result = ['code' => -1, 'msg' => '插件不存在'];
            }
        }
        exit(json_encode($result));
        break;
    case 'getMailsTplList':
        $plugin_id = input('plugin_id', 1, 1);
        if (!$plugin_id) {
            $result = ['code' => -1, 'msg' => '插件ID不能为空'];
        } else {
            $row = $DB->get_row("SELECT * FROM `pre_plugin` where `id`='{$plugin_id}'");
            if ($row) {
                $device = $row['dirname'];
                $list   = $DB->select("SELECT * FROM `pre_ems_tpl` where `status`=1");
                $result = ['code' => 0, 'msg' => '成功', 'data' => [
                    'list' => $list,
                ]];
            } else {
                $result = ['code' => -1, 'msg' => '指定驱动插件不存在'];
            }
        }
        exit(json_encode($result));
        break;
    case 'getMailsTplInfo':
        $id = input('id', 1, 1);
        if (!$id) {
            $result = ['code' => -1, 'msg' => '模板ID不能为空'];
        } else {
            $row = $DB->get_row("SELECT * FROM `pre_ems_tpl` where `id`='{$id}'");
            if ($row) {
                $result = ['code' => 0, 'msg' => '成功', 'data' => $row];
            } else {
                $result = ['code' => -1, 'msg' => '模板不存在'];
            }
        }
        exit(json_encode($result));
        break;
    case 'getSmsTplList':
        $plugin_id = input('plugin_id', 1, 1);
        if (!$plugin_id) {
            $result = ['code' => -1, 'msg' => '插件ID不能为空'];
        } else {
            $row = $DB->get_row("SELECT * FROM `pre_plugin` where `id`='{$plugin_id}'");
            if ($row) {
                $device = $row['dirname'];
                $list   = $DB->select("SELECT * FROM `pre_sms_tpl` where `device`='{$device}'");
                $result = ['code' => 0, 'msg' => '成功', 'data' => [
                    'list' => $list,
                ]];
            } else {
                $result = ['code' => -1, 'msg' => '指定驱动插件不存在'];
            }
        }
        exit(json_encode($result));
        break;
    case 'getSmsTplInfo':
        $id = input('id', 1, 1);
        if (!$id) {
            $result = ['code' => -1, 'msg' => '模板ID不能为空'];
        } else {
            $row = $DB->get_row("SELECT * FROM `pre_sms_tpl` where `id`='{$id}'");
            if ($row) {
                $result = ['code' => 0, 'msg' => '成功', 'data' => $row];
            } else {
                $result = ['code' => -1, 'msg' => '模板不存在'];
            }
        }
        exit(json_encode($result));
        break;
    case 'onTestSend':
        $plugin_id = input('plugin_id', 1, 1);
        if (!$plugin_id) {
            $result = ['code' => -1, 'msg' => '插件ID不能为空'];
        } else {
            $row = $DB->get_row("SELECT * FROM `pre_plugin` where `id`='{$plugin_id}'");
            if ($row) {

                $list = '不支持的插件类型';

                if ($row['type'] == 'sms') {
                    $template_id  = input('template_id', 1, 1);
                    $template_row = $DB->get_row("SELECT * FROM `pre_sms_tpl` where `id`='{$template_id}'");
                    if (!$template_row) {
                        exit(json_encode(['code' => -1, 'msg' => '短信模板不存在']));
                    }

                    $event = $template_row['event'];

                    if (!$event) {
                        $event = 'default';
                    }

                    $mobile = input('mobile');

                    if (!$mobile) {
                        exit(json_encode(['code' => -1, 'msg' => '手机号不能为空']));
                    }

                    try {
                        $sms = new Sms($plugin_id);
                        $res = $sms->send($mobile, null, $event);
                    } catch (\Throwable $th) {
                        $res = $th->getMessage() . '[' . $th->getLine() . ']';
                    }
                } elseif ($row['type'] == 'mails') {

                    // 自定义内容
                    $body = input('body');

                    $template_id  = input('template_id', 1, 1);
                    $template_row = $DB->get_row("SELECT * FROM `pre_ems_tpl` where `id`='{$template_id}'");

                    if (!$body && !$template_row) {
                        exit(json_encode(['code' => -1, 'msg' => '邮件模板不存在']));
                    }

                    $event = 'default';
                    if ($template_row) {
                        $event = $template_row['event'];
                    }

                    if (!$event) {
                        $event = 'default';
                    }

                    $email = input('email');
                    if (!$email) {
                        exit(json_encode(['code' => -1, 'msg' => '邮箱不能为空']));
                    }

                    try {
                        $ems = new Ems($plugin_id);
                        if ($body) {
                            $res = $ems->sendEmail($email, '发送邮件测试', $body);
                        } else {
                            $res = $ems->send($email, null, $event);
                        }
                    } catch (\Throwable $th) {
                        $res = $th->getMessage() . '[' . $th->getLine() . ']';
                    }
                }
                if ($res === true) {
                    $result = ['code' => 0, 'msg' => '发送成功, 请注意接收'];
                } else {
                    $result = ['code' => -1, 'msg' => $res];
                }
            } else {
                $result = ['code' => -1, 'msg' => '指定驱动插件不存在'];
            }
        }
        exit(json_encode($result));
        break;
    case 'onEmsTplDel':
        $id = input('id', 1, 1);
        if (!$id) {
            $result = ['code' => -1, 'msg' => '插件ID不能为空'];
        } else {
            $DB->exec("DELETE FROM `pre_ems_tpl` where `id`='{$id}'");
            $result = ['code' => 0, 'msg' => '删除成功'];
        }
        exit(json_encode($result));
        break;
    case 'onSmsTplDel':
        $id = input('id', 1, 1);
        if (!$id) {
            $result = ['code' => -1, 'msg' => '插件ID不能为空'];
        } else {
            $DB->exec("DELETE FROM `pre_sms_tpl` where `id`='{$id}'");
            $result = ['code' => 0, 'msg' => '删除成功'];
        }
        exit(json_encode($result));
        break;
    case 'onSaveTpl':
        $id     = input('id', 1, 1);
        $action = input('action', 1, 1);
        if (!$id) {
            $result = ['code' => -1, 'msg' => '插件ID不能为空'];
        } else {

            if ($action == 'mails') {
                $row = $DB->get_row("SELECT * FROM `pre_ems_tpl` where `id`='{$id}'");
            } else {
                $row = $DB->get_row("SELECT * FROM `pre_sms_tpl` where `id`='{$id}'");
            }

            if ($row) {

                if ($action == 'mails') {
                    Db::name('ems_tpl')->where([
                        'id' => $row['id'],
                    ])->update([
                        'name'    => input('name', 1, 1),
                        'event'   => input('event', 1, 1),
                        'subject' => input('subject', 1, 1),
                        'body'    => addcslashes(input('name', 0, 0), "'"),
                        'remark'  => input('remark', 1, 1),
                        'status'  => input('status', 1, 1),
                    ]);
                } else {
                    Db::name('sms_tpl')->where([
                        'id' => $row['id'],
                    ])->update([
                        'name'        => input('name', 1, 1),
                        'event'       => input('event', 1, 1),
                        'type'        => input('type', 1, 1),
                        'device'      => input('device', 1, 1),
                        'device_id'   => input('device_id', 1, 1),
                        'template_id' => input('template_id', 1, 1),
                        'content'     => input('content', 1, 1),
                        'remark'      => input('remark', 1, 1),
                        'condition'   => intval(input('condition', 1, 1)),
                        'status'      => input('status', 1, 1),
                    ]);
                }
                $lastSql = Db::getLastSql();
                if ($action == 'mails') {
                    $row = $DB->get_row("SELECT * FROM `pre_ems_tpl` where `id`='{$id}'");
                } else {
                    $row = $DB->get_row("SELECT * FROM `pre_sms_tpl` where `id`='{$id}'");
                }

                $result = ['code' => 0, 'msg' => '成功', 'data' => $row, 'sql' => $lastSql];
            } else {
                $result = ['code' => -1, 'msg' => '模板不存在'];
            }
        }
        exit(json_encode($result));
        break;

    case 'onBatchUpdateSmsCondition':
        $ids = input('ids', 1, 1);
        if (!is_array($ids)) {
            exit(json_encode(['code' => -1, 'msg' => '批量提交的参数列表不能为空']));
        }

        $count = count($ids);
        $succ  = 0;
        $error = [];
        $arr   = [];

        foreach ($ids as $key => $id) {
            $row = $DB->find("SELECT * FROM `pre_sms_tpl` where `id`='{$id}'");
            if ($row) {
                $device    = $row['device'];
                $pluginRow = $DB->find("SELECT * FROM `pre_plugin` where `dirname`='{$device}'");

                if ($pluginRow) {
                    $type   = $row['type'];
                    $params = [
                        'title'   => $row['name'],
                        'type'    => $row['type'],
                        'name'    => $row['name'],
                        'content' => $row['content'],
                        'remark'  => strstr($remark, $weburl) !== false ? $remark : '线上综合商城:' . $weburl,
                        'config'  => json_decode($pluginRow['config'], true) ?: [],
                    ];

                    if ($row['template_id']) {
                        $params['template_id'] = $row['template_id'];

                        $func = $row['type'] == 'cn' ? 'getCnTemplate' : 'getGlobalTemplate';
                    } else {
                        // 未提交审核的直接提交
                        $func = $row['type'] == 'cn' ? 'createCnTemplate' : 'createGlobalTemplate';
                    }

                    try {
                        $class = Sms::loadClass($row['device'], $pluginRow);
                        if ($class) {
                            $ret = $class->$func($params);

                            $arr[] = [
                                'params' => $params,
                                'ret'    => $ret,
                            ];

                            if (checkPluginSuccess($ret)) {
                                $succ++;
                                if (array_has('template', $ret)) {
                                    Db::name('sms_tpl')->where([
                                        'id' => $row['id'],
                                    ])->update([
                                        'template_id' => array_get('template_id', $ret['template']),
                                        'condition'   => array_get('template_status', $ret['template']),
                                    ]);
                                } else {
                                    Db::name('sms_tpl')->where([
                                        'id' => $row['id'],
                                    ])->update([
                                        'condition' => 3,
                                    ]);
                                }
                            } else {
                                Db::name('sms_tpl')->where('id', $row['id'])->update([
                                    'fail_msg' => getPluginMsg($ret),
                                ]);
                                $error[] = getPluginMsg($ret);
                            }
                        } else {
                            $error[] = '该模板[' . $row['name'] . ']对应的驱动插件' . $row['device'] . '加载失败';
                        }
                    } catch (\Throwable $th) {
                        $arr[] = [
                            'params' => $params,
                            'ret'    => null,
                        ];

                        $error[] = '该模板[' . $row['name'] . ']提交失败, ' . $th->getMessage() . '[' . $th->getLine() . '] in' . str_replace(ROOT, '', $th->getFile());
                    }
                } else {
                    $arr[] = [
                        'params' => $params,
                        'ret'    => null,
                    ];

                    $error[] = '该模板[' . $row['name'] . ']对应的驱动插件' . $row['device'] . '不存在';
                }
            } else {
                $arr[] = [
                    'params' => null,
                    'ret'    => null,
                ];
                $error[] = '该模板' . $id . '不存在';
            }
        }
        $result = [
            'code' => -1,
            'msg'  => '共' . $count . '个，成功更新' . $succ . '个模板 <br/> 失败原因如下:' . implode("<br/>\r\n", array_unique($error)),
            'data' => [
                'arr' => $arr,
            ],
        ];
        exit(json_encode($result));
        break;
    case 'onBatchSubmitSmsCondition':
        $ids = input('ids', 1, 1);
        if (!is_array($ids)) {
            exit(json_encode(['code' => -1, 'msg' => '批量提交的参数列表不能为空']));
        }

        $count = count($ids);
        $succ  = 0;
        $error = [];
        $arr   = [];

        foreach ($ids as $key => $id) {
            $row = $DB->find("SELECT * FROM `pre_sms_tpl` where `id`='{$id}'");
            if ($row) {
                $device    = $row['device'];
                $pluginRow = $DB->find("SELECT * FROM `pre_plugin` where `dirname`='{$device}'");

                if ($pluginRow) {

                    $template_id = null;
                    if ($row['template_id']) {
                        // 审核中的或已经通过的不再提交
                        if ($row['condition'] == 3 || $row['condition'] == 1) {
                            $succ++;
                            continue;
                        }

                        $template_id = $row['template_id'];

                        $func = $row['type'] == 'cn' ? 'putCnTemplate' : 'putGlobalTemplate';
                    } else {
                        $func = $row['type'] == 'cn' ? 'createCnTemplate' : 'createGlobalTemplate';
                    }

                    $type   = $row['type'];
                    $params = [
                        'title'       => $row['name'],
                        'func'        => $func,
                        'type'        => $row['type'],
                        'name'        => $row['name'],
                        'template_id' => $template_id,
                        'content'     => $row['content'],
                        'remark'      => strstr($remark, $weburl) !== false ? $remark : '线上综合商城:' . $weburl,
                        'config'      => json_decode($pluginRow['config'], true) ?: [],
                    ];

                    try {
                        $class = Sms::loadClass($row['device'], $pluginRow);
                        if ($class) {
                            $ret = $class->$func($params);

                            $arr[] = [
                                'params' => $params,
                                'ret'    => $ret,
                            ];

                            if (checkPluginSuccess($ret)) {
                                $succ++;
                                if (array_has('template', $ret)) {
                                    Db::name('sms_tpl')->where([
                                        'id' => $row['id'],
                                    ])->update([
                                        'template_id' => array_get('template_id', $ret['template']),
                                        'condition'   => array_get('template_status', $ret['template']),
                                    ]);
                                } else {
                                    Db::name('sms_tpl')->where([
                                        'id' => $row['id'],
                                    ])->update([
                                        'condition' => 3,
                                    ]);
                                }
                            } else {
                                Db::name('sms_tpl')->where('id', $row['id'])->update([
                                    'fail_msg' => getPluginMsg($ret),
                                ]);
                                $error[] = getPluginMsg($ret);
                            }
                        }
                    } catch (\Throwable $th) {
                        $arr[] = [
                            'params' => $params,
                            'ret'    => null,
                        ];

                        $error[] = '该模板[' . $row['name'] . ']提交失败, ' . $th->getMessage() . '[' . $th->getLine() . '] in' . str_replace(ROOT, '', $th->getFile());
                    }
                } else {
                    $arr[] = [
                        'params' => $params,
                        'ret'    => null,
                    ];

                    $error[] = '该模板[' . $row['name'] . ']对应的驱动插件' . $row['device'] . '不存在';
                }
            } else {
                $arr[] = [
                    'params' => null,
                    'ret'    => null,
                ];
                $error[] = '该模板' . $id . '不存在';
            }
        }
        $result = [
            'code' => -1,
            'msg'  => '共' . $count . '个，成功更新' . $succ . '个模板 <br/> 失败原因如下:' . implode("<br/>\r\n", array_unique($error)),
            'data' => [
                'arr' => $arr,
            ],
        ];
        exit(json_encode($result));
        break;
    case 'tixian_today_alipay':
        if (!empty($_POST['date'])) {
            $startTime = $_POST['date'] . ' 00:00:00';
            $endTime   = date("Y-m-d", strtotime($_POST['date'] . " +1 day")) . ' 00:00:00';
        } else {
            $startTime = date("Y-m-d", strtotime("-1 day")) . ' 00:00:00';
            $endTime   = date("Y-m-d") . ' 00:00:00';
        }
        $money  = $DB->getColumn("SELECT sum(realmoney) FROM pre_tixian WHERE pay_type=0 AND addtime>='{$startTime}' AND addtime<'{$endTime}' AND status=0");
        $number = $DB->getColumn("SELECT count(*) FROM pre_tixian WHERE pay_type=0 AND addtime>='{$startTime}' AND addtime<'{$endTime}' AND status=0");
        exit(json_encode(['code' => 0, 'money' => round($money, 2), 'number' => (int) $number]));
        break;
    case 'master_tixian_today_alipay':
        if (!empty($_POST['date'])) {
            $startTime = $_POST['date'] . ' 00:00:00';
            $endTime   = date("Y-m-d", strtotime($_POST['date'] . " +1 day")) . ' 00:00:00';
        } else {
            $startTime = date("Y-m-d", strtotime("-1 day")) . ' 00:00:00';
            $endTime   = date("Y-m-d") . ' 00:00:00';
        }
        $money  = $DB->getColumn("SELECT sum(realmoney) FROM pre_master_tixian WHERE pay_type=0 AND addtime>='{$startTime}' AND addtime<'{$endTime}' AND status=0");
        $number = $DB->getColumn("SELECT count(*) FROM pre_master_tixian WHERE pay_type=0 AND addtime>='{$startTime}' AND addtime<'{$endTime}' AND status=0");
        exit(json_encode(['code' => 0, 'money' => round($money, 2), 'number' => (int) $number]));
        break;
    case 'goods_sort':
        $tid  = input('post.tid', 1);
        $sort = intval(input('post.sort', 1));
        $row  = $DB->get_row("SELECT * FROM `pre_tools` where `tid`=:tid", [':tid' => $tid]);
        if (!$row) {
            $result = ['code' => -1, 'msg' => '该商品不存在！', 'data' => []];
        } else {
            $sql = "UPDATE  `pre_tools` SET `sort`=:sort where `tid`=:tid";
            if ($DB->query($sql, [':sort' => $sort, ':tid' => $tid])) {
                $result = ['code' => 0, 'msg' => 'succ', 'data' => ['tid' => $tid, 'time' => time()]];
            } else {
                $result = ['code' => -1, 'msg' => '操作失败，' . $DB->error(), 'data' => []];
            }
        }
        exit(json_encode($result));
        break;
    case 'toggleBan': // 封禁/解封账户
        $zid = intval(input('post.zid'));
        $status = intval(input('post.status')); // 1=封禁, 0=解封
        $type = input('post.type'); // site=分站, master=供货商

        if (!$zid || !in_array($type, ['site', 'master'])) {
            exit('{"code":-1,"msg":"参数错误"}');
        }

        $table = $type == 'site' ? 'pre_site' : 'pre_master';
        $name = $type == 'site' ? '分站' : '供货商';

        if ($status == 1) {
            // 封禁账户
            $banned_time = date('Y-m-d H:i:s');
            $sql = "UPDATE `{$table}` SET `is_banned` = 1, `banned_time` = ? WHERE `zid` = ?";
            $params = array($banned_time, $zid);
            $action = '封禁';
        } else {
            // 解封账户
            $sql = "UPDATE `{$table}` SET `is_banned` = 0, `banned_time` = NULL, `login_fail_count` = 0 WHERE `zid` = ?";
            $params = array($zid);
            $action = '解封';
        }

        if ($DB->query($sql, $params)) {
            // 记录操作日志
            $user_info = $DB->get_row("SELECT user FROM `{$table}` WHERE zid = ?", array($zid));
            $username = $user_info ? $user_info['user'] : '未知';
            fzlog_result($zid, $name . '管理', '管理员操作：' . $action . '账户 ' . $username, $action . '操作成功', 1);

            exit('{"code":0,"msg":"' . $action . $name . '成功"}');
        } else {
            exit('{"code":-1,"msg":"' . $action . $name . '失败：' . $DB->error() . '"}');
        }
        break;
    default:
        exit('{"code":-4,"msg":"No Act","version":"' . AJAX_VERSION . '"}');
        break;
}

function checkPluginSuccess($data)
{
    if (is_array($data)) {
        if (isset($data['status']) && in_array($data['status'], [1, 200, 'success', 'succ', '成功'])) {
            return true;
        } elseif (isset($data['code']) && in_array($data['code'], [200, 1])) {
            return true;
        }
    }
    return false;
}

function getPluginMsg($data)
{
    if (is_array($data)) {
        if (isset($data['msg'])) {
            return $data['msg'];
        } elseif (isset($data['info'])) {
            return $data['info'];
        } elseif (isset($data['message'])) {
            return $data['message'];
        }
    }
    return '未知错误';
}
