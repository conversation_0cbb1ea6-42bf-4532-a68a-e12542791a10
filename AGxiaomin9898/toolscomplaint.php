<?php
# 文章插件 By 神话
include "../includes/common.php";
checkLogin();

$my    = isset($_GET['my']) ? daddslashes($_GET['my']) : null;
$title = "商品投诉反馈";
$act   = isset($_GET['act']) ? daddslashes($_GET['act']) : null;
if ($act == "setActive") {
    $id     = intval(input('get.id'));
    $active = intval(input('get.active'));
    $sql    = "UPDATE `pre_pro_complaint` set active='" . $active . "' where id='" . $id . "'";
    if ($DB->query($sql)) {
        $result = ['code' => 0, 'msg' => '操作成功'];
    } else {
        $result = ['code' => -1, 'msg' => '操作失败！' . $DB->error()];
    }
    exit(json_encode($result));
} elseif ($act == "setTop") {
    $id  = intval(input('get.id'));
    $top = intval(input('get.top'));
    $sql = "UPDATE `pre_pro_complaint` SET `top`='{$top}' where `id`='{$id}'";
    if (false !== $DB->exec($sql)) {
        $result = ['code' => 0, 'msg' => '操作成功'];
    } else {
        $result = ['code' => -1, 'msg' => '操作失败！' . $DB->error()];
    }
    exit(json_encode($result));
}

checkAuthority('message');
include './head.php';

$editor_load = true;

$fl = '<option value="1">平台公告</option><option value="2">业务推荐</option>';
echo '<div class="col-sm-12 col-md-12 col-lg-12 center-block" style="float: none;padding-top:10px;">    ';
if ($my == "del") {
    $id  = intval($_GET['id']);
    $sql = "delete FROM `pre_pro_complaint` where id='" . $id . "'";
    if ($DB->query($sql)) {
        showmsg("删除成功！", 1);
    } else {
        showmsg("删除失败！" . $DB->error(), 4);
    }
} else {
    $sql     = "1";
    $pro_name=isset($_GET['pro_name'])?$_GET['pro_name']:"";
    $status=isset($_GET['status'])?$_GET['status']:"";
    if ($pro_name) {
        $pro_name = daddslashes($pro_name);
        $sql     .= " and pro_name like '%{$pro_name}%'";
    }
    if ($status == '1') {
        $sql    .= " and status='处理中'";
    }
    $numrows = $DB->count("SELECT count(*) from pre_pro_complaint where status='待处理'");
    $con     = '当前共有 <b style="color:red;">' . $numrows . '</b> 条网盘信息未处理。';
    $link    = "";
  

    echo '<div class="block">
<div class="block-title clearfix">
<h2>' . $con . '</h2>
<form method="GET" class="form-inline">
&nbsp;&nbsp;
<div class="form-group">
   <input type="text" class="form-control" name="pro_name" placeholder="请输入商品名称" value="'.$pro_name.'">
</div>

 <div class="form-group">
    <select class="form-control" name="status" default="'.$status.'" id="status" placeholder="选择状态">
        <option value="0">所有状态</option>
        <option value="1">处理中</option>
    </select>
 </div>
 <button type="submit" class="btn btn-info">搜索</button>&nbsp;
 <a href="./toolscomplaint.php" class="btn btn-warning">重置</a>&nbsp;

</form>
<p></p>
</div>
      <form>
      <div class="table-responsive">
        <table class="table table-striped">
          <thead><tr><th style="width: 80px;">ID</th><th style="width: 450px;">商品名称</th><th style="width: 100px;">投稿内容</th><th style="width: 100px;">投稿ID</th><th style="width: 150px;">提交IP</th><th style="width: 150px;">提交时间</th><th style="width: 150px;">处理时间</th><th style="width: 120px;">状态</th><th>操作</th></tr></thead>
          <tbody>';

    $pagesize = 30;
    $pages    = intval($numrows / $pagesize);
    if ($numrows % $pagesize) {
        $pages++;
    }

    if (isset($_GET['page'])) {
        $page = intval($_GET['page']);
    } else {
        $page = 1;
    }
    $offset = $pagesize * ($page - 1);
    $rs     = $DB->query("SELECT * FROM pre_pro_complaint WHERE {$sql} order by id DESC limit $offset,$pagesize");
    while ($res = $DB->fetch($rs)) {
        $user_id = $res['user_id'];
        $content_translated = str_replace(['0', '1', '2'],['没有效果', '二改垃圾', '空壳项目'],
        $res['content']);
        $updatetime= $res['updatetime']?date('Y-m-d H:i:s',$res['updatetime']): '暂未处理';
        echo '<tr><td><input type="checkbox" name="checkbox[]" value="' . $res['id'] . '">&nbsp;<b>' . $res['id'] . '</b></td>
        <td><a href="./shopedit.php?my=edit&tid=' . $res['pro_id'] . '">' . $res['pro_name'] . '</a></td>
        <td>' . $content_translated . '</td>
        <td><a href="./sitelist.php?zid='.$res['user_id'].'">'.$res['user_id'].'</a></td>
        <td>'.$res['ip'].'</td>
        <td>' . date('Y-m-d H:i:s',$res['addtime']) . '</td>
        <td>'.$updatetime.'</td>
   <td><input type="text" class="form-control input-sm status" name="status" value="'.$res['status'].'" placeholder="填写处理结果" required="" data-id="'.$res['id'].'"></td>
   <td><a href="./toolscomplaint.php?my=del&id=' . $res['id'] . '" class="btn btn-xs btn-danger" onclick="return confirm(\'你确实要删除此记录吗？\');">删除</a></td></tr>';
    }

    echo <<<'html1'
            </tbody></table></div>
        <div style="height: 100px;display: block;"></div>
html1;
    if ($is_mb == false) {
        echo '<footer class="navbar-fixed-bottom">
            <div class="paging-navbar">';
    } else {
        echo '<footer>
            <div class="" style="width: 98%;display:block;background-color: #fff;border-color: 1px 2px #cfdadd;position: fixed;bottom: 0;left: 15px;"><div style="padding:5px 8px;">';
    }
    echo <<<'html2'
<div class="form-inline">
            <input type="hidden" name="result_all" id="result_all"/>&nbsp;
            <input name="chkAll1" type="checkbox" id="chkAll1" onclick="check1(this.form.list1)" value="checkbox">&nbsp;反选&nbsp;
            <select name="checkStatus"><option selected>选择批量操作</option><option value="4">批量删除</option></select>&nbsp;
            <button type="button" onclick="operation()" class="btn btn-primary">确定</button>
            <div class="form-group" style="margin-left:10px"></div>
html2;
#分页
    $pageList = new \core\Page($numrows, $pagesize, 0, $link);
    echo $pageList->showPage();
    echo "</div></form></footer></div>";
}
echo '
<script type="text/javascript">
"use strict";
var checkList = [] || new Array();
$(document).on("click", ".setTop", function(event) {
  event.preventDefault();
  /* Act on the event */
  var id = $(this).data("id");
  var top = $(this).data("top");
  $.ajax({
        type : \'GET\',
        url : \'?act=setTop&id=\'+id+\'&top=\'+top,
        dataType : \'json\',
        success : function(data) {
            window.location.reload()
        },
        error:function(data){
            layer.msg(\'服务器错误\');
            return false;
        }
    });
});
function check1(field) {
    var checkbox = field || document.getElementsByName(\'checkbox[]\');
    for (var i = 0; i < checkbox.length; i++) {
        if (checkbox[i].checked === false) {
            checkbox[i].checked = true;
        } else {
            checkbox[i].checked = false;
        }
    }
}

function getVals() {
    var checkbox = document.getElementsByName(\'checkbox[]\');
    checkList = [];
    for (var i = 0; i < checkbox.length; i++) {
        if (checkbox[i].checked) {
            checkList.push(checkbox[i].value);
        }
    }
    //console.log(checkList);
}

function sort(id,sort) {
    $.ajax({
        type : "GET",
        url : \'ajax.php?act=setArticleSort&id=\'+id+\'&sort=\'+sort,
        dataType : \'json\',
        success : function(data) {
            window.location.reload();
        },
        error:function(data){
            layer.msg(\'服务器错误\');
            return false;
        }
    });
}

function operation() {
    getVals();
    var aid = $("select[name=\'checkStatus\']").val();
    var ii = layer.load(2, {
        shade: [0.1, \'#fff\']
    });
    $.ajax({
        type : "POST",
        url : \'ajax.php?act=link_operation\',
        dataType : \'json\',
        data : {
            checkbox : checkList,
            aid : aid
        },
        success : function(data) {
            layer.close(ii);
            if(data.code==0){
                layer.msg(data.msg,{
                  time:1*1000,
                  end:function(){
                    window.location.reload();
                  }
                });
            }
        },
        error:function(data){
            layer.close(ii);
            layer.msg(\'服务器错误\');
            return false;
        }
    });
}

function setActive(id,active) {
    $.ajax({
        type : \'GET\',
        url : \'?act=setActive&id=\'+id+\'&active=\'+active,
        dataType : \'json\',
        success : function(data) {
            window.location.reload()
        },
        error:function(data){
            layer.msg(\'服务器错误\');
            return false;
        }
    });
}

function show(id) {
    $.ajax({
        type : \'GET\',
        url : \'ajax.php?act=getMessage&id=\'+id,
        dataType : \'json\',
        success : function(data) {
            if(data.code==0){
                layer.open({
                  type: 1,
                  skin: \'layui-layer-lan\',
                  anim: 2,
                  shadeClose: true,
                  title: \'查看站内文章\',
                  content: \'<div class="widget"><div class="widget-content widget-content-mini themed-background-muted text-center"><b>\'+data.title+\'</b><br/><small><font color="grey">管理员  \'+data.date+\'</font></small></div><div class="widget-content">\'+data.content+\'</div></div>\'
                });
            }else{
                layer.alert(data.msg);
            }
        },
        error:function(data){
            layer.msg(\'服务器错误\');
            return false;
        }
    });
}
</script>';
?>
<script>
    $(function () {
    //监听表单name="status"失去焦点事件
        $('.status').blur(function () {
            var id = $(this).data('id');
            var status = $.trim($(this).val());
            if (status == '处理中') {
                return false;
            }
            if (status == '') {
                layer.alert('状态不能为空');
                return false;
            }

            $.ajax({
                type: "POST",
                url: "ajax.php?act=tools_complaint",
                dataType: 'json',
                data : {
                    status : status,
                    id : id,
                    aid : 1
                },
                success: function (data) {
                    if (data.code == 0) {
                        layer.msg(data.msg,{
                          time:1*1000,
                          end:function(){
                            window.location.reload();
                          }
                        });
                    }else{
                        layer.alert(data.msg);
                    }
                }
            })
        });
    });
</script>
<?php include 'footer.php';?>
