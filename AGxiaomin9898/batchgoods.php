<?php
include '../includes/common.php';
checkLogin();
$title='批量对接商品';
include './head.php';
?>
    <div class="col-sm-12 col-md-10 center-block" style="float: none;">
<?php

checkAuthority('shop');

$rs           = $DB->query("SELECT * FROM cmy_class WHERE upcid is null OR upcid<1 order by sort asc");
$classselect       = '<option value="0">未分类</option>';
$cmy_class[0] = '未分类';
while ($res = $DB->fetch($rs)) {
    $cmy_class[$res['cid']] = $res['name'];
    $select .= '<option value="' . $res['cid'] . '">' . $res['name'] . '</option>';
    $subClass = $DB->count("SELECT count(*) FROM cmy_class WHERE upcid='" . $res['cid'] . "' order by sort asc");
    if ($subClass > 0) {
        $rs2 = $DB->query("SELECT * FROM cmy_class WHERE active=1 and upcid='" . $res['cid'] . "' order by sort asc");
        while ($res2 = $DB->fetch($rs2)) {
            $cmy_class[$res2['cid']] = $res2['name'];
            $classselect .= '<option value="' . $res2['cid'] . '">----' . $res2['name'] . '</option>';
        }
    }
}

$rs=$DB->query('SELECT * FROM cmy_shequ order by id asc');
$shequselect='';
while ($res=$DB->fetch($rs)) {
    if($res['alias']=='caihon'){$res['type']='彩虹系统';}
		$shequselect .='<option value="'.$res['id'].'" type="'.$res['type'].'" domain="'.$res['url'].'">[<font color=blue>'.$res['type'].'</font>] '.$res['url'].($res['remark']?' ('.$res['remark'].')':'').'</option>';
	
}

$rs          = $DB->query('SELECT * FROM cmy_price order by id desc');
$priceselect = '<option value="0">不加价</option>';
while ($res = $DB->fetch($rs)) {
    if ($res['kind'] == 1) {
        $kind  = '元';
        $color = 'red';
        $type  = '累加价';
    } else {
        $kind  = '%';
        $color = 'green';
        $type  = '百分比';
    }
    $priceselect .= '<option value="' . $res['id'] . '" kind="' . $res['kind'] . '" p_2="' . $res['p_2'] . '" p_1="' . $res['p_1'] . '" p_0="' . $res['p_0'] . '" style="color:' . $color . '">[' . $type . ']&nbsp;' . $res['name'] . '(+' . $res['p_0'] . $kind . '|+' . $res['p_1'] . $kind . '|+' . $res['p_2'] . $kind . ')</option>';
}


$act=$_GET['act'];
?>
<div class="block">
<div class="block-title"><h3 class="panel-title">批量对接商品</h3></div>
<?php if($act==''){?>
<div class="">
		<div class="alert alert-info">
            批量对接商品目前只支持同系统对接的类型。使用此功能可以快速添加/更新本站对接的商品。
        </div>
          <form action="?" method="GET" role="form">
		    <input type="hidden" name="act" value="data">
			<div class="form-group">
				<div class="input-group"><div class="input-group-addon">选择对接站点</div>
				<select class="form-control" name="shequ"><?php echo $shequselect?></select>
			</div></div>
            <p><input type="submit" name="submit" value="获取商品分类" class="btn btn-primary btn-block"></p>
          </form>
          <?php }else if($act=='data'){
          $shequd=$_GET['shequ'];
          $submit=$_GET['submit'];
          	$row=$DB->get_row("select * from pre_shequ where id='$shequd' limit 1");
          
	      if($row['alias']!='caihon'){exit('该对接网站类型不支持批量添加商品');}
	      else{
	          //$rows = third_call('daishua', $row, 'goods_list_by_cid', [$cid]);
	          $result = getGoodsCategory_extend($row);
                if ($result['code'] == 0) {
                    $class = $result['data'];
                    if (is_array($class)) {
                        $rows = [];
                        foreach ($class as $row) {
                            $rows[$row['cid']] = $row;
                        }
                        saveTempData('pclassData', $rows);
                    }
                } else {
                    showmsg("获取分类失败，" . $result['msg'], 4);
                }
	      }
	   
          ?>
          <div class="">
          <form action="?" role="form">
		    <input type="hidden" name="shequ" value="<?php echo $_GET['shequ']?>">
			<div class="form-group">
				<div class="input-group"><div class="input-group-addon">当前对接站点</div>
				<input class="form-control" value="<?php echo $row['url']?>" disabled=""><span class="input-group-btn"><a href="./batchgoods.php" class="btn btn-default">重新选择</a></span>
			</div></div>
			<?php 
			$url='http://'.$row['url'].'/api.php?act=classlist';
			$clos=file_get_contents($url);
			$cl2=json_decode($clos);
	        $cl3=get_object_vars($cl2);
	        $cl4=($cl3['data']);
	     
	   
			?>
		    <div class="form-group">
				<div class="input-group"><div class="input-group-addon">选择对接站点商品分类</div>
				<select class="form-control" id="cid"><option value="-1">--请选择分类--</option>
				<?php foreach ($cl4 as $clv){
				$clv=(get_object_vars($clv));?>
				<option value="<?php echo $clv['cid']?>"><?php echo $clv['name']?></option>
				<?php }?>
				</select>
			</div></div>
			<table class="table table-bordered table-vcenter table-hover" id="shoptable">
			<tbody id="shoplist"></tbody>
			</table>
			<div class="form-group">
				<div class="input-group"><div class="input-group-addon">选择保存到本站的分类</div>
				<select class="form-control" id="mcid">   <?php

$rs=$DB->query("SELECT * FROM pre_class WHERE 1 order by sort asc");
while($res = $rs->fetch())
{?>
				    <option value="<?php echo $res['cid']?>"><?php echo $res['name']?></option>
				    
				    <?php }?></select>
			</div></div>
			<div class="form-group">
				<div class="input-group"><div class="input-group-addon">选择使用的加价模板</div>
				<select class="form-control" id="prid"> <?php

$rs2=$DB->query("SELECT * FROM pre_price");
while($res2 = $rs2->fetch()){
 
?>
<option value="<?php echo $res2['id']?>"><?php echo $res2['name']?></option>
<?php }?></select><span class="input-group-btn"><a href="./price.php" class="btn btn-default">加价模板管理</a></span>
			</div></div>
            <p><input type="button" name="submit" value="确定添加/更新选中商品" class="btn btn-primary btn-block" id="add_submit"></p>
          </form>
        </div>
          <?php }?>
        </div>

</div>
</div>
<script src="<?php echo $cdnpublic?>layer/3.1.1/layer.js"></script>
<script src="assets/js/batchgoods.js?ver=<?php echo VERSION ?>"></script>
</body>
</html>