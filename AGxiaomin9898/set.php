<?php
/**
 *  Name  系统设置
 *  Auhor 百晓生
 */
include '../includes/common.php';

$title = '系统设置';
checkLogin();

checkAuthority('sets');

$mod = isset($_GET['mod']) ? $_GET['mod'] : null;
if ($mod == 'user_n') {
    if ($webConfig['test']) {
        $result = ['code' => -1, 'msg' => '修改成功！[测试站操作不会生效]'];
        exit(json_encode($result));
    }

    $adm_email = input('adm_email', 1);
    // 绑定邮箱
    if ($adm_email && conf('ems_check_change_info') == 1 && ($adm_email != $conf['adm_email'] || !validateData($conf['adm_email'], 'email'))) {

        if (\core\Ems::checkIsRun()) {
            $code  = input('code2');
            $event = 'safecheck';
            try {
                $sms       = new \core\Ems();
                $adm_email = validateData($conf['adm_email'], 'email') ? $conf['adm_email'] : $adm_email;
                $check     = $sms->check($adm_email, $code ? $code : null, $event);
                if ($check !== true) {
                    json($check, 406, [
                        'email' => $adm_email,
                        'event' => 'safecheck',
                        'type'  => '1',
                    ]);
                }
            } catch (\Throwable $th) {
                json('发送邮件错误, ' . $th->getMessage());
            }
        }
    }

    if (!$adm_email) {
        $adm_email = $conf['adm_email'];
    }

    // 安全验证
    $adm_safe_check_email = input('adm_safe_check_email', 1);
    if (isset($_POST['adm_safe_check_email']) && $adm_safe_check_email != $conf['adm_safe_check_email']) {
        if ((conf('ems_check_change_info') == 1 || conf('ems_check_change_email') == 1) && \core\Ems::checkIsRun() && validateData($adm_email, 'email')) {
            $code  = input('code2');
            $event = 'safecheck';
            try {
                $sms   = new \core\Ems();
                $check = $sms->check($adm_email, $code ? $code : null, $event);
                if ($check !== true) {
                    json($check, 406, [
                        'email' => $adm_email,
                        'event' => 'safecheck',
                        'type'  => '2',
                    ]);
                }
            } catch (\Throwable $th) {
                json('发送邮件错误, ' . $th->getMessage());
            }

        }
    }

    // 安全验证
    $adm_login_checkemail = input('adm_login_checkemail', 1);
    if (isset($_POST['adm_login_checkemail']) && $adm_login_checkemail != $conf['adm_login_checkemail']) {
        if (conf('ems_check_change_info') == 1 && \core\Ems::checkIsRun() && validateData($adm_email, 'email')) {
            $code  = input('code2');
            $event = 'safecheck';
            try {
                $sms   = new \core\Ems();
                $check = $sms->check($adm_email, $code ? $code : null, $event);
                if ($check !== true) {
                    json($check, 406, [
                        'email' => $adm_email,
                        'event' => 'safecheck',
                        'type'  => '3',
                    ]);
                }
            } catch (\Throwable $th) {
                json('发送短信错误, ' . $th->getMessage());
            }
        }
    }

    $adm_tel = input('adm_tel', 1);
    if ($adm_tel && ($adm_tel != $conf['adm_tel'] || empty($conf['adm_tel']))) {
        if (conf('ems_check_change_info') == 1 && \core\Sms::checkIsRun()) {
            $code    = input('code');
            $event   = 'change_mobile';
            $adm_tel = $conf['adm_tel'] ? $conf['adm_tel'] : $adm_tel;
            try {
                $sms   = new \core\Sms();
                $check = $sms->check($adm_tel, $code ? $code : null, $event);
                if ($check !== true) {
                    json($check, 407, [
                        'email' => $adm_tel,
                        'event' => $event,
                        'type'  => '4',
                    ]);
                }
            } catch (\Throwable $th) {
                json('发送短信错误, ' . $th->getMessage());
            }
        }
    }

    $adm_pwd = input('adm_pwd');
    // 改密验证
    if ($adm_pwd && conf('ems_check_change_pwd') == 1 && $adm_pwd != $conf['adm_pwd']) {
        if (\core\Ems::checkIsRun() && validateData($adm_email, 'email')) {
            $code  = input('code2');
            $event = 'change_pwd';
            try {
                $sms   = new \core\Ems();
                $check = $sms->check($adm_email, $code ? $code : null, $event);
                if ($check !== true) {
                    json($check, 406, [
                        'email' => $adm_email,
                        'event' => $event,
                        'type'  => '5',
                    ]);
                }
            } catch (\Throwable $th) {
                json('发送短信错误, ' . $th->getMessage());
            }
        }
    }

    $adm_user = input('adm_user');
    $sendmsg  = '';
    if ($adm_pwd && $adm_user && $adm_user != $conf['adm_user']) {
        $sendmsg = '安全提醒！您已成功修改后台账号和密码';
    } else {
        if ($adm_pwd) {
            $sendmsg = '安全提醒！您已成功修改后台密码';
        } elseif ($adm_user && $adm_user != $conf['adm_user']) {
            $sendmsg = '安全提醒！您已成功修改后台账号';
        }
    }

    if ($sendmsg && \core\Ems::checkIsRun() && validateData($adm_email, 'email')) {
        try {
            $ems = new \core\Ems();
            $ems->sendEmail($adm_email, $sendmsg, '尊敬的管理员<b>' . $conf['adm_user'] . '</b>, ' . $sendmsg . '!<br/>如非本人操作, 你的账号和密码可能已经泄露, 请及时核查<br/>操作IP地址 :' . $clientip);
        } catch (\Throwable $th) {
            //throw $th;
        }
    }

    foreach ($_POST as $key => $value) {
        if ($key != 'do') {
            if ($key == 'adm_pwd' && $value == "") {
                continue;
            }

            if ($key == 'adm_tel' && $value == "") {
                continue;
            }

            if ($key == 'adm_email' && $value == "") {
                continue;
            }
            saveSetting($key, $value);
        }
    }

    $ad = $CACHE->clear();
    if ($ad) {
        $result = array("code" => 0, "msg" => '修改后台资料成功！', "data" => null);
    } else {
        $result = array('code' => -1, 'msg' => '修改后台资料失败！原因：' . $DB->error());
    }
    exit(json_encode($result));
} else if ($mod == 'saveJkType') {
    saveSetting('pricejk_edit', intval($_POST['pricejk_edit']));
    if ($CACHE->clear()) {
        exit('{"code":0,"msg":"监控方案保存成功"}');
    } else {
        exit('{"code":-1,"msg":"监控方案保存失败！' . $DB->error() . '"}');
    }
} elseif ($mod == 'checkTel') {
    if (daddslashes($_POST['adm_tel']) != $conf['zz_adm_tel']) {
        $result = array("code" => 0);
    } else {
        $result = array("code" => -1);
    }
    exit(json_encode($result));
} elseif ($mod == 'epay_update') {
    $selects = getAddPayList(1);
    $selects .= $epayStr_footer;
    json_success('succ', ['selects' => $selects]);
} elseif ($mod == 'codepay_update') {
    $selects = getAddCodePayList(1);
    json_success('succ', ['selects' => $selects]);
}

checkFileSize();

include './head.php';

echo '
    <link rel="stylesheet" href="./assets/css/set.css?' . VERSION . '">
    <div class="col-xs-12 col-sm-12 col-lg-12 center-block" style="float: none;padding-top:10px;">
';

if ($mod == 'site_n' && $_POST['do'] == 'submit') {
    if (!$_POST['submit']) {
        showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
    } else {
        foreach ($_POST as $key => $value) {
            if ($key != 'submit') {
                saveSetting($key, $value);
            }
        }
    }
    $ad = $CACHE->clear();
    if ($ad) {
        showmsg('网站核心配置修改成功！', 1);
    } else {
        showmsg('网站核心配置修改失败！<br/>' . $DB->error(), 4);
    }
} elseif ($mod == 'other_n' && $_POST['do'] == 'submit') {
    if (!$_POST['submit']) {
        showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
    } else {
        foreach ($_POST as $key => $value) {
            if ($key != 'submit') {
                $value = strFilter($value);
                saveSetting($key, $value);
            }
        }
    }
    $ad = $CACHE->clear();
    if ($ad) {
        showmsg('网站附加功能修改成功！', 1);
    } else {
        showmsg('网站附加功能修改失败！<br/>' . $DB->error(), 4);
    }
} elseif ($mod == 'viewEncode_n' && $_POST['do'] == 'submit') {
    if (!$_POST['submit']) {
        showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
    } else {
        foreach ($_POST as $key => $value) {
            if ($key != 'submit') {
                $value = strFilter($value);
                saveSetting($key, $value);
            }
        }
    }
    $ad = $CACHE->clear();
    if ($ad) {
        showmsg('视图加密功能修改成功！', 1);
    } else {
        showmsg('视图加密功能修改失败！<br/>' . $DB->error(), 4);
    }
} elseif ($mod == 'tixian_n') {
    if (!$_POST['submit'] || !$_POST['do'] == 'submit') {
        showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
    } else {
        foreach ($_POST as $key => $value) {
            if ($key != 'submit') {
                $value = strFilter($value);
                saveSetting($key, $value);
            }
        }
    }

    $ad = $CACHE->clear();
    if ($ad) {
        showmsg('代理提现设置修改成功！', 1);
    } else {
        showmsg('代理提现设置修改失败！<br/>' . $DB->error(), 4);
    }
} elseif ($mod == 'recharge_n') {
    if (!$_POST['submit'] || !$_POST['do'] == 'submit') {
        showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
    } else {
        foreach ($_POST as $key => $value) {
            if ($key != 'submit') {
                $value = strFilter($value);
                saveSetting($key, $value);
            }
        }
    }
    $ad = $CACHE->clear();
    if ($ad) {
        showmsg('代理充值配置修改成功！', 1);
    } else {
        showmsg('代理充值配置修改失败！<br/>' . $DB->error(), 4);
    }
} elseif ($mod == 'cloud_n' && $_POST['do'] == 'submit') {
    if (!$_POST['submit']) {
        showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
    } else {

        $type = input('type');

        if ($type == 'sms') {
            $arr = [
                'sms_check_change_mobile',
                'sms_check_change_info',
                'sms_check_change_pwd',
                'sms_check_findpwd',
                'sms_check_login',
                'sms_check_register',
            ];
        } else {
            $arr = [
                'ems_check_change_mobile',
                'ems_check_change_info',
                'ems_check_change_pwd',
                'ems_check_findpwd',
                'ems_check_login',
                'ems_check_register',
            ];
        }

        $keys = array_keys($_POST);

        foreach ($arr as $key => $value) {
            if (in_array(trim($value), $keys)) {
                $data = strFilter($_POST[$value]);
                saveSetting($value, $data);
            } else {
                saveSetting($value, '0');
            }
        }

        foreach ($_POST as $key => $value) {
            if ($key != 'submit' && !in_array($key, $arr)) {
                if ($value == 'on') {
                    $value = '1';
                }
                $value = strFilter($value);
                saveSetting($key, $value);
            }
        }
    }
    $ad = $CACHE->clear();
    if ($ad) {
        showmsg(($type == 'sms' ? '短信' : '邮件') . '配置修改成功！', 1);
    } else {
        showmsg(($type == 'sms' ? '短信' : '邮件') . '配置修改失败！<br/>' . $DB->error(), 4);
    }
} elseif ($mod == 'rank_n') {
    if (!$_POST['submit'] || !$_POST['do'] == 'submit') {
        showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
    } else {
        foreach ($_POST as $key => $value) {
            if ($key != 'submit') {
                $value = strFilter($value);
                saveSetting($key, $value);
            }
        }
    }
    $ad = $CACHE->clear();
    if ($ad) {
        showmsg('分站排行奖励设置修改成功！', 1);
    } else {
        showmsg('分站排行奖励设置修改失败！<br/>' . $DB->error(), 4);
    }
} elseif ($mod == 'qiandao_n') {
    if (!$_POST['submit'] || !$_POST['do'] == 'submit') {
        showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
    } else {
        foreach ($_POST as $key => $value) {
            if ($key != 'submit') {
                saveSetting($key, $value);
            }
        }
    }
    $action = $_POST['action'];
    $action = $action ? $action : '分站签到';
    $ad     = $CACHE->clear();
    if ($ad) {
        showmsg($action . '配置修改成功！', 1);
    } else {
        showmsg($action . '配置修改失败！<br/>' . $DB->error(), 4);
    }
} elseif ($mod == 'save_n') {
    if (!$_POST['submit'] || !$_POST['do'] == 'submit') {
        showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
    } else {
        $action = input('post.action');
        if (!$action) {
            $action = '系统';
        }

        if ($webConfig['test']) {
            showmsg($action . '配置修改成功！[测试站操作不会生效]', 1);
        }

        $act = isset($_POST['act']) ? input('post.act', 1) : '';
        if ($act == 'priceCronList') {
            $PriceCronList = explode('|', input('post.PriceCronList', 1));
            $cids          = [];
            foreach ($PriceCronList as $key => $cid) {
                $row = $DB->get_row("SELECT * FROM `pre_class` WHERE cid = ?", [$cid]);
                if ($row) {
                    $cids[] = $cid;
                }
            }
            saveSetting('PriceCronList', implode('|', $cids));
        } else {
            $key_white_list = array(
                'template_purpleYear_right',
                'template_purpleYear_friendlink',
                'free_maxmsg',
                'free_okmsg',
                'free_mail_model',
                'freeblacklisttip',
                'faka_mail',
                'pay_alert',
            );
            foreach ($_POST as $key => $value) {
                if ($key != 'submit') {
                    if ($act !== "gonggao" && !in_array($key, $key_white_list)) {
                        //剥去html标签和标签样式
                        $value = input('post.' . $key, 1);
                    }
                    if (preg_match('/captcha_/', $key) && $value == 'on') {
                        $value = 1;
                    }
                    $value = strFilter($value);
                    saveSetting($key, $value);
                }
            }
        }
        $ad = $CACHE->clear();
        if ($ad) {
            showmsg($action . '修改成功！', 1);
        } else {
            showmsg($action . '修改失败！<br/>' . $DB->error(), 4);
        }
    }

} elseif ($mod == 'search') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">模糊搜索替换匹配功能设置</h3></div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
         <input type="hidden" name="action" value="模糊搜索功能"/>
        <div class="alert alert-info">
           使用此功能可将被搜索填写的词替换成另一个，以此提高搜索准确率！例如花费->话费
        </div>
        <input type="hidden" name="do" value="submit"/>
        <div class="form-group">
              <label class="col-sm-2 control-label">模糊替换功能开关</label>
              <div class="col-sm-10">
              <select class="form-control" id="search_replace_open" name="search_replace_open" default="';
    echo $conf['zz_search_replace_open'];
    echo '"><option value="0" >关闭</option><option value="1">仅代理</option><option value="2">无限制</option></select>
            <pre>仅代理表示只有注册了的代理可使用此功能，游客不可以；无限制表示已注册代理和首页游客都可以使用此功能</pre>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">模糊匹配规则</label>
              <div class="col-sm-10">
              <input type="text" id="search_replace_list" name="search_replace_list" value="';
    echo $conf['zz_search_replace_list'] != "" ? $conf['zz_search_replace_list'] : '**|DY,**|KS';
    echo '" class="form-control"/>
              <pre>填写方法：被替换的词|替换后的词，例如花费|话费，多个用英文逗号,隔开</pre>
              </div>
        </div>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>
    </form>
      </div>
    </div>
    ';
} elseif ($mod == 'pricejk') {
    echo '
    <div class="block">
        <div class="nav-tabs-alt">
          <ul class="nav nav-tabs nav-justified" data-toggle="tabs">
            <li class="active text-cmgg-xs">
              <a href="#cron_class" data-toggle="tab">
                监控分类列表
              </a>
            </li>
            <li class="text-cmgg-xs">
              <a href="#cron_set" data-toggle="tab">
                监控功能设置
              </a>
            </li>
          </ul>
          <div class="modal-body">
                <div id="myTabContent" class="tab-content">
                    <!--监控分类列表-->
                    <div class="tab-pane fade in active" id="cron_class">
                        <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
                        <input type="hidden" name="do" value="submit"/>
                        <input type="hidden" name="act" value="priceCronList"/>
                        <input type="hidden" name="action" value="监控分类列表"/>
                        <div id="alert_frame" class="alert" style=" background-color: #e6f7ff;color: #0c343a;">
                            1.功能说明：通过该功能可以把对接主流系统的商品实现自动同步价格和上下架，大大减少您的工作量<br>
                            2.详细步骤：第一步在下方表格勾选要监控的分类，第二步在最下方保存分类，第三步定时监控链接！备注：在表格内可上下滑动查看<br>
                            3.支持系统：亿樂、九五、同系统、商战网、时空云、卡商、卡易信、卡卡云、彩虹、直客SUP（后续支持系统会更多哦~）<br>
                            4.监控方法：详见&nbsp;<a href="./cron.php">监控任务</a>&nbsp;第三个【商品价格】<br>
                        </div>
                        <table class="table table-bordered" style="table-layout: fixed;">
                            <thead>
                            <tr><th style="width: 30%;max-width:100px;">选中[Cid]</th><th style="width: 50%;">分类名称</th><th style="width: 20%;">对接数量</th></tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="3">
                                        <div style="max-height:' . ($is_mb == false ? '400px' : '320px') . ';display:block;overflow: auto;">
                                        <table class="table table-bordered" style="table-layout: fixed;">
                                            <tbody>

                                    ';
    $rs     = $DB->query("SELECT * FROM `pre_class` WHERE (upcid is NULL OR upcid < 1) order by sort asc");
    $flRows = array();
    if ($rs) {
        $rows = $DB->fetchAll($rs);
        foreach ($rows as $key => $value) {
            $cid      = $value['cid'];
            $subcount = $DB->count("SELECT count(*) FROM `pre_class` WHERE `upcid`= ?", [$cid]);

            if ($subcount > 0) {
                $subRs = $DB->query("SELECT * FROM `pre_class` WHERE `upcid`= ?", [$cid]);
                if ($subRs) {
                    $value['num'] = 0;
                    $flRows[]     = $value;
                    $subRows      = $DB->fetchAll($subRs);
                    foreach ($subRows as $sub_key => $sub_row) {
                        $sub_cid = $sub_row['cid'];
                        $count   = $DB->count("SELECT count(*) FROM `pre_tools` WHERE `is_curl`=2 AND (`cid`={$sub_cid} OR {$sub_cid} IN (`cids`))");

                        $sub_row['num']  = intval($count);
                        $sub_row['name'] = '[上级:' . $cid . ']' . $sub_row['name'];
                        $flRows[]        = $sub_row;
                    }
                }
            } else {
                $count = $DB->count("SELECT count(*) FROM `pre_tools` WHERE `is_curl`=2 AND (`cid`={$cid} OR {$cid} IN (`cids`))");

                $value['num'] = intval($count);
                $flRows[]     = $value;
            }
        }
    }
    $cronArr   = explode('|', $conf['PriceCronList']);
    $classList = '';
    foreach ($flRows as $key => $row) {
        $style = '';
        $num   = 0;
        if ($row['num'] > 0) {
            $style = ' style="color:red;"';
            $num   = '<span style="color:red;">' . $row['num'] . '个</span>';
        }
        $classList .= '<tr>
                            <td style="width: 30%;max-width:100px;">
                            <input class="inp-cmckb-xs clist" id="cid' . $row['cid'] . '" type="checkbox" value="' . $row['cid'] . '" ' . (in_array($row['cid'], $cronArr) ? 'checked="checked"' : '') . ' onclick="setCronList(this)" style="display: none;"/>
                            <label class="cmckb-xs" for="cid' . $row['cid'] . '" style="padding: 1px 4px;"><span>
                              <svg width="12px" height="10px">
                                <use xlink:href="#checkSvg"></use>
                              </svg>
                            </span><span' . $style . '>' . $row['cid'] . '</span></label>
                            </td>
                            <td style="width: 50%;">
                            <span' . $style . '>' . $row['name'] . '</span>
                            </td>
                            <td style="width: 20%;">
                            ' . $num . '
                            </td>
                            </tr>
                            ';
    }

    echo $classList;

    echo '                    </tbody>
                                        </table>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!--SVG Sprites-->
                        <svg class="inline-svg">
                          <symbol id="checkSvg" viewbox="0 0 12 10">
                            <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                          </symbol>
                        </svg>
                        <div class="form-group">
                              <label class="col-sm-2 control-label">当前监控分类列表</label>
                              <div class="col-sm-10">
                               <input type="text" id="PriceCronList" name="PriceCronList" placeholder="当前监控分类列表为空" value="' . $conf['PriceCronList'] . '" class="form-control"/>
                               <small style="color:red">需要价格监控必须勾选要监控的分类并保存，否则无法更新价格！</small>
                              </div>
                        </div>
                        <div class="form-group">
                          <div class="col-sm-12">
                          <input type="submit" name="submit" value="保存监控分类列表" class="btn btn-primary form-control"/><br/>
                          </div>
                        </div>
                        </form>
                    </div>
                    <!--监控分类列表 end-->
                    <!--监控功能设置-->
                    <div class="tab-pane fade" id="cron_set">
                            <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
                            <input type="hidden" name="do" value="submit"/>
                            <input type="hidden" name="action" value="价格监控功能"/>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">在以下情况修改价格</label>
                                  <div class="col-sm-10">
                                   <select class="form-control" id="pricejk_edit" name="pricejk_edit" default="' . $conf['pricejk_edit'] . '">
                                   <option value="1">监控时始终修改价格（推荐）</option>
                                   <option value="0">专业价格小于等于社区价格时</option>
                                   <option value="2">只更新成本价格不改任何售价（不推荐）</option>
                                   </select>
                                  </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">关闭用户后台价格变动通知</label>
                                  <div class="col-sm-10">
                                   <select class="form-control" id="pricejk_show_close" name="pricejk_show_close" default="' . $conf['pricejk_show_close'] . '">
                                   <option value="0">开启</option>
                                   <option value="1">关闭</option>
                                   </select>
                                  </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">彩虹、卡卡云等可批量系统单次更新最大商品数量</label>
                                  <div class="col-sm-10">
                                  <input type="text" id="pricejk_maxnum" name="pricejk_maxnum" value="';

    if ($conf['pricejk_maxnum'] < 300 || $conf['pricejk_maxnum'] > 3000) {
        $conf['pricejk_maxnum'] = 500;
    }
    echo $conf['pricejk_maxnum'];
    echo '" class="form-control"/>
                                  <pre>如果出现502 Bad Gateway，请填写更小的数量直到不提示502超时为止！默认为500个，最大3000个，最小300个</pre>
                                  </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">直客、商战等不可批量系统单次更新最大商品数量</label>
                                  <div class="col-sm-10">
                                  <input type="text" id="pricejk_maxnum_yile" name="pricejk_maxnum_yile" value="';
    if ($conf['pricejk_maxnum_yile'] < 120 || $conf['pricejk_maxnum_yile'] > 800) {
        $conf['pricejk_maxnum_yile'] = 200;
    }
    echo $conf['pricejk_maxnum_yile'];
    echo '" class="form-control"/>
                                  <pre>如果出现502 Bad Gateway，请填写更小的数量直到服务器不会出现502提示！默认为200个，最大800个，最小120个</pre>
                                  </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">监控商品过滤线</label>
                                  <div class="col-sm-10">
                                  <div class="input-group">
                                    <input type="text" id="cron_maxprice" name="cron_maxprice" value="';
    echo $conf['cron_maxprice'] > 0 ? $conf['cron_maxprice'] : '8888';
    echo '" class="form-control"/>
                                    <span class="input-group-addon">元</span>
                                  </div>
                                  <pre>当商品售价大于等于此价格就不监控！用于部分只起到提示作用的商品过滤，提升监控效率减少性能占用</pre>
                                  </div>
                            </div>
                             <div class="form-group">
                                  <label class="col-sm-2 control-label">亿樂价格更新间隔</label>
                                  <div class="col-sm-10">
                                  <div class="input-group">
                                    <input type="text" id="pricejk_time" name="pricejk_time" value="';
    echo $conf['pricejk_time'] >= 120 ? $conf['pricejk_time'] : '120';
    echo '" class="form-control"/>
                                    <span class="input-group-addon">秒</span>
                                  </div>
                                  <pre>用于亿樂商品下单时同步商品价格的间隔时间.最低2分钟一次</pre>
                                  </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">关闭同步商品上下架</label>
                                  <div class="col-sm-10">
                                  <select class="form-control" id="cron_status_sync" name="cron_status_sync" default="';
    echo $conf['cron_status_sync'];
    echo '"><option value="0">开启同步(默认)</option><option value="1">关闭同步</option>
                                </select>
                                <pre>开启后在使用价格监控同步功能时会自动修改上下架状态，按需求使用即可</pre>
                                </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">同步商品图片</label>
                                  <div class="col-sm-10">
                                  <select class="form-control" id="corn_shopimg" name="corn_shopimg" default="';
    echo $conf['corn_shopimg'];
    echo '"><option value="0" >关闭</option><option value="1">开启</option>
                                </select>
                                <pre>仅部分系统支持，随着更新会支持更多</pre>
                                </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">同步库存</label>
                                  <div class="col-sm-10">
                                  <select class="form-control" id="corn_stock" name="corn_stock" default="';
    echo $conf['corn_stock'];
    echo '"><option value="0">关闭</option><option value="1">开启</option>
                                </select>
                                <pre>仅支持同系统、彩虹、直客等部分系统（同系统对接站那边商品需要开启“库存控制”，如果对接平台系统更新可能导致失效）</pre>
                                </div>
                            </div>
                             <div class="form-group">
                                  <label class="col-sm-2 control-label">同步商品描述/简介</label>
                                  <div class="col-sm-10">
                                  <select class="form-control" id="corn_desc" name="corn_desc" default="';
    echo $conf['corn_desc'];
    echo '"><option value="0">关闭</option><option value="1">开启</option>
                                </select>
                                <pre>仅部分系统支持，随着更新会支持更多</pre>
                                </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">上下架同步到显示隐藏</label>
                                  <div class="col-sm-10">
                                  <select class="form-control" id="cron_status_close" name="cron_status_close" default="';
    echo $conf['cron_status_close'] == 1 ? '1' : '0';
    echo '"><option value="0" >关闭</option><option value="1">开启</option>
                                </select>
                                <pre>开启后检测到商品已上架就同步为显示，检测到商品已下架就同步为隐藏</pre>
                                </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">开启默认加价模板</label>
                                  <div class="col-sm-10">
                                  <select class="form-control" id="tool_price_open" name="tool_price_open" default="';
    echo $conf['tool_price_open'];
    echo '"><option value="0" >关闭</option><option value="1">开启</option>
                                </select>
                                <small><span style="color:red;">有免费商品时不建议开启</span>&nbsp;开启后未设置加价模板的商品，在使用价格监控时会自动根据下方设置的比例加价</small>
                                </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">默认加价方式</label>
                                  <div class="col-sm-10">
                                  <select class="form-control" id="tool_price_kind" name="tool_price_kind" default="';
    echo $conf['tool_price_kind'];
    echo '"><option value="1" >1_直接累加</option><option value="2">2_百分比加价</option>
                                </select>
                                <pre>直接累加：按成本价累加，比如成本价10元，出售价加价填0.5元，最终出售价格就是10.5元；</pre>
                                </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">出售价加价</label>
                                  <div class="col-sm-10">
                                  <input type="text" id="tool_price_0" name="tool_price_0" value="';
    echo $conf['tool_price_0'] > 0 ? $conf['tool_price_0'] : '13';
    echo '" class="form-control"/>
                                  <pre>加价方式为直接累加时填整数和小数（小数可后两位）；加价方式为百分比加价时填整数，填100就是100%，加1倍的价格（价格X2），可超过100</pre>
                                  </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">专业版加价</label>
                                  <div class="col-sm-10">
                                  <input type="text" id="tool_price_1" name="tool_price_1" value="';
    echo $conf['tool_price_1'] > 0 ? $conf['tool_price_1'] : '10';
    echo '" class="form-control"/>
                                  <pre>加价方式为直接累加时填整数和小数（小数可后两位）；加价方式为百分比加价时填整数，填100就是100%，加1倍的价格（价格X2），可超过100</pre>
                                  </div>
                            </div>
                            <div class="form-group">
                                  <label class="col-sm-2 control-label">旗舰版加价</label>
                                  <div class="col-sm-10">
                                  <input type="text" id="tool_price_2" name="tool_price_2" value="';
    echo $conf['tool_price_2'] > 0 ? $conf['tool_price_2'] : '8';
    echo '" class="form-control"/>
                                  <pre>加价方式为直接累加时填整数和小数（小数可后两位）；加价方式为百分比加价时填整数，填100就是100%，加1倍的价格（价格X2），可超过100</pre>
                                  </div>
                            </div>
                            <div class="form-group">
                              <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
                              </div>
                            </div>
                        </form>
                    </div>
                    <!--监控功能设置 end-->
                </div>
            </div>
        </div>
    </div>
    </div>
    <script>
    function setCronList(el){
        var PriceCronList = $("#PriceCronList").val();
        var cids = PriceCronList.split("|");
        if(el.checked){
            var type = "add";
        }
        else{
            var type = "remove";
        }
        var list = getCronArr(cids,el.value,type);
        $("#PriceCronList").val(list);
    }

    function getCronArr(cids,cid,type){
        var arr= new Array();
        var isIn = false;
        $.each(cids, function(i, v) {
            if(v!=""){
                if(v == cid){
                    isIn = true;
                    if(type=="add"){
                        arr.push(cid);
                    }
                }
                else{
                   arr.push(v);
                }
            }
        });

        if(isIn == false && type == "add"){
            arr.push(cid);
        }
        if(arr.length > 1){
            return arr.join("|");
        }
        return arr.join("");

    }
    </script>
    ';
} elseif ($mod == 'workorder') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">工单系统设置</h3></div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
          <input type="hidden" name="do" value="submit"/>
          <input type="hidden" name="action" value="工单系统"/>
          <div class="form-group">
          <label class="col-sm-2 control-label">分站可提交工单</label>
          <div class="col-sm-10"><select class="form-control" name="workorder_open" default="';
    echo $conf['zz_workorder_open'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">开启工单可上传图片</label>
          <div class="col-sm-10"><select class="form-control" name="workorder_image" default="';
    echo $conf['workorder_image'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
        </div><br/>
          <div class="form-group hide">
            <label class="col-sm-2 control-label">工单中图片储存方式</label>
            <div class="col-sm-10"><select class="form-control" name="workorder_image_api" default="';
    echo $conf['workorder_image_api'];
    echo '"><option value="0">本地(不推荐)</option><option value="1">FTP储存(推荐一)</option><option value="2">阿里云OSS(推荐二)</option></select>
            <small>FTP储存和阿里云OSS需要先配置好文件上传，<a href="./set.php?mod=uploadFile">点我配置文件上传储存</a></small>
            </div>
          </div><br/>
          <div class="form-group">
          <label class="col-sm-2 control-label">用户可查看工单图片</label>
          <div class="col-sm-10"><select class="form-control" name="workorder_image_read" default="';
    echo $conf['workorder_image_read'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
    <small>储存方式使用【阿里云OSS】时访问图片时会根据访问量计费，用户刷访问导致计量付费超标时可关闭！<a href="./set.php?mod=uploadFile">点我配置文件上传储存</a></small>
    </div>
        </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">首页游客订单可申请限制</label>
            <div class="col-sm-10"><select class="form-control" name="complain_limit" default="';
    echo $conf['zz_complain_limit'];
    echo '"><option value="0">关闭限制</option><option value="7">7天内可申请</option><option value="15">15天内可申请</option><option value="31">1个月内可申请</option><option value="90">3个月内可申请</option><option value="182">6个月内可申请</option><option value="365">12个月内可申请</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">实时检测周期间隔</label>
            <div class="col-sm-10">
                <div class="input-group">
                <input type="text" name="work_notice_speed" value="';
    echo $conf['zz_work_notice_speed'] > 0 ? $conf['zz_work_notice_speed'] : '30';
    echo '" class="form-control"/>
                <span class="input-group-addon">秒</span>
                </div>
                <pre>循环检测周期时间，默认30秒</pre>
            </div>
          </div><br/>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>
    </form>
      </div>
    </div>
    ';
} elseif ($mod == 'message') {
    $themeList = getArticleThemeList();
    $theme     = !empty($conf['zz_article_theme']) ? $conf['zz_article_theme'] : 'default';
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">文章伪静态</h3></div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
          <input type="hidden" name="do" value="submit"/>
          <input type="hidden" name="action" value="文章伪静态"/>
          <div class="form-group">
            <label class="col-sm-2 control-label">网站首页显示文章</label>
            <div class="col-sm-10"><select class="form-control" name="index_article" default="';
    echo $conf['zz_index_article'];
    echo '"><option value="0">关闭显示</option><option value="1">开启显示</option></select>
            <pre>目前部分模板支持，如果出现页面错乱等问题请在此处关闭，后续版本将支持更多模板</pre>
            </div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">文章使用主题</label>
            <div class="col-sm-10"><select class="form-control" name="article_theme" default="';
    echo $conf['zz_article_theme'];
    echo '">';
    foreach ($themeList as $name) {
        echo '<option value="' . $name . '">' . $name . '</option>';
    }
    echo '    </select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">文章是否开启伪静态</label>
            <div class="col-sm-10"><select class="form-control" name="article_static" default="';
    echo $conf['zz_article_static'] > 0 ? '1' : '0';
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
           <pre>伪静态有助于搜索引擎收录文章，需要在文章管理页面根据提示配置好伪静态规则后才能使用！</pre>
          </div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">是否开启搜索功能</label>
            <div class="col-sm-10"><select class="form-control" name="article_search" default="';
    echo $conf['zz_article_search'] > 0 ? '1' : '0';
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">文章默认显示数量</label>
            <div class="col-sm-10"><input type="text" name="message_page" value="';
    echo $conf['zz_message_page'];
    echo '" class="form-control"/>
            </div>
          </div><br/>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>
    </form>
      </div>
    </div>
    ';
} elseif ($mod == 'uploadFile') {
    echo '
    <!--modal-->
    <div class="modal fade" align="left" id="help" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
            <h4 class="modal-title" id="myModalLabel">FTP文件上传失败帮助</h4>
          </div>
          <div class="modal-body">
            <H4 style="color:red">一、FTP静态站和网站使用同一个服务器</H4>
            <p>由于FTP存在不同防火墙的原因，如果你网站和FTP静态站不是同一个服务器，可能会导致不同之间的服务器无法正常接通，这时候可以尝试使用同一个服务器来解决</p>

            <H4 style="color:red">二、FTP地址使用内网IP/内网地址</H4>
            <p>部分高防主机服务器，拦截了某些端口，导致外网链接FTP时无法正常运作，你可以尝试使用内网IP/内网地址再试试（必须和网站是同一个服务器或者同一个IP段）</p>

            <H4 style="color:red">三、内网IP/内网地址获取方法</H4>
            <p>可以先填【127.0.0.1】试试；内网IP/内网地址通常是和外网IP在控制面板上的一个地方，如果没有写，你可以咨询主机提供商客服！内网IP每个服务器都有，说没有的那肯定是骗你</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
          </div>
        </div>
      </div>
    </div>
    <!--modal end-->
    <div class="block">
    <div class="block-title"><h3 class="panel-title">文件储存设置</h3></div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
          <div class="alert alert-info">
             有条件的建议开启ftp模式。静态站点放图片更安全！<br>
             FTP教程步骤一：在填写前请使用任意一个域名搭建一个静态站点（放图片用的，宝塔用户的php版本选择静态）<br>
             FTP教程步骤二：静态图片站点里面，创建一个名为images的目录<br>
             FTP教程步骤三：开始把静态图片站点的资料在下方填写好并保存！<br>
             注意事项：务必使用同一个类型主机或同一台服务器搭建静态图片站，否则可能会上传失败
          </div>
          <input type="hidden" name="do" value="submit"/>
          <input type="hidden" name="action" value="文件储存模式"/>
          <div class="form-group">
            <label class="col-sm-2 control-label">文件储存模式</label>
            <div class="col-sm-10">
            <select class="form-control" name="file_type" onChange="fileShow(this.value)" default="';
    echo $conf['file_type'] > 0 ? $conf['file_type'] : '0';
    echo '"><option value="0">本地（不推荐）</option><option value="1">FTP储存（推荐一）</option><option value="2">阿里云OSS（推荐二）</option></select>
                <small style="color:red">使用【本地】有被入侵服务器的风险，建议使用FTP储存和阿里云OSS</small>
                </div>
          </div><br/>
          <div id="file_ftp_display" style="display:none;">
              <div class="form-group">
                <label class="col-sm-2 control-label">图片站点地址</label>
                <div class="col-sm-10">
                <input type="text" name="file_ftp_url" value="';
    echo $conf['zz_file_ftp_url'];
    echo '" class="form-control"/>
                <pre>填写完整的静态图片站点网页地址，如http://img.baidu.cn/</pre>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">FTP地址</label>
                <div class="col-sm-10">
                <input type="text" name="file_ftp_server" value="';
    if (empty($conf['file_ftp_server'])) {
        $conf['file_ftp_server'] = '127.0.0.1';
    }
    echo $conf['file_ftp_server'];
    echo '" class="form-control"/>
                <pre><span style="color:red;">默认填127.0.0.1不懂不要乱动！</span>格式必须是145.554.869.35或ftp.baidu.cn这两种！<br><a href="#help" data-toggle="modal" data-target="#help">点我查看上传失败解决建议</a></pre>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">FTP端口</label>
                <div class="col-sm-10">
                <input type="text" name="file_ftp_port" value="';
    echo $conf['zz_file_ftp_port'];
    echo '" placeholder="不填默认21" class="form-control"/>
                <pre>请认真填写，否则无法成功上传</pre>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">FTP用户名</label>
                <div class="col-sm-10">
                <input type="text" name="file_ftp_username" value="';
    echo $conf['zz_file_ftp_username'];
    echo '" placeholder="FTP用户名" class="form-control"/>
                <pre>请认真填写，否则无法成功上传</pre>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">FTP用户密码</label>
                <div class="col-sm-10">
                <input type="text" name="file_ftp_password" value="';
    echo $conf['zz_file_ftp_password'];
    echo '" placeholder="FTP用户密码" class="form-control"/>
                <pre>请认真填写，否则无法成功上传</pre>
                </div>
              </div>
          </div>
          <div id="file_alioss_display" style="display:none;">
              <div class="form-group">
                <label class="col-sm-2 control-label">阿里云accessKeyId</label>
                <div class="col-sm-10">
                <input type="text" name="file_alioss_accessKeyId" value="';
    echo $conf['file_alioss_accessKeyId'];
    echo '" class="form-control"/>
                <small>阿里云【RAM访问控制】获得：<a href="https://ram.console.aliyun.com/manage/ak">ram.console.aliyun.com/manage/ak</a>，第一次用时需要创建一个</small>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">阿里云accessKeySecret</label>
                <div class="col-sm-10">
                <input type="text" name="file_alioss_accessKeySecret" value="';
    echo $conf['file_alioss_accessKeySecret'];
    echo '" class="form-control"/>
                <small>阿里云【RAM访问控制】获得：<a href="https://ram.console.aliyun.com/manage/ak">ram.console.aliyun.com/manage/ak</a>，第一次用时需要创建一个</small>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">名称Bucket</label>
                <div class="col-sm-10">
                <input type="text" name="file_alioss_bucketname" value="';
    echo $conf['file_alioss_bucketname'];
    echo '" class="form-control"/>
                <small><span style="color:red">创建时[存储类型]选【低频访问存储】或【标准存储】，[读写权限]选【公共读】</span>&nbsp;阿里云OSS控制台Bucket列表获得！控制台：<a href="https://oss.console.aliyun.com/bucket">oss.console.aliyun.com</a>；创建教程：教程见后台官方公告新手教程汇总！</small>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">地域节点Endpoint</label>
                <div class="col-sm-10">
                <input type="text" name="file_alioss_endpoint" value="';
    echo $conf['file_alioss_endpoint'];
    echo '" class="form-control"/>
                <small>阿里云OSS控制台Bucket列表获得！控制台：<a href="https://oss.console.aliyun.com/bucket">oss.console.aliyun.com</a>；创建教程：教程见后台官方公告新手教程汇总</small>
                </div>
              </div>
          </div>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
          </div>
          </div>
          </form>
        </div>
        <script>
        function fileShow(val){
            $("#file_ftp_display").hide();
            $("#file_alioss_display").hide();
            if(val == 1){
                $("#file_ftp_display").show();
            }
            else if(val == 2){
                $("#file_alioss_display").show();
            }
            else{
                $("#file_ftp_display").hide();
            }
        }
        </script>
    ';
} elseif ($mod == 'freeSet') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">免费商品功能设置</h3></div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
          <div class="alert alert-info">
             此处的功能全部是跟免费商品有关的，请根据需求设置！
          </div>
          <input type="hidden" name="do" value="submit"/>
          <input type="hidden" name="action" value="免费商品"/>
          <div class="form-group">
              <label class="col-sm-2 control-label">免费商品限制开关</label>
              <div class="col-sm-10">
                <select class="form-control" name="free_max_open" default="' . $conf['zz_free_max_open'] . '">
                  <option value="0">关闭限制</option><option value="1">开启限制</option>
                </select>
                <pre>开启后新用户领取免费商品次数将被限制，不开启无限领取</pre>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">单个商品领取上限</label>
              <div class="col-sm-10">
                <div class="input-group">
                <input type="text" name="free_max" value="' . $conf['zz_free_max'] . '" class="form-control"/>
                <span class="input-group-addon">次</span>
                </div>
              </div>
            </div><br/>
            <div class="form-group">
              <label class="col-sm-2 control-label">免费商品上限提示</label>

              <div class="col-sm-10"><textarea style="min-height: 80px;height: 80px;" class="form-control" name="free_maxmsg" rows="5">' . htmlspecialchars($conf['zz_free_maxmsg']) . '</textarea>
               <pre>支持html代码</pre>
              </div>
            </div><br/>
            <div class="form-group">
              <label class="col-sm-2 control-label">免费商品成功提示</label>

              <div class="col-sm-10"><textarea style="min-height: 80px;height: 80px;" class="form-control" name="free_okmsg" rows="5">' . htmlspecialchars($conf['zz_free_okmsg']) . '</textarea>
              <pre>支持html代码</pre>
              </div>
            </div><br/>
            <div class="form-group">
              <label class="col-sm-2 control-label">免费商品下单黑名单</label>
              <div class="col-sm-10"><textarea class="form-control" name="freeblacklist" rows="3">';
    echo $conf['zz_freeblacklist'];
    echo '</textarea></div>
            </div><br/>
            <div class="form-group">
              <label class="col-sm-2 control-label">免费商品禁止下单提示内容</label>
              <div class="col-sm-10"><textarea class="form-control" name="freeblacklisttip" rows="3">';
    echo $conf['zz_freeblacklisttip'];
    echo '</textarea>
                <pre>支持html代码</pre>
                </div>
            </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">是否开启免费商品邮件</label>
              <div class="col-sm-10"><select class="form-control" name="free_mail_open" default="';
    echo $conf['zz_free_mail_open'] > 0 ? '1' : '0';
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
            <pre>开启后，用户下单免费商品将会发一封通知邮件，可拉拢客户 <p style="color:red">注意，国外服务器无法使用QQ邮箱，且容易频繁、拦截，建议使用阿里云邮箱&nbsp;<a href="./set.php?mod=mail">点我配置</a></p></pre>
            </div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">免费商品邮件标题</label>
            <div class="col-sm-10"><input class="form-control" name="free_mail_title" id="free_mail_title" value="';
    echo $conf['free_mail_title'];
    echo '"/>';
    echo "\r\n";
    echo '</div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">免费商品邮件内容模板</label>
            <div class="col-sm-10"><textarea class="form-control" name="free_mail_model" id="free_mail_model" rows="6">';
    echo $conf['zz_free_mail_model'];
    echo "\r\n";
    echo '</textarea></div>
          </div>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="提交修改" class="btn btn-primary form-control"/><br/><br/>
            <a href="./set.php?mod=free_mail_reset" class="btn btn-warning btn-block" onclick="return confirm(\'确定要重置吗？\');">重置模板设置</a><br/>
          </div>
          </div>
          </form>
        </div>
        <div class="panel-footer">
          <font color="green">变量代码：<br/>
          <a href="#" onclick="Addstr(\'free_mail_model\',\'[br]\');return false">[br]</a>&nbsp;换行<br/>
          <a href="#" onclick="Addstr(\'free_mail_model\',\'[name]\');return false">[name]</a>&nbsp;商品名称<br/>
          <a href="#" onclick="Addstr(\'free_mail_model\',\'[alert]\');return false">[alert]</a>&nbsp;商品简介<br/>
          <a href="#" onclick="Addstr(\'free_mail_model\',\'[date]\');return false">[date]</a>&nbsp;购买时间<br/>
          <a href="#" onclick="Addstr(\'free_mail_model\',\'[sitename]\');return false">[sitename]</a>&nbsp;站点名称<br/>
          <a href="#" onclick="Addstr(\'free_mail_model\',\'[siteurl]\');return false">[siteurl]</a>&nbsp;站点URL<br/>
          </div>
          </div>
          <script>
          function Addstr(id, str) {
            $("#"+id).val($("#"+id).val()+str);
          }
          </script>
    ';
} elseif ($mod == 'rank') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">分站排行奖励设置</h3></div>
    <div class="">
      <form action="./set.php?mod=rank_n" method="post" class="form-horizontal" role="form">
        <div class="alert alert-info">
           此奖励将根据分站对应的销售额发放，关于限制某些商品不参与奖励的后面更新！<br>
           自动发放奖励监控链接（手动访问也可以）：<a href="' . $weburl . 'cron.php?act=rankCron&key=' . $conf['zz_cronkey'] . '">' . $weburl . 'cron.php?act=rankCron&key=' . $conf['zz_cronkey'] . '</a>
        </div>
        <input type="hidden" name="do" value="submit"/>
        <div class="form-group">
              <label class="col-sm-2 control-label">排行奖励开关</label>
              <div class="col-sm-10">
              <select class="form-control" id="fenzhan_rank_open" name="fenzhan_rank_open" default="';
    echo $conf['zz_fenzhan_rank_open'];
    echo '"><option value="0" >关闭排行奖励</option><option value="1">开启排行奖励</option></select>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">普通用户计入排行奖励</label>
              <div class="col-sm-10">
              <select class="form-control" id="fenzhan_rank_user" name="fenzhan_rank_user" default="';
    echo $conf['zz_fenzhan_rank_user'];
    echo '"><option value="0" >关闭</option><option value="1">开启</option></select>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">排名奖励名次</label>
              <div class="col-sm-10">
              <input type="text" id="fenzhan_rank_limit" name="fenzhan_rank_limit" value="';
    echo $conf['zz_fenzhan_rank_limit'] ? $conf['zz_fenzhan_rank_limit'] : '10';
    echo '" class="form-control"/>
              <pre>整数，要奖励的名次，例如奖励前五名（包括第五名），就填写5即可</pre>
              </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">奖励百分比</label>
              <div class="col-sm-10">
              <input type="text" id="fenzhan_rank_rate" name="fenzhan_rank_rate" value="';
    echo $conf['zz_fenzhan_rank_rate'] ? $conf['zz_fenzhan_rank_rate'] : '5';
    echo '" class="form-control"/>
              <pre>整数，根据销售金额奖励多少百分比，例如奖励5%，不超过100（这是废话）</pre>
              </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">发放大概时间</label>
              <div class="col-sm-10">
              <input type="text" id="fenzhan_rank_time" name="fenzhan_rank_time" value="';
    echo $conf['zz_fenzhan_rank_time'] ? $conf['zz_fenzhan_rank_time'] : '23:55:00';
    echo '" class="form-control"/>
              <pre>每天发放奖励的大概时间，必须设置为当前快结束的时间，如23:55:00，监控链接的执行时间得大于此设置时间才行！</pre>
              </div>
        </div>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>
    </form>
      </div>
    </div>
    ';
} elseif ($mod == 'bugWork_n') {
    $title   = input('post.bug_title', 1);
    $content = input('post.bug_content', 0);
    $type    = input('post.bug_type', 1);
    $module  = input('post.bug_module', 1);
    $step    = input('post.bug_step', 1);
    if (empty($title) || empty($content)) {
        showmsg('请确保工单标题或工单内容都不为空', 4);
    }
    $data = sendBugWorks($title, $content, $type, $module, $step);
    if ($data['code'] == 0) {
        showmsg($data['msg'], 1);
    } else {
        showmsg($data['msg'], 4);
    }
} elseif ($mod == 'bugWork') {
    $editor_load = true;
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">在线提交Bug反馈工单</h3></div>
    <div class="">
      <form action="./set.php?mod=bugWork_n" method="post" class="form-horizontal" role="form">
        <div class="alert alert-info">
           首先感谢您对本程序的支持，有什么好的建议可以在下方提交给我们哦！<br>
           怎么反馈下面提供了3个示例，可参考！bug反馈需要说明具体那个功能和是什么配置才导致不能用的，或者说明复现步骤！<br>
        </div>
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="bug_module" value="" class="form-control"/>
        <div class="form-group">
            <label class="col-sm-2 control-label">反馈类型</label>
            <div class="col-sm-10">
              <select id="bug_type" name="bug_type" class="form-control">
                  <option value="0">商城bug</option>
                  <option value="1">功能建议</option>
                  <option value="2">插件bug</option>
                  <option value="3">其他</option>
              </select>
              <small>如果是有bug请详细描述和说明复现步骤，否则<span style="color:red">将不能及时采纳更新！</span><a href="javascript:;" id="example1" style="color:#379ecc">示例1</a>&nbsp;<a href="javascript:;" id="example2" style="color:#379ecc">示例2</a>&nbsp;<a href="javascript:;" id="example3" style="color:#379ecc">示例3</a></small>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">所属板块</label>
            <div class="col-sm-10">
              <select id="bug_module" name="bug_module" class="form-control">
                  <option value="0">后台</option>
                  <option value="1">分站</option>
                  <option value="2">模板</option>
                  <option value="3">支付</option>
                  <option value="4">对接</option>
                  <option value="5">插件</option>
                  <option value="6">其他（可补充在内容里面）</option>
              </select>
            </div>
            <small></>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">标题</label>
              <div class="col-sm-10">
              <input type="text" id="bug_title" name="bug_title" value="" class="form-control"/>
              </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">内容</label>
            <div class="col-sm-10">
            <div id="editorBox" style="display:none"></div>
            <textarea name="bug_content" id="bug_content" cols="30" rows="10" class="form-control textDom hide"></textarea>
            </div>
        </div>
        <div class="form-group" id="bug_step_box" style="display:none;">
              <label class="col-sm-2 control-label">bug复现步骤</label>
              <div class="col-sm-10">
                  <div id="editorBox2"></div>
                  <textarea name="bug_step" id="bug_step" cols="30" rows="10" class="form-control textDom2 hide"></textarea>
              </div>
        </div>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="提交Bug反馈工单" class="btn btn-primary form-control"/><br/>
        </div>
    </form>
      </div>
    </div>
    <script type="text/javascript">
    $(document).on("change", "#bug_type", function(event) {
        event.preventDefault();
        var type = parseInt($(this).val());
        if (type > 0) {
            $("#bug_step_box").hide();
        } else {
            $("#bug_step_box").show();
        }
    });
    $(document).on("change", "#bug_module", function(event) {
        event.preventDefault();
        var bug_module = parseInt($(this).val());
        if (bug_module == 5) {
            $("#bug_step_box").hide();
        }else{
            $("bug_type").change();
        }
    });
    $(document).on("click", "#example1", function(event) {
        event.preventDefault();
        $("#bug_type").val(0);
        $("#bug_module").val(2);
        $("#bug_title").val("模板faka2购买页面购物车的入口出不来");
        $("#bug_content").val("模板faka2的购买页面，把商品加入购物车后。<br/>下方没有出现购物车订单信息和购物车入口，客户没法直接进入购物车付款<br/>【此处附上相应截图】");
        $("#bug_step_box").show();
        $("#bug_step").val("1.打开网站首页<br/>2.选择商品<br/>3.输入信息，加入购物车");
        editorChange();
    });
    $(document).on("click", "#example2", function(event) {
        event.preventDefault();
        $("#bug_type").val(0);
        $("#bug_module").val(4);
        $("#bug_title").val("对接卡易信下单后不能同步订单状态");
        $("#bug_content").val("对接卡易信能成功下单，有对接订单号，监控也挂了的，但是不能成功同步订单<br/>【此处附上相应截图】");
        $("#bug_step_box").hide();
        $("#bug_step_box").val("");
        editorChange();
    });
    $(document).on("click", "#example3", function(event) {
        event.preventDefault();
        $("#bug_type").val(2);
        $("#bug_module").val(5);
        $("#bug_title").val("插件【手机端两边间距调整】某某个模板不能用");
        $("#bug_content").val("【手机端两边间距调整】在这个faka模板改了设置的边距不生效，只会生效默认的。希望尽快修一下，其他模板还未测试");
        $("#bug_step_box").hide();
        $("#bug_step_box").val("");
        editorChange();
    });
    </script>
    ';
} elseif ($mod == 'invite') {
    $row['name'] = "请先填写商品ID，保存后刷新";
    if ($conf['invite_tid'] > 0) {
        $row = $DB->get_row("select * FROM pre_tools where tid= ?", array($conf['invite_tid']));
        if (!$row) {
            $row['name'] = "当前商品不存在，请确认后重新填写";
        }
    }
    echo '<div class="block">
    <div class="block-title">
    <h3 class="panel-title">推广送赞设置</h3>
    </div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="action" value="推广送赞功能"/>
        <div class="form-group">
              <label class="col-sm-2 control-label">是否开启推广送赞</label>
              <div class="col-sm-10">
              <select class="form-control" id="invite_open" name="invite_open" default="';
    echo $conf['zz_invite_open'];
    echo '"><option value="0" >关闭</option><option value="1">开启</option></select>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">赠送商品ID</label>
              <div class="col-sm-10">
              <input type="text" id="invite_tid" name="invite_tid" value="';
    echo $conf['zz_invite_tid'];
    echo '" class="form-control"/>
              <pre>在商品列表，点击商品进入，在地址栏中tid=后面的数字即为商品ID</pre>
              </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">增送商品名称</label>
              <div class="col-sm-10">
              <input type="text" value="';
    echo $row['name'];
    echo '" class="form-control" disabled/>
              </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">赠送金额条件限制</label>
              <div class="col-sm-10">
              <input type="text" name="invite_min" value="';
    echo $conf['zz_invite_min'];
    echo '" class="form-control"/>
              <pre>当被推广人下单时的金额满足此金额时，才会发放奖励。不填或为0则不限制金额</pre>
              </div>
        </div>
        <div class="form-group">
          <div class="col-sm-12"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>
    </form>
      </div>
    </div>
    ';
} elseif ($mod == 'cron_set') {
    echo '<div class="block">
    <div class="block-title">
        <h3 class="panel-title">监控设置</h3>
    </div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="action" value="监控"/>
        <div class="col-sm-12 col-md-10 col-lg-10" style="float:none;margin:auto">
            <div class="form-group">
                <div class="input-group">
                <div class="input-group-addon">进行中订单超时天数</div>
                <input class="form-control" type="number" name="cron_order_timeout" value="';
    echo $conf['cron_order_timeout'] > 0 ? $conf['cron_order_timeout'] : 7;
    echo '">
                </div>
            </div>
            <div class="form-group">
                <div class="input-group">
                <div class="input-group-addon">进行中订单超时动作</div>
                <select name="cron_order_zt" class="form-control" default="';
    echo $conf['cron_order_zt'];
    echo '">
                <option value="0">异常（默认）</option>
                <option value="1">已完成</option>
                <option value="2">不处理</option>
                </select>
                </div>
            </div>
        <div class="form-group">
          <div class="col-sm-12"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>
    </form>
      </div>
    </div>
    ';
} elseif ($mod == 'api_set') {
    echo '<div class="block">
    <div class="block-title">
        <h3 class="panel-title">api对接设置</h3>
    </div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="action" value="api对接"/>
        <div class="col-sm-12 col-md-10 col-lg-10" style="float:none;margin:auto">
            <div class="form-group">
              <label class="col-sm-2 control-label">API对接密钥</label>
              <div class="col-sm-10"><input type="text" name="apikey" value="';
    echo $conf['zz_apikey'];
    echo '" class="form-control" placeholder="用于下单软件，随便填写即可"/>
                <pre style="color:red">请不要将此密钥轻易给不信任的人，否则被恶意使用出现任何损失自行负责哦</pre>
                </div>
            </div><br/>
            <div class="form-group">
              <label class="col-sm-2 control-label">Api查订单详情验证skey</label>
              <div class="col-sm-10"><select class="form-control" name="api_check_skey" default="';
    echo $conf['api_check_skey'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
                <small>未开启时可能会被恶意使用订单ID批量查询订单详情，导致假冒客服欺骗贵站点客户</small>
            </div>
            </div><br/>
            <div class="form-group">
              <div class="col-sm-12"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
            </div>
    </form>
      </div>
    </div>
    ';
} elseif ($mod == 'choujiang') {
    echo '<div class="block">
    <div class="block-title">
    <h3 class="panel-title">抽奖功能设置</h3>
      <span style="margin-left:10px;display:inline-block">
         <a href="./choujiang.php" class="btn btn-primary btn-xs">抽奖商品列表设置</a>
      </span>
    </div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="action" value="抽奖功能"/>
        <div class="col-sm-12 col-md-10 col-lg-10" style="float:none;margin:auto">
        <div class="form-group">
        <div class="input-group">
        <div class="input-group-addon">是否开启抽奖</div>
        <select name="gift_open" class="form-control" default="';
    echo $conf['zz_gift_open'];
    echo '">
        <option value="0">关闭</option>
        <option value="1">开启</option>
        </select>
        </div>
        </div>
        <div class="form-group">
        <div class="input-group">
        <div class="input-group-addon">每日每人抽奖次数</div>
        <input class="form-control" type="number" name="cjcishu" value="';
    echo $conf['zz_cjcishu'];
    echo '">
        </div>
        </div>
        <div class="form-group">
        <div class="input-group">
        <div class="input-group-addon">抽奖上限提示信息</div>
        <input class="form-control" type="text" name="cjmsg" value="';
    echo $conf['zz_cjmsg'];
    echo '">
        </div>
        </div>
        <div class="form-group">
        <div class="input-group">
        <div class="input-group-addon">抽奖付费金额</div>
        <input class="form-control" type="text" name="cjmoney" value="';
    echo $conf['zz_cjmoney'];
    echo '" placeholder="填0则不需要付费">
        </div>
        </div>
        <div class="form-group">
        <div class="input-group">
        <div class="input-group-addon">是否显示中奖记录</div>
        <select name="gift_log" class="form-control" default="';
    echo $conf['zz_gift_open'];
    echo '">
        <option value="0">关闭</option>
        <option value="1">开启</option>
        </select>
        </div>
        </div>
        </div>
        <div class="form-group">
          <div class="col-sm-12"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>
    </form>
      </div>
    </div>
    ';
} elseif ($mod == 'appset') {
    echo '<div class="block">
    <div class="block-title">
    <h3 class="panel-title">APP在线生成系统设置</h3>
    </div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="action" value="APP在线生成"/>
        <div class="col-sm-12 col-md-10 col-lg-10" style="float:none;margin:auto">
            <div class="form-group">
                <div class="input-group">
                <div class="input-group-addon">开启APP在线生成</div>
                <select name="app_open" class="form-control" default="';
    echo $conf['app_open'];
    echo '">
                <option value="0">关闭</option>
                <option value="1">开启</option>
                </select>
                </div>
            </div>
            <div class="form-group">
                <div class="input-group">
                <div class="input-group-addon">APPKEY密钥</div>
                <input class="form-control" type="text" name="app_key" value="';
    echo $conf['app_key'];
    echo '">
                </div>
                <small>先注册打包平台（<a href="http://app.izuoquan.com/" target="_blank">http://app.izuoquan.com/</a>）账号，并保证余额充足！<a id="queryRmb" class="btn btn-primary btn-xs">查询余额和包月状态</a></small>
            </div>
            <div class="form-group">
                <div class="input-group">
                <div class="input-group-addon">专业分站花费金额</div>
                <input class="form-control" type="text" name="app_price" value="';
    echo $conf['app_price'];
    echo '" placeholder="填0则不需要付费">
                </div>
            </div>
            <div class="form-group">
                <div class="input-group">
                <div class="input-group-addon">旗舰分站花费金额</div>
                <input class="form-control" type="text" name="app_price2" value="';
    echo $conf['app_price2'];
    echo '" placeholder="填0则不需要付费">
                </div>
            </div>
            <div class="form-group hide">
                <div class="input-group">
                <div class="input-group-addon">APP生成类型</div>
                <select name="app_create_type" class="form-control" default="';
    echo $conf['app_create_type'];
    echo '">
                <option value="0">普通网页封装版（默认）</option>
                <option value="3">网页封装按钮版</option>
                </select>
                </div>
            </div>
            <div class="form-group">
                <div class="input-group">
                <div class="input-group-addon">自定义图标和背景</div>
                <select name="app_diy" class="form-control" default="';
    echo $conf['app_diy'];
    echo '">
                <option value="0">关闭</option>
                <option value="1">开启</option>
                </select>
                </div>
            </div>
            <div class="form-group">
                <div class="input-group">
                <div class="input-group-addon">APP客户端公告</div>
                <textarea name="app_client_alert" rows="4" class="form-control">';
    echo $conf['app_client_alert'];
    echo '</textarea>
                </div>
            </div>
            <div class="form-group">
                <div class="input-group">
                <div class="input-group-addon">APP保存到本地</div>
                <select name="app_save_local" class="form-control" default="';
    echo $conf['app_save_local'];
    echo '">
                <option value="0">关闭</option>
                <option value="1">开启</option>
                </select>
                </div>
            </div>
        </div>
        <div class="form-group">
          <div class="col-sm-12"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>
    </form>
      </div>
    </div>
    <script type="text/javascript">
    $(document).on("click", "#queryRmb",function(){
        var ii = layer.load(0);
        $.ajax({
            type: "GET",
            url: "ajax.php?act=app_userinfo",
            dataType: "json",
            success: function(data) {
                layer.close(ii);
                var area = [$(window).width() > 640 ? \'480px\' : \'95%\', \'auto\'];
                if (data.code == 0) {
                    var item = data.data;
                    layer.open({
                        type: 1,
                        title: "app生成系统账户信息查询",
                        area: area,
                        skin: "layui-layer-rim",
                        btn:["我知道了"],
                        content: \'<table class="table"><tr><td class="warning">账户余额</td><td>\' +item.money + \'</td></tr><tr><td class="warning">手机号</td><td>\' +item.phone + \'</td></tr><tr><td class="warning">生成总计</td><td>\' +item.total + \'</td></tr><tr><td class="warning">成功次数</td><td>\' +item.task_succeeded + \'</td></tr><tr><td class="warning">失败次数</td><td>\' +item.task_failed + \'</td></tr><tr><td class="warning">蓝奏Cookie状态</td><td><p style="max-width:100px;word-wrap:break-word; word-break:normal;">\' +item.lanzou_cookie + \'</p></td></tr><tr><td class="warning">包月状态</td><td>\' +item.vip_status + \'</td></tr></table>\'
                    });
                } else {
                    layer.alert(data.msg);
                }
            },
            error: function(data) {
                layer.close(ii);
                layer.msg("服务器错误，请稍后再试或联系平台客服");
                return false;
            }
        });
    });
    </script>
    ';
} elseif ($mod == 'recharge_set') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">代理充值设置</h3></div>
    <div class="">
      <form action="./set.php?mod=recharge_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <div class="form-group">
              <label class="col-sm-2 control-label">充值返利开关</label>
              <div class="col-sm-10">
              <select class="form-control" id="fz_fanli_open" name="fz_fanli_open" default="';
    echo $conf['zz_fz_fanli_open'];
    echo '"><option value="0" >关闭返利</option><option value="1">开启返利</option></select>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">充值返利规则</label>
              <div class="col-sm-10">
              <input type="text" id="fz_fanli_list" name="fz_fanli_list" value="';
    echo $conf['zz_fz_fanli_list'] ? $conf['zz_fz_fanli_list'] : '10|2,20|3,50|4,100|5';
    echo '" class="form-control"/>
              <pre>例如满10元返2%等可以这样填：10|2,20|3,50|4,100|5  多个用英文逗号,隔开</pre>
              </div>
        </div>
         <div class="form-group">
              <label class="col-sm-2 control-label">最低充值金额</label>
              <div class="col-sm-10">
              <input type="text"  name="fenzhan_recharge_min" value="';
    echo round($conf['fenzhan_recharge_min'], 2);
    echo '" class="form-control"/>
              <pre>分站/用户充值最低金额，单位 元</pre>
              </div>
        </div>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>
    </form>
      </div>
    </div>
    ';
} elseif ($mod == 'user') {
    if ($is_account) {
        sysmsg('管理员您好！如需修改密码请联系主站长<br/><a href="./index.php">>>回到后台首页</a>');
    }
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">管理员资料设置</h3></div>
    <div class="">
      <form id="adminForm" role="form">
      <div class="form-horizontal" role="form">
        <input type="hidden" id="do" value="submit"/>
        <div class="form-group">
          <label class="col-sm-2 control-label">用户名</label>
          <div class="col-sm-10"><input type="text" id="adm_user" name="adm_user" value="';
    echo $conf['zz_adm_user'];
    echo '" class="form-control" required/>
             <pre style="color:red">注意：请不要设置成特别简单易猜的，诸如您的QQ号、微信、联系方式等，否则被入侵后果自负</pre>
            </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">登录密码</label>
          <div class="col-sm-10">
          <input type="text" id="adm_pwd" name="adm_pwd" value="" class="form-control" placeholder="不修改请留空"/>
          <pre>不修改请留空</pre>
          </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">开启邮箱登录安全验证</label>
          <div class="col-sm-10"><select class="form-control" name="adm_login_checkemail" default="';
    echo $conf['zz_adm_login_checkemail'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
           <pre>开启后系统管理员(非员工账户)每次登陆后台都会验证邮箱, 未绑定有效邮箱将不会验证</pre>
        </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">开启敏感操作安全验证</label>
          <div class="col-sm-10"><select class="form-control" name="adm_safe_check_email" default="';
    echo $conf['zz_adm_safe_check_email'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
           <pre>开启后系统管理员(非员工账户)每次操作提现、分站/用户加款、供货商加款扣款等都会验证邮箱, 未绑定有效邮箱将不会验证</pre>
        </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">登录过期时间</label>
          <div class="col-sm-10">
            <div class="input-group">
            <input type="text" id="adm_login_expire" name="adm_login_expire" value="';
    echo $conf['zz_adm_login_expire'] > 0 ? $conf['zz_adm_login_expire'] : '72';
    echo '" class="form-control" required/><span class="input-group-addon">小时</span>
            </div>
          </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">绑定手机号</label>
          <div class="col-sm-10">
           <div class="input-group">
             <input type="text" name="adm_tel" id="adm_tel" onblur="change()" value="';
    echo '';
    echo '" class="form-control" placeholder="不修改手机号请留空"/>
             <span id="sendCode" onclick="sendCode(\'sms\')" class="input-group-addon btn btn-primary btn-sm">发送验证码</span>
           </div>
          </div>
        </div><br/>
        <div class="form-group" id="code_display" style="display:none">
          <label class="col-sm-2 control-label">填写短信验证码</label>
          <div class="col-sm-10">
             <input type="text" id="code" name="code" value="" class="form-control" placeholder="填写验证码"/>
          </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">绑定邮箱</label>
             <div class="col-sm-10">
            <div class="input-group">
                <input type="text" name="adm_email" id="adm_email" onblur="change()" value="" class="form-control" placeholder="不修改邮箱请留空"/>
                <span id="sendCode" onclick="sendCode(\'ems\')" class="input-group-addon btn btn-primary btn-sm">发送验证码</span>
            </div>
            <pre>邮箱验证可提高安全，如果不需要可在关闭对应操作: <a href="./set.php?mod=ems">点我去设置邮箱开关</a></pre>
          </div>
        </div><br/>
        <input type="hidden" id="event" name="event" value="safecheck"/>
        <div class="form-group" id="code2_display" style="display:none">
          <label class="col-sm-2 control-label">填写邮箱验证码</label>
          <div class="col-sm-10"><input type="text" id="code2" name="code2" value="" class="form-control" placeholder="填写验证码"/></div>
        </div><br/>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10">
            <a id="submit" class="btn btn-primary form-control">修改</a><br/>
         </div>
        </div>
        </form>
      </div>
    </div>
    </div>
    <script type="text/javascript">
    var changeTel=false;
    function change(){
        var adm_tel = $("#adm_tel").val();
        $.ajax({
            type : "POST",
            url : "?mod=checkTel",
            dataType : "json",
            data : "adm_tel="+adm_tel,
            success : function(data) {
              if(data.code == 0){
                  changeTel=true;
                  $("#code_display").show();
              }
              else{
                  changeTel=false;
              }
            }
        });
    }

    function submit(){
        var adm_pwd = $("#adm_pwd").val();
        var adm_tel = $("#adm_tel").val();
        var code = $("#code").val();
        if(adm_pwd!="" && "" == code && adm_tel!=""){
          return layer.alert("当前验证码不能为空！");
        }
        else if(changeTel==true && "" == code){
          return layer.alert("当前验证码不能为空！");
        }

        var ii = layer.load(2, {shade:[0.1,"#fff"]});
        $.ajax({
            type : "POST",
            url : "?mod=user_n",
            data : $("#adminForm").serialize(),
            dataType : "json",
            success : function(res) {
                layer.close(ii);
                if(res.code == 0){
                    layer.msg(res.msg);
                }else if(res.code == 406 || res.code == 407){

                    if(res.data.event){
                        $("#event").val(res.data.event);
                    }
                    layer.alert(res.msg);
                }else{
                    layer.alert(res.msg);
                }
            },
            error:function(){
                layer.close(ii);
                layer.alert("服务器请求超时，请稍后重试！");
            }
        });
    }
    function sendCode(type){
        // var adm_tel = $("#adm_tel").val();
        // var event = $("#event").val();
        // if(type=="sms" && "" == adm_tel){
        //   layer.msg("手机号不能为空！");
        //   return;
        // }

        var adm_email_old = "' . $conf['adm_email'] . '";
        var adm_email = $("#adm_email").val();
        var event = $("#event").val();

        if(type=="ems" && !adm_email_old && "" == adm_email){
            layer.msg("请先填写邮箱");
            return;
        }

        if(adm_email_old){
           adm_email = adm_email_old;
        }

        if(!event){
           event = "safecheck";
        }

        if(type == "sms"){
            $("#code_display").show();
        }else{
            $("#code2_display").show();
        }

        var ii = layer.load(2, {shade:[0.1,"#fff"]});
        $.ajax({
          type : "POST",
          url : "ajax.public.php?act=" +  (type == "sms" ? "sms_send":"ems_send"),
          data : type == "sms"  ? "event=" + event + "" : "event=" + event + "&email=" + adm_email,
          dataType : "json",
          success : function(data) {
                layer.close(ii);
                if(data.code == 0){
                    $("#sendCode").html("已发送");
                    setTimeout(function(){
                        $("#sendCode").html("发送验证码");
                    },  1500);
                    layer.msg(data.msg);
                }else{

                    layer.alert(data.msg);
                }
          },
          error:function(){
            layer.close(ii);
            layer.alert("服务器请求超时，请稍后重试！");
          }
        });
    }
    var items = $("select[default]");
    for (i = 0; i < items.length; i++) {
        $(items[i]).val($(items[i]).attr("default")||0);
    }
    $("select[name=faka_input]").change();
    $(document).on("click", "#submit", function(event) {
        submit();
    });
    </script>
    ';
} elseif ($mod == 'sms') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">短信验证配置</h3></div>
    <form action="./set.php?mod=cloud_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="type" value="sms"/>
        <div class="form-group">
          <label class="col-sm-2 control-label">是否开启手机短信验证</label>
          <div class="col-sm-10"><select class="form-control" name="sms_open" default="';
    echo $conf['sms_open'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
          <pre>开启后下方的验证选项才会起作用</pre>
        </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">24小时验证码次数限制</label>
          <div class="col-sm-10">
              <input type="text" class="form-control" name="cloud_num" value="';
    echo $conf['zz_cloud_num'] > 0 ? $conf['zz_cloud_num'] : '3';
    echo '">
          </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">数字验证码长度</label>
          <div class="col-sm-10">
              <input type="text" class="form-control" name="cloud_length" value="';
    echo $conf['zz_cloud_length'] >= 6 ? $conf['zz_cloud_length'] : '6';
    echo '">
          </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">验证码时间限制</label>
          <div class="col-sm-10">
          <select class="form-control" name="cloud_max" default="';
    echo $conf['zz_cloud_max'] > 0 ? $conf['zz_cloud_max'] : '2';
    echo '">
             <option value="0">无限制</option>
             <option value="1">1分钟1次</option>
             <option value="2">5分钟1次（默认）</option>
             <option value="3">30分钟1次</option>
             <option value="4">2小时1次</option>
             <option value="5">6小时1次</option>
             <option value="6">12小时1次</option>
        </select>
        </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">验证场景开启勾选</label>
          <div class="col-sm-10">
             <div class="checkbox">
                <label class="checkbox-inline">
                <input type="checkbox" value="' . $conf['sms_check_change_mobile'] . '" name="sms_check_change_mobile" ' . ($conf['sms_check_change_mobile'] == 1 ? 'checked' : null) . '>修改手机
                </label>
                <label class="checkbox-inline">
                <input type="checkbox" value="' . $conf['sms_check_change_info'] . '" name="sms_check_change_info" ' . ($conf['sms_check_change_info'] == 1 ? 'checked' : null) . '>修改资料
                </label>
                <label class="checkbox-inline">
                <input type="checkbox"  value="' . $conf['sms_check_change_pwd'] . '" name="sms_check_change_pwd" ' . ($conf['sms_check_change_pwd'] == 1 ? 'checked' : null) . '>修改密码
                </label>
                <label class="checkbox-inline">
                <input type="checkbox"  value="' . $conf['sms_check_findpwd'] . '" name="sms_check_findpwd" ' . ($conf['sms_check_findpwd'] == 1 ? 'checked' : null) . '>找回密码
                </label>
                <label class="checkbox-inline">
                <input type="checkbox"  value="' . $conf['sms_check_login'] . '" name="sms_check_login" ' . ($conf['sms_check_login'] == 1 ? 'checked' : null) . '>登录账号
                </label>
                <label class="checkbox-inline">
                <input type="checkbox"  value="' . $conf['sms_check_register'] . '" name="sms_check_register" ' . ($conf['sms_check_register'] == 1 ? 'checked' : null) . '>注册账号
                </label>
              </div>
               <pre>在未勾选的部分场景下，不需要通过短信验证就能正常执行对应操作！短信验证和邮件验证都开启的情况下，优先验证手机</pre>
          </div>
        </div><br/>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
         </div>
        </div>
      </form>
    </div>
    <script>
    var items = $("select[default]");
    for (i = 0; i < items.length; i++) {
        $(items[i]).val($(items[i]).attr("default")||0);
    }

    $(".checkbox").find("input").click(function(){
         if(this.checked==true){
            $(this).val("1");
         }
         else{
            $(this).val("2");
         }
    });

    $("select[name=faka_input]").change();

    function checkOnChange(el){
        if (el.checked==false) {
            $(el).val("off");
        }else{
            $(el).val("1")
        }
    }

    var checkboxs = document.querySelectorAll("[name^=ems_check]");
    for (let index = 0; index < checkboxs.length; index++) {
        $(checkboxs[index]).on("click", function (el) {
            checkOnChange(el);
        });
    }

    var checkboxs = document.querySelectorAll("[name^=sms_check]");
    for (let index = 0; index < checkboxs.length; index++) {
        $(checkboxs[index]).on("click", function (el) {
            checkOnChange(el);
        });
    }
    </script>
    ';
} elseif ($mod == 'ems') {

    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">邮件发信设置</h3></div>
    <form action="./set.php?mod=cloud_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="type" value="ems"/>
        <div class="form-group">
          <label class="col-sm-2 control-label">验证场景开启勾选</label>
          <div class="col-sm-10">
             <div class="checkbox">
                <label class="checkbox-inline">
                <input type="checkbox" value="' . intval($conf['ems_check_change_email']) . '" name="ems_check_change_email" ' . ($conf['ems_check_change_email'] == 1 ? 'checked' : null) . '>修改邮箱
                </label>
                <label class="checkbox-inline">
                <input type="checkbox" value="' . $conf['ems_check_change_info'] . '" name="ems_check_change_info" ' . ($conf['ems_check_change_info'] == 1 ? 'checked' : null) . '>修改资料
                </label>
                <label class="checkbox-inline">
                <input type="checkbox"  value="' . $conf['ems_check_change_pwd'] . '" name="ems_check_change_pwd" ' . ($conf['ems_check_change_pwd'] == 1 ? 'checked' : null) . '>修改密码
                </label>
                <label class="checkbox-inline">
                <input type="checkbox"  value="' . $conf['ems_check_findpwd'] . '" name="ems_check_findpwd" ' . ($conf['ems_check_findpwd'] == 1 ? 'checked' : null) . '>找回密码
                </label>
                <label class="checkbox-inline">
                <input type="checkbox"  value="' . $conf['ems_check_login'] . '" name="ems_check_login" ' . ($conf['ems_check_login'] == 1 ? 'checked' : null) . '>登录账号
                </label>
                <label class="checkbox-inline">
                <input type="checkbox"  value="' . $conf['ems_check_register'] . '" name="ems_check_register" ' . ($conf['ems_check_register'] == 1 ? 'checked' : null) . '>注册账号
                </label>
              </div>
              <br/>
              <pre>在未勾选的场景下，不需要通过邮件验证就能正常执行对应操作！<b>部分场景下</b>, 短信验证和邮件验证都开启的情况下，优先验证手机<br/><span style="color:red;">请注意, 系统会自动根据开关设置在需要的场景下自动验证, 例如注册账号的对于管理员就用不到</span></pre>
          </div>
        </div><br/>
        <div class="form-group">
            <label class="col-sm-2 control-label">工单邮件提醒</label>
            <div class="col-sm-10"><select class="form-control" name="work_notice_email" default="';
    echo $conf['work_notice_email'] > 0 ? '1' : '0';
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
            <pre>开启后新增工单和工单回复都会提醒</pre>
            </div>
        </div><br/>
        <div class="form-group">
            <label class="col-sm-2 control-label">系统邮件账户</label>
            <div class="col-sm-10">
             <input type="text" class="form-control" name="mail_recv" value="';
    echo $conf['mail_recv'];
    echo '">
            <pre>用于接收邮箱提醒, 在部分场景下替代管理员绑定的邮箱</pre>
            </div>
        </div><br/>

        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
         </div>
        </div>
      </form>
    </div>
    <script>
    var items = $("select[default]");
    for (i = 0; i < items.length; i++) {
        $(items[i]).val($(items[i]).attr("default")||0);
    }

    $(".checkbox").find("input").click(function(){
         if(this.checked==true){
            $(this).val("1");
         }
         else{
            $(this).val("2");
         }
    });

    $("select[name=faka_input]").change();
    function checkOnChange(el){
        if (el.checked==false) {
            $(el).val("off");
        }else{
            $(el).val("1")
        }
    }

    var checkboxs = document.querySelectorAll("[name^=ems_check]");
    for (let index = 0; index < checkboxs.length; index++) {
        $(checkboxs[index]).on("click", function (el) {
            checkOnChange(el);
        });
    }

    var checkboxs = document.querySelectorAll("[name^=sms_check]");
    for (let index = 0; index < checkboxs.length; index++) {
        $(checkboxs[index]).on("click", function (el) {
            checkOnChange(el);
        });
    }
    </script>
    ';
} elseif ($mod == 'info') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">网站基本信息</h3>
            <a href="./set.php?mod=other" class="btn btn-primary btn-xs">模糊搜索设置</a>
            <a href="./set.php?mod=search" class="btn btn-info btn-xs">附加功能设置</a>
            </div>
<div class="">
  <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
    <input type="hidden" name="do" value="submit"/>
    <input type="hidden" name="action" value="网站基本信息"/>
    <div class="form-group">
      <label class="col-sm-2 control-label">主站运行状态</label>
      <div class="col-sm-10"><select class="form-control" name="index_run" default="';
    echo $conf['zz_index_run'];
    echo '"><option value="0">暂停运行</option><option value="1">正常运行</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">网站名称</label>
      <div class="col-sm-10"><input type="text" name="sitename" value="';
    echo $conf['zz_sitename'];
    echo '" class="form-control" required/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">SEO标题后缀</label>
      <div class="col-sm-10"><input type="text" name="title" value="';
    echo $conf['zz_title'];
    echo '" class="form-control"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">SEO关键字</label>
      <div class="col-sm-10"><input type="text" name="keywords" value="';
    echo $conf['zz_keywords'];
    echo '" class="form-control"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">后台配色</label>
      <div class="col-sm-10"><select class="form-control" name="ui_user" default="';
    echo $conf['ui_user'] > 0 ? $conf['ui_user'] : $conf['ui_user'];
    echo '"><option value="0">蓝白</option><option value="1">灰黑</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">SEO网站描述</label>
      <div class="col-sm-10"><textarea class="form-control" name="description" rows="3">';
    echo $conf['zz_description'];
    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">主站站长QQ</label>
      <div class="col-sm-10"><input type="text" name="zzqq" value="';
    echo $conf['zz_zzqq'];
    echo '" class="form-control"/>
    <pre>填写主站长QQ，用于部分场景下展示</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">网站首页全局脚本</label>
      <div class="col-sm-10">
      <textarea class="form-control" name="index_html" rows="6">';
    echo htmlspecialchars($conf['zz_index_html']);
    echo '</textarea>
        <pre>支持全局js脚本和客服脚本代码，如果是其他html代码可能会乱排版</pre>
      </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">网站首页统计代码</label>
      <div class="col-sm-10">
      <textarea class="form-control" name="index_html_bottom" rows="3">';
    echo htmlspecialchars($conf['zz_index_html_bottom']);
    echo '</textarea>
        <pre>只能放备案号和统计代码（其他代码可能会乱排版），会在所有模板底部展示</pre>
      </div>
    </div><br/>
    <div class="form-group">
     <label class="col-sm-2 control-label">商品动态日志显示模式</label>
     <div class="col-sm-10"><select class="form-control" name="tool_log_type" default="';
    echo $conf['tool_log_type'];
    echo '"><option value="0">手动模式(默认)</option><option value="1">自动模式+手动模式</option><option value="2">手动模式(仅显示最新5条)</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">是否统一为主站客服</label>
      <div class="col-sm-10"><select class="form-control" name="index_unify" default="';
    echo $conf['zz_index_unify'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
     <pre>开启后，全站统一为主站的客服，分站的客服联系也将显示主站设置的客服！（其他地方）</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">客服ＱＱ</label>
      <div class="col-sm-10"><input type="text" name="kfqq" value="';
    echo $conf['zz_kfqq'];
    echo '" class="form-control"/>
     <pre style="color:red">如果留空，主站页面的所有头像将会为空！且部分页面可能会无法正常工作</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">客服一昵称</label>
      <div class="col-sm-10"><input type="text" name="kfname" value="';
    echo $conf['zz_kfname'];
    echo '" class="form-control"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">客服ＱＱ二</label>
      <div class="col-sm-10"><input type="text" name="kfqq2" value="';
    echo $conf['zz_kfqq2'];
    echo '" class="form-control"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">客服二昵称</label>
      <div class="col-sm-10"><input type="text" name="kfname2" value="';
    echo $conf['zz_kfname2'];
    echo '" class="form-control"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">客服微信</label>
      <div class="col-sm-10"><input type="text" name="kfwx" value="';
    echo $conf['zz_kfwx'];
    echo '" class="form-control"/>
        <pre>推荐填写客服链接等</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">客服在线时间</label>
      <div class="col-sm-10"><input type="text" name="on_line" value="';
    echo $conf['zz_on_line'] != "" ? $conf['zz_on_line'] : '早11:30~晚22:30';
    echo '" class="form-control"/>
       <pre>客服在线时间段，例如：早11:30~晚22:30</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">网站创建时间</label>
      <div class="col-sm-10"><input type="date" name="build" value="';
    echo $conf['zz_build'];
    echo '" class="form-control"/></div>';

    $select = '<option value="https://cn.vip.achaci.cn/">国内主用（推荐）</option><option value="http://cn.vip.chenmds.cn/">国内备用1</option><option value="http://cn.vip.5kzx.cn/">国内备用2</option>';
    $list   = null;
    if (function_exists('getQqApiList')) {
        $list = getQqApiList();
    }
    if (is_array($list)) {
        $select = '';
        foreach ($list as $key => $v) {
            $select .= '<option value="' . $v['url'] . '">' . $v['label'] . '</option>';
        }
    }
    $select .= '<option value="diy">自定义接口</option>';
    echo '
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">空间说说接口选择</label>
      <div class="col-sm-10"><select class="form-control" name="site_qzone_api" default="';
    if ($conf['site_qzone_api'] != 'diy' && !preg_match('/^(http|https):\/\/[\w\-\.]+\.[\w]{2,8}\/$/', $conf['site_qzone_api'])) {
        $conf['site_qzone_api'] = 'https://cn.vip.achaci.cn/';
    }
    echo $conf['site_qzone_api'];
    echo '">' . $select . '</select>
       <small>说明：由于TX更新限制，在线有效期半小时~2小时左右且不能访问过多，导致现在接口都极不稳定，请自行多备小号扫码<br/>自己上传Ck链接：<a href="' . $weburl . 'qzone.php" target="_blank">' . $weburl . 'qzone.php</a></small>
    </div>
    </div><br/>
    <div class="form-group" id="qzone_api0" style="display:none">
      <label class="col-sm-2 control-label">自定义空间说说接口地址</label>
      <div class="col-sm-10"><input type="text" name="site_qzone_api_url" value="';
    echo $conf['site_qzone_api_url'];
    echo '" class="form-control"/>
            <small>说说接口填写格式参考：http(s)://xx.xxx.cn/ssapi.php?uin={{uin}}&page={{page}}<br/>{{uin}}： 要查询的QQ，不能删只能改所在位置<br/>{{page}}： 要查询的页码数，不能删只能改所在位置<br/>注意：说说接口是按照GET方式请求调用的，参数必须支持GET方式才可以正常使用</small>
        </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">系统验证平台</label>
      <div class="col-sm-10"><select class="form-control" name="captcha_open" default="';
    echo intval($conf['zz_captcha_open']);
    echo '"><option value="0">关闭</option><option value="1">极致验证</option><option value="2">中英验证码</option></select>
        <small style="color:red">开启验证可用于避免免费商品被撸，或被恶意批量注册用户！<a href="https://www.geetest.com/Register" target="_blank">去极致验证平台</a></small>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">验证码输出类型</label>
      <div class="col-sm-10"><select class="form-control" name="captcha_type" default="';
    echo $conf['zz_captcha_type'] > 0 ? '1' : '0';
    echo '"><option value="0">字母数字</option><option value="1">中文字符</option><option value="2">混合随机</option></select>
            <small style="color:red">该设置针对中英验证码有效，启用混合随机类型更复杂，中文+字母数字随机可有效避免被刷</small>
        </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">极致验证ID</label>
      <div class="col-sm-10"><input type="text" name="captcha_id" value="';
    echo !empty($conf['zz_captcha_id']) ? $conf['zz_captcha_id'] : '0ea798b5bed2f1e44363199aadfc2773';
    echo '" class="form-control"/>
            <small>不填使用系统默认的极致验证ID</small>
        </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">极致验证key</label>
          <div class="col-sm-10"><input type="text" name="captcha_key" value="';
    echo !empty($conf['zz_captcha_key']) ? $conf['zz_captcha_key'] : '8df6fd729fd29d69f41c36bbb14483ca';
    echo '" class="form-control"/>
            <small>不填使用系统默认的极致验证key</small>
        </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">验证场景开启勾选</label>
          <div class="col-sm-10">
             <div class="checkbox">
                <label>
                <input type="hidden" value="0" name="captcha_open_login"/>
                <input type="checkbox" ' . ($conf['zz_captcha_open_login'] == 1 ? 'checked' : null) . ' name="captcha_open_login">用户登录
               </label>
               <label>
                <input type="hidden" value="0" name="captcha_open_reg"/>
                <input type="checkbox" ' . ($conf['zz_captcha_open_reg'] == 1 ? 'checked' : null) . ' name="captcha_open_reg">用户注册
               </label>
               <label>
                <input type="hidden" value="0" name="captcha_open_regsite"/>
                <input type="checkbox" ' . ($conf['zz_captcha_open_regsite'] == 1 ? 'checked' : null) . ' name="captcha_open_regsite">搭建分站
               </label>
               <label>
                <input type="hidden" value="0" name="captcha_open_freebuy"/>
                <input type="checkbox" ' . ($conf['zz_captcha_open_freebuy'] == 1 ? 'checked' : null) . ' name="captcha_open_freebuy">免费商品下单
               </label>
            </div>
              <small style="color:red">在未勾选开启的场景下，不会使用极致验证来验证是否真人操作！</small>
          </div>
    </div><br/>
    <div class="form-group">
        <label class="col-sm-2 control-label">富文本编辑器选择</label>
        <div class="col-sm-10"><select class="form-control" name="editor_index" default="';
    echo $conf['zz_editor_index'] ? $conf['zz_editor_index'] : 0;
    echo '"><option value="0">关闭富文本编辑器</option><option value="1">wangEditor编辑器</option></select>
        <pre>如果您不会html代码，可以选择使用可视化编辑的富文本编辑器！反之关闭即可</pre>
        </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">列表单页显示数量</label>
      <div class="col-sm-10"><select class="form-control" name="index_pagesize" default="';
    echo $conf['zz_index_pagesize'] ? $conf['zz_index_pagesize'] : 30;
    echo '">
          <option value="15">每页15条</option>
          <option value="30">每页30条</option>
          <option value="50">每页50条</option>
          <option value="100">每页100条</option>
          <option value="200">每页200条</option>
          <option value="500">每页500条</option>

    </select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">订单搜索默认类型</label>
      <div class="col-sm-10"><select class="form-control" name="list_type" default="';
    echo $conf['zz_list_type'];
    echo '">
          <option value="0">自动匹配</option>
          <option value="1">订单编号</option>
          <option value="4">下单数据（任意数据）</option>
          <option value="2">商品名称（模糊）</option>
          <option value="3">商品编号</option>
          <option value="5">站点ZID</option>
    </select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">后台首页加载模式</label>
      <div class="col-sm-10"><select class="form-control" name="data_type" default="';
    echo $conf['zz_data_type'] ? $conf['zz_data_type'] : 1;
    echo '">
          <option value="1">精简数据加载</option>
          <option value="2">详细数据加载</option>
    </select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">手机QQ打开网站跳转其他浏览器</label>
      <div class="col-sm-10"><select class="form-control" name="qqjump" default="';
    echo $conf['zz_qqjump'];
    echo '"><option value="0">关闭</option><option value="1">默认跳转页</option><option value="2">加强防鸿页</option></select>
    </div>
    </div><br/>
    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
     </div>
    </div>
    高级功能：<a href="./clone.php">克隆站点</a>｜<a href="./set.php?mod=cleancache">清理设置缓存</a>｜<a href="./set.php?mod=cleanlog" onclick="return confirm(\'您确实要清空所有社区对接日志吗？\');">清空社区对接日志</a>｜<a href="./set.php?mod=cleanpay" onclick="return confirm(\'您确实要清空1天前的支付记录吗？\');">清空支付记录</a>｜<a href="./set.php?mod=defend">防CC模块设置</a>｜<a href="./set.php?mod=proxy">代理服务器设置</a>

  </form>
    <script type="text/javascript">
    $(document).on("change","select[name=site_qzone_api]",function(e){
        e.preventDefault();
        /* Act on the event */
        $("#qzone_api0").hide();
        var value =$(this).val();
        if(value=="diy"){
            $("#qzone_api0").show();
        }
    });
    //$("select[name=site_qzone_api]").change();
    </script>
</div>
</div>
';
} elseif ($mod == 'proxy') {
    $server_hash = md5($_SERVER['SERVER_SOFTWARE'] . $_SERVER['SERVER_ADDR']);
    if ($server_hash === $conf['server_hash'] && $conf['proxy'] == 1) {
        $is_proxy = $conf['proxy'];
    } else {
        $is_proxy = 0;
    }
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">代理服务器设置</h3></div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="server_hash" value="' . $server_hash . '"/>
        <input type="hidden" name="action" value="代理服务器"/>
        <div class="alert alert-info">
        此功能用于绕过部分拦截国外服务器的对接平台，如卡易信！<br>
        一般情况下是可以绕过的，如果对接平台更新或者有强制验证就会无效，此时必需要国内对接才能解决
        </div>
        ';
    echo '
         <div class="form-group">
              <label class="col-sm-2 control-label">代理服务器开关</label>
              <div class="col-sm-10">
              <select class="form-control" id="proxy" name="proxy" default="';
    echo $is_proxy;
    echo '"><option value="0" >关闭</option><option value="1">开启</option><option value="2">自定义代理</option></select>
                <pre>由于内置的代理服务器只有一个，如出现频繁请自定义配置代理服务器</pre>
             </div>
        </div>
       <div id="display_proxyBox">
       <div class="form-group">
              <label class="col-sm-2 control-label">代理服务器IP</label>
              <div class="col-sm-10">
              <input class="form-control" name="proxy_host" value="';
    echo $conf['proxy_host'];
    echo '"/>
            <pre>必须认真填写。如不是自己搭建的代理服务器，请在代理服务器提供商平台获取</pre>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">代理服务器端口</label>
              <div class="col-sm-10">
              <input class="form-control" name="proxy_port" value="';
    echo $conf['proxy_port'];
    echo '"/>
            <pre>必须认真填写。如：8545，如不是自己搭建的代理服务器，请在代理服务器提供商平台获取</pre>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">代理服务器账号</label>
              <div class="col-sm-10">
              <input class="form-control" name="proxy_user" value="';
    echo $conf['proxy_user'];
    echo '"/>
            <pre>如果代理服务器资料未设置用户验证（不清楚可以问提供商），可不填</pre>
            </div>
        </div
        <div class="form-group">
              <label class="col-sm-2 control-label">代理服务器密码</label>
              <div class="col-sm-10">
              <input class="form-control" name="proxy_pwd" value="';
    echo $conf['proxy_pwd'];
    echo '"/>
            <pre>如果代理服务器资料未设置用户验证（不清楚可以问提供商），可不填</pre>
            </div>
        </div>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>';
    echo '</form>
      </div>
    </div>
    <script type="text/javascript">
    $("#proxy").change(function(){
        if(this.value == 2){
            $("#display_proxyBox").show();
        }
        else{
            $("#display_proxyBox").hide();
        }
    });
    </script>
    ';
} elseif ($mod == 'payLimit') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">支付方式金额限制设置</h3></div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="action" value="支付方式金额限制"/>
        <div class="alert alert-info">此处的限制开关是订单全局设置，对所有总金额大于0且不满足设置要求的订单都有效！金额单位：元。</div>
        <div class="form-group">
              <label class="col-sm-2 control-label">QQ钱包限制开关</label>
              <div class="col-sm-10">
              <select class="form-control" id="proxy" name="qqpay_limit_open" default="';
    echo $conf['qqpay_limit_open'] > 0 ? '1' : '0';
    echo '"><option value="0" >关闭</option><option value="1">开启</option></select>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">QQ钱包最低限制金额</label>
              <div class="col-sm-10">
              <input class="form-control" name="qqpay_limit" value="';
    echo $conf['qqpay_limit'];
    echo '"/>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">支付宝限制开关</label>
              <div class="col-sm-10">
              <select class="form-control" id="proxy" name="alipay_limit_open" default="';
    echo $conf['alipay_limit_open'] > 0 ? '1' : '0';
    echo '"><option value="0" >关闭</option><option value="1">开启</option></select>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">支付宝最低限制金额</label>
              <div class="col-sm-10">
              <input class="form-control" name="alipay_limit" value="';
    echo $conf['alipay_limit'];
    echo '"/>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">微信限制开关</label>
              <div class="col-sm-10">
              <select class="form-control" id="proxy" name="wxpay_limit_open" default="';
    echo $conf['wxpay_limit_open'] > 0 ? '1' : '0';
    echo '"><option value="0" >关闭</option><option value="1">开启</option></select>
            </div>
        </div>
        <div class="form-group">
              <label class="col-sm-2 control-label">微信最低限制金额</label>
              <div class="col-sm-10">
              <input class="form-control" name="wxpay_limit" value="';
    echo $conf['wxpay_limit'];
    echo '"/>
            </div>
        </div>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
        </div>';
    echo '</form>
      </div>
    </div>
    ';
} elseif ($mod == 'order') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">网站订单相关设置</h3></div>
    <div class="">
      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
        <input type="hidden" name="do" value="submit"/>
        <input type="hidden" name="action" value="网站订单相关"/>
         <div class="form-group">
          <label class="col-sm-2 control-label">下单后邮件通知我</label>
          <div class="col-sm-10"><select class="form-control" name="email_push" default="';
    echo $conf['zz_email_push'];
    echo '">
            <option value="0">关闭</option>
            <option value="1">开启（只包含自营商品）</option>
            <option value="2">开启（所有商品）</option>
            <option value="3">开启（不包含对接商品）</option>
            <option value="4">开启（不包含对接和虚拟商品）</option>
            </select>
            <small>选择开启后，商品可单独是否选择开启通知！如果是选择关闭后，所有商品被下单后都不会再发送通知！<span style="color:red">注意：如果没发通知，请检查【邮箱配置】是否正常、商品里面是否开启【下单后发送邮件提醒】</span></small>
            </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">分站订单退款方式</label>
          <div class="col-sm-10"><select class="form-control" name="refund_type" default="';
    echo $conf['zz_refund_type'];
    echo '">
            <option value="1">当分站游客购买时主站手动联系游客并退款，同时扣除分站相应提成</option>
            <option value="2">统一退款到分站账户</option>
            </select>
            <small>由于分站订单有两种，一种分站站长自己下单的，另一种是分站的游客客户下单的，请根据需求设置</small>
            </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">分站提成扣除方式</label>
          <div class="col-sm-10"><select class="form-control" name="refund_point_type" default="';
    echo $conf['zz_refund_point_type'] > 0 ? '1' : '0';
    echo '">
            <option value="0">扣除提成（默认）</option>
            <option value="1">扣除余额</option>
            </select>
            <small>分站账户有提成余额和普通余额，其中只能提成余额可提现，请根据需求设置</small>
            </div>
        </div><br/>
        <div class="form-group">
          <label class="col-sm-2 control-label">退款给分站的到账方式</label>
          <div class="col-sm-10"><select class="form-control" name="refund_fenzhan_type" default="';
    echo $conf['zz_refund_fenzhan_type'] > 0 ? '1' : '0';
    echo '">
            <option value="0">到提成（默认，可提现）</option>
            <option value="1">到余额（不可提现）</option>
            </select>
            <small>注意，目前分站账户中只有提成余额可提现！</small>
            </div>
        </div><br/>
        <div class="form-group hide">
        <label class="col-sm-2 control-label">自动合并相同待处理订单</label>
        <div class="col-sm-10"><select class="form-control" name="multi_merge" default="';
    echo $conf['zz_multi_merge'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
         <small>开启后，订单是待处理且客户多次下相同订单情况下，自动合并相同订单，份数和付款金额会叠加！</small>
      </div>
      </div><br/>
      <div class="form-group">
        <label class="col-sm-2 control-label">下单数据不合格处理</label>
        <div class="col-sm-10"><select class="form-control" name="order_inputcheck" default="';
    echo $conf['order_inputcheck'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
         <small>开启后，用于处理一部分不符合下单要求的数据，例如下单时填写了包含中文或特殊符号的链接！</small>
      </div>
      </div><br/>
      <div class="form-group">
        <label class="col-sm-2 control-label">查单详情订单号显示方式</label>
        <div class="col-sm-10"><select class="form-control" name="order_id_type" default="';
    echo $conf['zz_order_id_type'];
    echo '"><option value="0">订单ID</option><option value="1">支付订单号（推荐）</option></select></div>
        </div><br/>
        <div class="form-group">
        <label class="col-sm-2 control-label">查单是否支持订单ID</label>
        <div class="col-sm-10"><select class="form-control" name="query_orderid" default="';
    echo $conf['zz_query_orderid'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
        </div><br/>
        <div class="form-group">
        <label class="col-sm-2 control-label">商品排序方式</label>
        <div class="col-sm-10"><select class="form-control" name="goods_sort_type" default="';
    echo $conf['zz_goods_sort_type'];
    echo '"><option value="1">正序(从小到大)</option><option value="0">倒序(从大到小)</option></select></div>
        </div><br/>
        <div class="form-group">
        <label class="col-sm-2 control-label">游客查单验证Cookie安全</label>
        <div class="col-sm-10"><select class="form-control" name="query_checkcookie" default="';
    echo $conf['zz_query_checkcookie'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
            <small>此功能开启后，可用于避免非下单用户直接通过QQ号等下单账号不登录就查询到订单！<span style="color:red">注意，开启后如果下单用户的Cookie缓存失效或更新了他也会查询不到订单，如果不需要可以关闭（Cookie是通过客户端网络IP生成的，游客只要是通过正常浏览器下单IP迟早是会变的）</span></small>
            </div>
        </div><br/>
        <div class="form-group">
        <label class="col-sm-2 control-label">订单隐私保护</label>
        <div class="col-sm-10"><select class="form-control" name="order_privacy_protection" default="';
    echo $conf['order_privacy_protection'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
            <small>开启后，主站无法通过代理网站客户下单时预留的信息（如客户姓名、电话、邮箱等）来查询和显示这些订单，保护代理商客户的隐私信息。<span style="color:red">注意：此功能主要用于保护代理商客户隐私，防止主站通过客户信息反查到代理渠道的订单详情</span></small>
            </div>
        </div><br/>
        <div class="form-group">
        <label class="col-sm-2 control-label">订单详情显示完成时间</label>
        <div class="col-sm-10"><select class="form-control" name="show_endtime" default="';
    echo $conf['zz_show_endtime'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
        </div><br/>
        <div class="form-group">
        <label class="col-sm-2 control-label">订单详情显示商品简介</label>
        <div class="col-sm-10"><select class="form-control" name="show_desc" default="';
    echo $conf['zz_show_desc'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
        </div><br/>
        <div class="form-group">
        <label class="col-sm-2 control-label">订单详情显示处理耗时</label>
        <div class="col-sm-10"><select class="form-control" name="show_usetime" default="';
    echo $conf['zz_show_usetime'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
        </div><br/>
        <div class="form-group">
        <label class="col-sm-2 control-label">订单详情显示申请工单售后</label>
        <div class="col-sm-10"><select class="form-control" name="show_complain" default="';
    echo $conf['zz_show_complain'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
      </div><br/>
      <div class="form-group">
        <label class="col-sm-2 control-label">订单详情申请售后限制</label>
        <div class="col-sm-10"><select class="form-control" name="complain_limit" default="';
    echo $conf['zz_complain_limit'];
    echo '"><option value="0">关闭限制</option><option value="7">7天内可申请</option><option value="15">15天内可申请</option><option value="31">1个月内可申请</option><option value="90">3个月内可申请</option><option value="182">6个月内可申请</option><option value="365">12个月内可申请</option></select></div>
      </div><br/>
      <div class="form-group">
      <label class="col-sm-2 control-label">首页显示数据缓存时间</label>
      <div class="col-sm-10"><input type="text" name="tongji_time" value="';
    echo $conf['zz_tongji_time'] >= 300 ? $conf['zz_tongji_time'] : '300';
    echo '" class="form-control"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">网站虚拟数据</label>
      <div class="col-sm-10"><select class="form-control" name="invented_open" default="';
    echo $conf['zz_invented_open'] > 0 ? '1' : '0';
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">虚拟总订单基数</label>
      <div class="col-sm-10"><input type="text" name="invented_orders" value="';
    echo $conf['zz_invented_orders'];
    echo '" class="form-control"/>
      <pre>此处设置虚拟总订单基础数量，实际显示数量=虚拟数据+实际数据</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">虚拟总交易额基数</label>
      <div class="col-sm-10"><input type="text" name="invented_moneys" value="';
    echo $conf['zz_invented_moneys'];
    echo '" class="form-control"/>
      <pre>此处设置虚拟总交易额基础数量，实际显示数量=虚拟数据+实际数据</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">虚拟今日订单基数</label>
      <div class="col-sm-10"><input type="text" name="invented_order" value="';
    echo $conf['zz_invented_order'];
    echo '" class="form-control"/>
      <pre>此处设置虚拟今日订单基础数量，实际显示数量=虚拟数据+实际数据</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">虚拟今日交易额基数</label>
      <div class="col-sm-10"><input type="text" name="invented_money" value="';
    echo $conf['zz_invented_money'];
    echo '" class="form-control"/>
      <pre>此处设置虚拟今日交易额基础数量，实际显示数量=虚拟数据+实际数据</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">虚拟分站数量基数</label>
      <div class="col-sm-10"><input type="text" name="invented_fenzhan" value="';
    echo $conf['zz_invented_fenzhan'];
    echo '" class="form-control"/>
      <pre>此处设置虚拟分站数量基础数量，实际显示数量=虚拟数据+实际数据</pre>
    </div>
    </div><br/>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
          </div>';
    echo '</form>
      </div>
    </div>
    ';

} elseif ($mod == 'site') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">网站核心设置</h3></div>
<div class="panel-body">
  <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
    <input type="hidden" name="do" value="submit"/>
    <input type="hidden" name="action" value="网站核心"/>
    <div class="form-group">
      <label class="col-sm-2 control-label">下单验证模块</label>
      <div class="col-sm-10"><select class="form-control" name="verify_open" default="';
    echo $conf['zz_verify_open'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">虚拟商品下单标题</label>
      <div class="col-sm-10"><select class="form-control" name="faka_input" onChange="fakaShow(this.value)" default="';
    echo $conf['zz_faka_input'];
    echo '"><option value="0">您的邮箱</option><option value="1">QQ邮箱</option><option value="2">手机号码</option><option value="3">您的ＱＱ</option><option value="-1">自定义标题</option><option value="4">(不填写内容)</option></select></div>
    </div><br/>
    <div class="form-group" id="faka_input_display" style="display:none">
      <label class="col-sm-2 control-label">自定义虚拟商品标题</label>
      <div class="col-sm-10"><input type="text" name="faka_input_index" value="';
    echo $conf['zz_faka_input_index'];
    echo '" class="form-control"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">提交工单后给站长发邮件</label>
      <div class="col-sm-10"><select class="form-control" name="workorder_mail" default="';
    echo $conf['zz_workorder_mail'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">首页显示数据统计信息</label>
      <div class="col-sm-10"><select class="form-control" name="hide_tongji" default="';
    echo $conf['zz_hide_tongji'];
    echo '"><option value="0">开启</option><option value="1">关闭</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">网站背景图</label>
      <div class="col-sm-10"><select class="form-control" name="ui_bing" onChange="setUiBox(this.value)" default="';
    echo $conf['zz_ui_bing'];
    echo '"><option value="0">自定义背景图片</option><option value="1">随机a壁纸</option><option value="2">Bing每日壁纸</option></select></div>
    </div><br/>
    <div class="form-group" id="ui_bing_display" style="display:none">
      <label class="col-sm-2 control-label">背景图片地址</label>
      <div class="col-sm-10"><input type="text" name="ui_bing_img" value="';
    echo $conf['zz_ui_bing_img'];
    echo '" class="form-control"/>
    <pre>此处填写自定义背景图片的地址</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">登录后才能下单</label>
          <div class="col-sm-10"><select class="form-control" name="index_regbuy" default="';
    echo $conf['zz_index_regbuy'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select>
        <pre>开启后游客访问网站会自动跳转登录页面</pre>
        </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">余额支付商品可见方式</label>
      <div class="col-sm-10"><select class="form-control" name="hide_rmbpay" default="';
    echo $conf['zz_hide_rmbpay'] != "" ? $conf['zz_hide_rmbpay'] : '0';
    echo '"><option value="0">不隐藏</option><option value="1">登录后可见</option></select>
          <pre style="color:red">注意！！选择“登录后可见”后，可选支付方式中开启“余额支付”的商品只能登录后才能看到</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">首页下单显示风格</label>
      <div class="col-sm-10"><select class="form-control" name="ui_shop" default="';
    echo $conf['zz_ui_shop'];
    echo '"><option value="0">经典模式</option><option value="1">分类图片宫格</option><option value="2">分类图片列表</option><option value="3">分类图片宫格2</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">首页商品显示风格</label>
      <div class="col-sm-10"><select class="form-control" name="ui_tool" default="';
    echo $conf['zz_ui_tool'] > 0 ? '1' : '0';
    echo '"><option value="0">经典下拉框</option><option value="1">新版全名称</option></select>
      <pre>为解决下拉框在苹果手机下商品过长显示不全的问题，新增了一个可以显示商品全名称，按需设置即可！</pre>
      </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">商品列表默认显示</label>
      <div class="col-sm-10"><select class="form-control" name="tool_show" default="';
    echo $conf['zz_tool_show'] != "" ? $conf['zz_tool_show'] : '1';
    echo '"><option value="0">关闭显示</option><option value="1">开启显示</option></select></div>
    </div><br/>
    <div class="form-group hide">
      <label class="col-sm-2 control-label">首页显示商品推荐(部分模板)</label>
          <div class="col-sm-10"><select class="form-control" name="index_hot" default="';
    echo $conf['zz_index_hot'];
    echo '"><option value="0">关闭显示</option><option value="1">开启显示</option></select>
        <pre>部分模板支持，如果出现页面错乱等问题请在此处关闭</pre>
        </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">是否开启卡密下单</label>
      <div class="col-sm-10"><select class="form-control" name="iskami" default="';
    echo $conf['zz_iskami'];
    echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">卡密购买地址</label>
      <div class="col-sm-10"><input type="text" name="kaurl" value="';
    echo $conf['zz_kaurl'];
    echo '" class="form-control"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">APP下载地址</label>
      <div class="col-sm-10"><input type="text" name="appurl" value="';
    echo $conf['zz_appurl'];
    echo '" class="form-control" placeholder="没有请留空"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">音乐直链</label>
      <div class="col-sm-10"><input type="text" name="music_url" value="';
    echo $conf['zz_music_url'];
    echo '" class="form-control" placeholder="部分模板可用，没有请留空"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">主站DG地址</label>
      <div class="col-sm-10"><input type="text" name="dgurl" value="';
    echo $conf['zz_dgurl'];
    echo '" class="form-control" placeholder="没有请留空"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">QQ群链接</label>
      <div class="col-sm-10"><input type="text" name="qqgroup_url" value="';
    echo $conf['zz_qqgroup_url'];
    echo '" class="form-control" placeholder="没有请留空"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">主站MZ地址</label>
      <div class="col-sm-10"><input type="text" name="mzurl" value="';
    echo $conf['zz_mzurl'];
    echo '" class="form-control" placeholder="没有请留空"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">主站租号地址</label>
      <div class="col-sm-10"><input type="text" name="zuhaourl" value="';
    echo $conf['zz_zuhaourl'];
    echo '" class="form-control" placeholder="没有请留空"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">支付宝红包码</label>
      <div class="col-sm-10"><input type="text" name="zfb_code" value="';
    echo $conf['zz_zfb_code'];
    echo '" class="form-control" placeholder="没有请留空"/></div>
    </div><br/>
     <div class="form-group">
      <label class="col-sm-2 control-label">支付宝红包图片链接</label>
      <div class="col-sm-10"><input type="text" name="zfb_codeurl" value="';
    echo $conf['zz_zfb_codeurl'];
    echo '" class="form-control" placeholder="没有请留空"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">全部商品下单黑名单账号</label>
      <div class="col-sm-10">
          <textarea class="form-control" name="blacklist" rows="3">';
    echo $conf['zz_blacklist'];
    echo '</textarea>
        <pre>多个用|隔开</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">全部商品黑名单提示内容</label>
      <div class="col-sm-10"><textarea class="form-control" name="blacklisttip" rows="3">';
    echo $conf['zz_blacklisttip'];
    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">Bing随机壁纸背景</label>
      <div class="col-sm-10"><select class="form-control" name="ui_bing" default="';
    echo $conf['zz_ui_bing'];
    echo '"><option value="0">关闭</option><option value="1">每天自动随机换</option><option value="2">每次访问自动随机换</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">拉圈圈赞API</label>
      <div class="col-sm-10"><input type="text" name="lqqapi" value="';
    echo $conf['zz_lqqapi'];
    echo '" class="form-control" placeholder="填写后将在首页显示免费拉圈圈，没有请留空"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">手机QQ打开网站跳转其他浏览器</label>
      <div class="col-sm-10"><select class="form-control" name="qqjump" default="';
    echo $conf['zz_qqjump'];
    echo '"><option value="0">关闭</option><option value="1">默认跳转页</option><option value="2">加强防鸿页</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">API对接密钥</label>
      <div class="col-sm-10"><input type="text" name="apikey" value="';
    echo $conf['zz_apikey'];
    echo '" class="form-control" placeholder="用于下单软件，随便填写即可"/>
        <pre style="color:red">请不要将此密钥轻易给不信任的人，否则被恶意使用出现任何损失自行负责哦</pre>
        </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">监控密钥</label>
      <div class="col-sm-10"><input type="text" name="cronkey" value="';
    echo $conf['zz_cronkey'];
    echo '" class="form-control" placeholder="用于易支付补单监控使用"/>
     <pre style="color:red">请不要将此密钥轻易给不信任的人，否则被恶意使用出现任何损失自行负责哦</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <div class="col-sm-12"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
     </div>
    </div>
    高级功能：<a href="./clone.php">克隆站点</a>｜<a href="./set.php?mod=cleancache">清理设置缓存</a>｜<a href="./set.php?mod=cleanlog" onclick="return confirm(\'您确实要清空所有社区对接日志吗？\');">清空社区对接日志</a>｜<a href="./set.php?mod=cleanpay" onclick="return confirm(\'您确实要清空1天前的支付记录吗？\');">清空支付记录</a>｜<a href="./set.php?mod=defend">防CC模块设置</a>｜<a href="./set.php?mod=proxy">代理服务器设置</a>
  </form>
</div>
</div>
<script>
function fakaShow(val){
    if(val == (-1)){
        $("#faka_input_display").show();
    }
    else{
        $("#faka_input_display").hide();
    }
}

function setUiBox(val){
    if(val == 0){
        $("#ui_bing_display").show();
    }
    else{
        $("#ui_bing_display").hide();
    }
}

$("select[name=faka_input]").change();
</script>
';
} elseif ($mod == 'qiandao') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">分站签到功能</h3></div>
<div class="panel-body">
  <form action="./set.php?mod=qiandao_n" method="post" class="form-horizontal" role="form">
  <input type="hidden" name="do" value="submit"/>
    <div class="form-group">
      <label class="col-sm-2 control-label">是否开启签到功能</label>
      <div class="col-sm-10"><select class="form-control" name="qiandao_open" default="';
    echo $conf['zz_qiandao_open'];
    echo '"><option value="0">关闭签到</option><option value="1">开启签到</option></select></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">关闭时提示信息</label>
      <div class="col-sm-10"><input type="text" name="qiandao_close_alert" value="';
    echo $conf['zz_qiandao_close_alert'];
    echo '" class="form-control" placeholder="关闭时提示信息"/>
    <pre>关闭时提示信息，只在签到功能关闭时展示给分站</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">旗舰分站签到奖励</label>
      <div class="col-sm-10"><input type="text" name="qiandao_power_2" value="';
    echo $conf['zz_qiandao_power_2'];
    echo '" class="form-control" placeholder="0.03"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">专业分站签到奖励</label>
      <div class="col-sm-10"><input type="text" name="qiandao_power_1" value="';
    echo $conf['zz_qiandao_power_1'];
    echo '" class="form-control" placeholder="0.02"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">普通用户签到奖励</label>
      <div class="col-sm-10"><input type="text" name="qiandao_power_0" value="';
    echo $conf['zz_qiandao_power_0'];
    echo '" class="form-control" placeholder="0.01"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">是否开启签到限制</label>
      <div class="col-sm-10"><select class="form-control" name="qiandao_limit_open" default="';
    echo $conf['zz_qiandao_limit_open'];
    echo '"><option value="0">关闭限制</option><option value="1">开启限制</option></select>
      <pre>此功能可对只签到撸余额的用户和分站达到较好的效果，有钱的站长随意哈~</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">签到限制有效天数</label>
      <div class="col-sm-10"><input type="text" name="qiandao_limit_day" value="';
    echo $conf['zz_qiandao_limit_day'];
    echo '" class="form-control" placeholder="3"/>
      <pre>表示多少天未满足下方的条件就限制用户或分站签到</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">签到限制目标金额</label>
      <div class="col-sm-10"><input type="text" name="qiandao_limit_point" value="';
    echo $conf['zz_qiandao_limit_point'];
    echo '" class="form-control" placeholder="0.01"/>
    <pre>对用户来说是最低消费金额，对分站来说是消费和提成加起来的最低金额。例如3天未消费1元（或提成+消费没有1元）就限制签到</pre>
    </div>
    </div><br/>
    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
     </div>
    </div>
  </form>
</div>
</div>
';
} elseif ($mod == 'mailcon_reset') {
    $faka_mail = '<b>商品名称：</b> [name]<br/><b>购买时间：</b>[date]<br/><b>以下是您的卡密信息：</b><br/>[kmdata]<br/>----------<br/><b>使用说明：</b><br/>[alert]<br/>----------<br/>[sitename]<br/>[siteurl]';
    saveSetting('faka_mail', $faka_mail);
    $ad = $CACHE->clear();
    if ($ad) {
        showmsg('虚拟商品发货模板重置成功！', 1);
    } else {
        showmsg('虚拟商品发货模板重置失败！<br/>' . $DB->error(), 4);
    }
} elseif ($mod == 'mailmodel') {
    echo '
<div class="block">
<div class="block-title"><h3 class="panel-title">发信邮件模板设置</h3></div>
<div class="">
  <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
  <input type="hidden" name="do" value="submit"/>
  <input type="hidden" name="action" value="发信邮件模板"/>
  <div class="form-group">
    <label class="col-sm-2 control-label">虚拟商品邮件模板</label>
    <div class="col-sm-10"><textarea class="form-control" name="faka_mail" id="faka_mail" rows="6">';
    echo $conf['zz_faka_mail'];
    echo "\r\n";
    echo '</textarea></div>
  </div>
  <div class="form-group">
    <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/><br/>
    <a href="./set.php?mod=mailcon_reset" class="btn btn-warning btn-block" onclick="return confirm(\'确定要重置吗？\');">重置模板设置</a><br/>
   </div>
  </div>
  </form>
</div>
<div class="panel-footer">
  <font color="green">变量代码：<br/>
  <a href="#" onclick="Addstr(\'faka_mail\',\'[kmdata]\');return false">[kmdata]</a>&nbsp;卡密内容<br/>
  <a href="#" onclick="Addstr(\'faka_mail\',\'[name]\');return false">[name]</a>&nbsp;商品名称<br/>
  <a href="#" onclick="Addstr(\'faka_mail\',\'[alert]\');return false">[alert]</a>&nbsp;商品简介<br/>
  <a href="#" onclick="Addstr(\'faka_mail\',\'[date]\');return false">[date]</a>&nbsp;购买时间<br/>
  <a href="#" onclick="Addstr(\'faka_mail\',\'[sitename]\');return false">[sitename]</a>&nbsp;站点名称<br/>
  <a href="#" onclick="Addstr(\'faka_mail\',\'[email]\');return false">[email]</a>&nbsp;收信人邮箱<br/></font>
  </div>
  </div>
  <script>
  function Addstr(id, str) {
    $("#"+id).val($("#"+id).val()+str);
  }
  </script>
  ';
} elseif ($mod == 'other') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">网站附加功能设置（当前为内测期，此处部分功能无法使用）</h3></div>
    <div class="">
      <form action="./set.php?mod=other_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
        <div class="form-group">
            <div class="form-group">
              <label class="col-sm-2 control-label">右侧按钮开关</label>

              <div class="col-sm-10">
                <select class="form-control" name="right_btn_open" default="' . $conf['zz_right_btn_open'] . '">
                  <option value="0">关闭</option><option value="1">启用</option>
                </select>
                <small>开启后使用本模板时前台会在右边悬浮显示下方设置的按钮</small>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">右侧悬浮按钮代码</label>
              <div class="col-sm-10">
                <textarea style="min-height: 80px;" class="form-control" name="right_btn_code" rows="10">' . htmlspecialchars($conf['zz_right_btn_code']) . '</textarea>
                <small>请参考默认代码改，注意不要动按钮代码以外的！如果改错了？<a href="?my=setMrCode">点我恢复默认</a></small>
              </div>
            </div><br/>
            <div class="form-group">
              <label class="col-sm-2 control-label">是否开启弹窗提示</label>
              <div class="col-sm-10">
              <select class="form-control" id="isToastr" name="isToastr" default="' . $conf['zz_isToastr'] . '"><option value="0" >关闭</option><option value="1">开启</option></select>
              <small>在部分模板下，网站首页在指定位置弹出一个小提示，可起到引导用户点击的效果</small>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">弹窗提示自定标题</label>
              <div class="col-sm-10">
              <input type="text" name="toastrTit" value="' . $conf['zz_toastrTit'] . '" class="form-control"/>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">弹窗提示自定代码</label>
              <div class="col-sm-10">
              <textarea style="min-height: 80px;height: 80px;" placeholder="支持简单的html代码" class="form-control" name="toastrMsg" rows="6">' . htmlspecialchars($conf['zz_toastrMsg']) . '</textarea>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">弹窗提示显示位置</label>
              <div class="col-sm-10">
              <select class="form-control" id="toastrDisplay" name="toastrDisplay" default="' . $conf['zz_toastrDisplay'] . '"><option value="toast-top-left" >顶部居左</option><option value="toast-top-center">顶部居中</option><option value="toast-top-right">顶部居右</option><option value="toast-bottom-left">底部居左</option><option value="toast-bottom-center">底部居中</option><option value="toast-bottom-right">底部居右</option></select>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">弹窗提示超时时间</label>
              <div class="col-sm-10">
                <div class="input-group">
                  <input type="text" name="toastrTimeOut" value="' . $conf['zz_toastrTimeOut'] . '" class="form-control"/>
                  <span class="input-group-addon">毫秒</span>
                  </div>
                  <small>弹窗提示自动关闭的时间，1秒=1000毫秒</small>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">附加自定义Js代码</label>
              <div class="col-sm-10">
              <textarea style="min-height: 80px;height: 80px;" placeholder="支持局部的js代码" class="form-control" name="toastrCode" rows="6">' . htmlspecialchars($conf['zz_toastrCode']) . '</textarea>
              <small>附加自定义Js代码，非编程爱好者请勿使用，否则报错自己负责</small>
              </div>
            </div>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
         </div>
        </div>
      </form>
    </div>
    </div>
    <script>
    var items = $("select[default]");
    for (i = 0; i < items.length; i++) {
        $(items[i]).val($(items[i]).attr("default")||0);
    }
    </script>
    ';
} elseif ($mod == 'viewEncode') {
    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">网站加密设置</h3></div>
    <div class="">
      <form action="./set.php?mod=viewEncode_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
        <div class="form-group">
            <div class="form-group">
              <label class="col-sm-2 control-label">开启首页加密模块</label>
              <div class="col-sm-10">
                <select class="form-control" name="viewEncode_index_open" default="' . $conf['viewEncode_index_open'] . '">
                  <option value="0">关闭</option>
                  <option value="1">页面主体加密(仅加密body不影响收录)</option>
                  <option value="2">页面全部加密(全部加密会影响收录)</option>
                </select>
                <small>开启加密后可隐藏源代码, 加上https后效果更佳</small>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">开启用户后台加密模块(暂时无效)</label>
              <div class="col-sm-10">
                <select class="form-control" name="viewEncode_user_open" default="' . $conf['viewEncode_user_open'] . '">
                  <option value="0">关闭</option>
                  <option value="1">开启</option>
                </select>
              </div>
            </div>
        </div>
        <div class="form-group">
          <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
         </div>
        </div>
      </form>
    </div>
    </div>
    <script>
    var items = $("select[default]");
    for (i = 0; i < items.length; i++) {
        $(items[i]).val($(items[i]).attr("default")||0);
    }
    </script>
    ';
} else {
    if ($mod == 'fenzhan_price_n' && $_POST['do'] == 'submit') {
        if (!$_POST['submit']) {
            showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
        } else {
            foreach ($_POST as $key => $value) {
                if ($key != 'submit') {
                    saveSetting($key, $value);
                }
            }
        }
        $ad = $CACHE->clear();
        if ($ad) {
            showmsg('修改成功！', 1);
        } else {
            showmsg('修改失败！<br/>' . $DB->error(), 4);
        }
    } elseif ($mod == 'fenzhan_price') {
        echo '<div class="block">
                <div class="block-title"><h3 class="panel-title">分站商品价格限制配置</h3></div>
            <div class="">
              <form action="./set.php?mod=fenzhan_price_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
                <div class="form-group">
                  <label class="col-sm-2 control-label">开启商品金额提升限制</label>
                  <div class="col-sm-10"><select class="form-control" name="fenzhan_price_open" default="';
        echo $conf['zz_fenzhan_price_open'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
                </div><br/>
                <div class="form-group">
                  <label class="col-sm-2 control-label">提升限制比例</label>
                  <div class="col-sm-10"><input type="text" name="fenzhan_price_max" value="';
        echo $conf['zz_fenzhan_price_max'];
        echo '" class="form-control" placeholder="填写百分比整数"/>
                    <pre>分站的商品最多可提升多少百分比，例如填60，将只能提升60%！可超过100（本限制只针对下方限制分类列表）</pre>
                    </div>
                </div><br/>
                <div class="form-group">
                  <label class="col-sm-2 control-label">限制商品分类列表</label>
                  <div class="col-sm-10"><input type="text" name="fenzhan_price_class" value="';
        echo $conf['zz_fenzhan_price_class'];
        echo '" class="form-control"/>
                    <pre>填写分类ID，多个用英文逗号,隔开</pre>
                    </div>
                </div><br/>
                <div class="form-group">
                  <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
                 </div>
                </div>
              </form>
            </div>
            </div>
            ';
    } elseif ($mod == 'fenzhan_n' && $_POST['do'] == 'submit') {
        if (!$_POST['submit']) {
            showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
        } else {
            foreach ($_POST as $key => $value) {
                if ($key != 'submit') {
                    saveSetting($key, $value);
                }
            }
        }
        $ad = $CACHE->clear();
        if ($ad) {
            showmsg('分站相关设置修改成功！', 1);
        } else {
            showmsg('分站相关设置修改失败！<br/>' . $DB->error(), 4);
        }
    } elseif ($mod == 'fenzhan') {
        echo '<div class="row">
    <!-- 分站开通设置-->
    <div class="col-sm-12 col-md-6 col-lg-6">
        <div class="block">
          <div class="block-title">
            <h3 class="panel-title">分站开通设置</h3>
            <span class="panel-tips">分站开通价格、成本、主站域名等</span>
             <span class="hide">
               <a href="./set.php?mod=tixian" class="btn btn-primary btn-xs">提现相关设置</a>
               <a href="./set.php?mod=tixian_df" class="btn btn-success btn-xs hide">代付接口设置</a>
             </span>
        </div>
        <div class="">
        <form action="./set.php?mod=fenzhan_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
          <div class="form-group">
            <label class="col-sm-2 control-label">自助开通分站</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_buy" default="';
        echo $conf['zz_fenzhan_buy'];
        echo '"><option value="1">开启</option><option value="0">关闭</option></select>
              </div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站自动续费</label>
            <div class="col-sm-10"><select class="form-control" name="renew_open" default="';
        echo $conf['zz_renew_open'] > 0 ? '1' : '0';
        echo '"><option value="1">开启</option><option value="0">关闭</option></select>
              <pre>开启后到期的分站将自动续费，续费失败将会提示充值</pre>
              </div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站可选择域名</label>
            <div class="col-sm-10"><input type="text" name="fenzhan_domain" value="';
        echo $conf['zz_fenzhan_domain'];
        echo '" class="form-control"/><pre>多个域名用英文半角,隔开！务必格式正确，否则分站可能会出现开通异常</pre></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">旗舰版价格</label>
            <div class="col-sm-10"><input type="text" name="fenzhan_price2" value="';
        echo $conf['zz_fenzhan_price2'];
        echo '" class="form-control"/></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">专业版价格</label>
            <div class="col-sm-10"><input type="text" name="fenzhan_price" value="';
        echo $conf['zz_fenzhan_price'];
        echo '" class="form-control"/></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">旗舰版成本价格</label>
            <div class="col-sm-10"><input type="text" name="fenzhan_cost2" value="';
        echo $conf['zz_fenzhan_cost2'];
        echo '" class="form-control"/><pre><span style="color:red">未设置该价格或低于开通价格时，上级代理可能会没提成！</span>分站续费/拿货价格，注意：分站成本价格请勿低于初始赠送余额！</pre></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">专业版成本价格</label>
            <div class="col-sm-10"><input type="text" name="fenzhan_cost" value="';
        echo $conf['zz_fenzhan_cost'];
        echo '" class="form-control"/><pre><span style="color:red">未设置该价格或低于开通价格时，上级代理可能会没提成！</span>分站续费/拿货价格，注意：分站成本价格请勿低于初始赠送余额！</pre></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">初始赠送余额</label>
            <div class="col-sm-10"><input type="text" name="fenzhan_free" value="';
        echo $conf['zz_fenzhan_free'];
        echo '" class="form-control"/></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">专业版升级价格</label>
            <div class="col-sm-10"><input type="text" name="fenzhan_upgrade" value="';
        echo $conf['zz_fenzhan_upgrade'];
        echo '" class="form-control" placeholder="不填写则不开启自助升级旗舰版功能"/></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">未开通网址显示404页面</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_page_404" default="';
        echo $conf['zz_fenzhan_page_404'] > 0 ? $conf['zz_fenzhan_page_404'] : '0';
        echo '"><option value="0">关闭</option><option value="1">开启</option></select>
              <pre>需要在下方填写主站要用的域名，否则不生效！！</pre>
              </div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">主站预留域名</label>
            <div class="col-sm-10"><textarea class="form-control" name="fenzhan_remain" rows="3">';
        echo $conf['zz_fenzhan_remain'];
        echo '</textarea><pre>设置部分域名无法被分站绑定（比如www的一级域名），多个域名用英文半角,隔开！</pre>
              </div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">注册到期延长月数</label>
            <div class="col-sm-10"><input type="text" name="fenzhan_expiry" value="';
        echo $conf['zz_fenzhan_expiry'];
        echo '" class="form-control"/></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站续费模式</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_renew_type" default="';
        echo $conf['zz_fenzhan_renew_type'] != "" ? $conf['zz_fenzhan_renew_type'] : '0';
        echo '"><option value="0">按开通原价续费</option><option value="1">按成本价续费</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">用户注册</label>
            <div class="col-sm-10"><select class="form-control" name="user_open" default="';
        echo $conf['zz_user_open'];
        echo '"><option value="1">开启</option><option value="0">关闭</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">注册用户购价等级</label>
            <div class="col-sm-10"><select class="form-control" name="user_level" default="';
        echo $conf['zz_user_level'];
        echo '"><option value="0">上级商品售价</option><option value="1">专业代理价格</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">开通自动复制主站公告代码</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_html" default="';
        echo $conf['zz_fenzhan_html'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
          </div><br/>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
            <input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/>
            <br/>
           </div>
          </div>
        </form>
        </div>
        </div>
    </div>
    <!-- / 分站开通设置-->
    <!-- 分站订单相关-->
    <div class="col-sm-12 col-md-6 col-lg-6">
        <div class="block">
          <div class="block-title">
            <h3 class="panel-title">分站订单相关</h3>
            <span class="panel-tips">跟分站相关的订单退款和订单提成设置</span>
        </div>
        <div class="">
        <form action="./set.php?mod=fenzhan_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站订单退款方式</label>
            <div class="col-sm-10"><select class="form-control" name="refund_type" default="';
        echo $conf['zz_refund_type'];
        echo '">
              <option value="1">当分站游客购买时主站手动联系游客并退款，同时扣除分站相应提成</option>
              <option value="2">统一退款到分站账户</option>
              </select>
              <pre>由于分站订单有两种，一种分站站长自己下单的，另一种是分站的游客客户下单的，请根据需求设置</pre>
              </div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站提成扣除方式</label>
            <div class="col-sm-10"><select class="form-control" name="refund_point_type" default="';
        echo $conf['zz_refund_point_type'] > 0 ? '1' : '0';
        echo '">
              <option value="0">扣除提成（默认）</option>
              <option value="1">扣除余额</option>
              </select>
              <pre>分站账户有提成余额和普通余额，其中只能提成余额可提现，请根据需求设置</pre>
              </div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">退款给分站的到账方式</label>
            <div class="col-sm-10"><select class="form-control" name="refund_fenzhan_type" default="';
        echo $conf['zz_refund_fenzhan_type'] > 0 ? '1' : '0';
        echo '">
              <option value="0">到提成（默认）</option>
              <option value="1">到余额</option>
              </select></div>
          </div><br/>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
            <input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/>
            <br/>
           </div>
          </div>
        </form>
        </div>
        </div>
    </div>
    <!-- / 分站订单相关-->
    <!-- 分站权限设置-->
    <div class="col-sm-12 col-md-6 col-lg-6">
        <div class="block">
          <div class="block-title">
            <h3 class="panel-title">分站功能权限设置</h3>
            <span class="panel-tips">设置某些功能权限是否可用</span>
        </div>
        <div class="">
        <form action="./set.php?mod=fenzhan_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
          <div class="form-group">
            <label class="col-sm-2 control-label">开启绑定手机号</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_tel" default="';
        echo $conf['zz_fenzhan_tel'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站推广图功能</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_tg" default="';
        echo $conf['zz_fenzhan_tg'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站可提交工单</label>
            <div class="col-sm-10"><select class="form-control" name="workorder_open" default="';
        echo $conf['zz_workorder_open'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站可上传logo</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_logo_open" default="';
        echo $conf['zz_fenzhan_logo_open'];
        echo '"><option value="0">关闭（安全推荐）</option><option value="1">开启</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站可设置公告</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_gonggao_open" default="';
        echo $conf['zz_fenzhan_gonggao_open'];
        echo '"><option value="0">关闭（安全推荐）</option><option value="1">开启</option></select>
              <pre>关闭后分站的所有公告将默认使用主站的公告内容</pre>
              </div>
          </div><br/>
           <div class="form-group">
         <label class="col-sm-2 control-label">自定义下级域名</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_domain_diy" default="';
        echo $conf['zz_fenzhan_domain_diy'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select>
              <pre>分站是否可以设置自己的域名，给用户搭建使用</pre></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站公告代码模式</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_gonggao_type" default="';
        echo $conf['zz_fenzhan_gonggao_type'];
        echo '"><option value="0">仅中英文/标点符号（安全模式）</option><option value="2">支持html代码</option></select></div>
          </div><br/>

          <div class="form-group hide">
            <label class="col-sm-2 control-label">生成卡密功能</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_kami" default="';
        echo $conf['zz_fenzhan_kami'];
        echo '"><option value="1">开启</option><option value="0">关闭</option></select></div>
          </div><br/>
           <div class="form-group">
            <label class="col-sm-2 control-label">自助更换域名</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_editd_open" default="';
        echo $conf['zz_fenzhan_editd_open'] != "" ? $conf['zz_fenzhan_editd_open'] : '0';
        echo '"><option value="1">开启</option><option value="0">关闭</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">更换域名费用</label>
            <div class="col-sm-10"><input type="text" name="fenzhan_editd_price" value="';
        echo $conf['zz_fenzhan_editd_price'];
        echo '" class="form-control"/></div>
          </div><br/>
          <div class="form-group">
                <label class="col-sm-2 control-label">分站查看下级余额</label>
                <div class="col-sm-10"><select class="form-control" name="fenzhan_readmoney" default="';
        echo $conf['zz_fenzhan_readmoney'];
        echo '"><option value="0">禁止查看</option><option value="1">可以查看</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站删除下级分站</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_deluser_open" default="';
        echo $conf['zz_fenzhan_deluser_open'];
        echo '"><option value="0">禁止删除</option><option value="1">删除后上级不可见但保留下级</option><option value="2">彻底删除下级</option></select></div>
          </div><br/>
          <div class="form-group">
            <label class="col-sm-2 control-label">分站可更换模板</label>
            <div class="col-sm-10"><select class="form-control" name="fenzhan_template" default="';
        echo $conf['zz_fenzhan_template'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
          </div><br/>
          <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
            <input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/>
            <br/>
           </div>
          </div>
        </form>
        </div>
        </div>
    </div>
    <!-- / 分站权限设置-->

    <!-- 分站默认配置-->
    <div class="col-sm-12 col-md-6 col-lg-6">
        <div class="block">
          <div class="block-title">
            <h3 class="panel-title">分站默认配置</h3>
        </div>
        <div class="">
        <form action="./set.php?mod=fenzhan_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
          <div class="form-group">
                <label class="col-sm-2 control-label">分站默认网站名称</label>
                <div class="col-sm-10"><input type="text" name="fenzhan_sitename" value="';
        echo $conf['zz_fenzhan_sitename'];
        echo '" class="form-control"/>
                  <pre>当分站未设置时，自动使用此设置</pre>
                  </div>
              </div><br/>
              <div class="form-group">
                <label class="col-sm-2 control-label">分站默认网站标题</label>
                <div class="col-sm-10"><input type="text" name="fenzhan_title" value="';
        echo $conf['zz_fenzhan_title'];
        echo '" class="form-control"/><pre>当分站未设置时，自动使用此设置</pre>
                  </div>
              </div><br/>
              <div class="form-group">
                <label class="col-sm-2 control-label">分站默认网站关键词</label>
                <div class="col-sm-10"><textarea class="form-control" name="fenzhan_description" rows="3">';
        echo $conf['zz_fenzhan_description'];
        echo '</textarea><pre>当分站未设置时，自动使用此设置！</pre></div>
              </div><br/>
              <div class="form-group">
                <label class="col-sm-2 control-label">分站默认网站介绍</label>
                <div class="col-sm-10"><input type="text" name="fenzhan_description" value="';
        echo $conf['zz_fenzhan_description'];
        echo '" class="form-control"/><pre>当分站未设置时，自动使用此设置</pre>
                  </div>
              </div><br/>
              <div class="form-group">
                <label class="col-sm-2 control-label">新增分站自动加价</label>
                <div class="col-sm-10"><select class="form-control" name="fenzhan_newSetPrice_open" default="';
        echo $conf['fenzhan_newSetPrice_open'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select>
                  <pre>开启后，新搭建的分站将根据下方设置的比例自动提升价格！方便分站用户可以直接宣传网站</pre>
                  </div>
              </div><br/>
              <div class="form-group">
                <label class="col-sm-2 control-label">新增分站出售价格加价倍数</label>
                <div class="col-sm-10"><input type="text" name="fz_ratio_price" value="';
        echo $conf['zz_fz_ratio_price'];
        echo '" class="form-control"/><pre>新增分站自动加价开启时可用，填整数。不能小于1！</pre>
                  </div>
              </div><br/>
               <div class="form-group">
                <label class="col-sm-2 control-label">新增分站下级拿货加价倍数</label>
                <div class="col-sm-10"><input type="text" name="fz_ratio_cost" value="';
        echo $conf['zz_fz_ratio_cost'];
        echo '" class="form-control"/><pre>新增分站自动加价开启且是旗舰分站时可用，填整数。不能小于1！</pre>
                  </div>
              </div><br/>
            <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
            <input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/>
            <br/>
           </div>
          </div>
        </form>
        </div>
        </div>
    </div>
    <!-- / 分站默认配置-->
</div>
<script>
$("select[name=\'fenzhan_buy\']").change(function(){
    if($(this).val() == 1){
        $("#frame_set1").css("display","inherit");
    }else{
        $("#frame_set1").css("display","none");
    }
});
</script>
';
    } elseif ($mod == 'tixian_df') {
        echo '<div class="block">
        <div class="block-title">
         <div class="form-inline">
            <h3 class="panel-title">代付接口设置</h3>
            <div class="form-group">
            <a href="./set.php?mod=tixian" class="btn btn-primary btn-xs">回到提现设置</a>
            <a href="./set.php?mod=fenzhan" class="btn btn-success btn-xs">分站相关设置</a>
            </div>
            </div>
        </div>
        <div class="">
          <form action="./set.php?mod=tixian_df_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
            <div class="form-group">
              <label class="col-sm-2 control-label">Api_url（代付接口地址）</label>
              <div class="col-sm-10"><input type="text" name="daifu_api_url" value="';
        echo $conf['zz_daifu_api_url'];
        echo '" class="form-control" placeholder="https://api.fcypay.com/transfer"/>
                <pre>例如风吹雨支付代付，请填写可以访问且完整的API接口地址</pre>
                </div>
            </div><br/>
            <div class="form-group">
              <label class="col-sm-2 control-label">Api_id（代付账号ID）</label>
              <div class="col-sm-10"><input type="text" name="daifu_api_id" value="';
        echo $conf['zz_daifu_api_id'];
        echo '" class="form-control" placeholder="填写Api_id"/>
                <pre>例如风吹雨支付代付，没有账号？<a href="' . ($conf['zz_daifu_api_url'] != "" ? $conf['zz_daifu_api_url'] : 'http://www.fcypay.com/index/user/register') . '" target="_blank">点我注册</a></pre>
                </div>
            </div><br/>
             <div class="form-group">
              <label class="col-sm-2 control-label">Api_key（代付账号key）</label>
              <div class="col-sm-10"><input type="text" name="daifu_api_key" value="';
        echo $conf['zz_daifu_api_key'];
        echo '" class="form-control" placeholder="填写Api_key"/>
                </div>
            </div><br/>
            <div class="form-group">
              <label class="col-sm-2 control-label">支付密码（代付账号支付密码）</label>
              <div class="col-sm-10"><input type="text" name="daifu_api_pwd" value="';
        echo $conf['zz_daifu_api_pwd'];
        echo '" class="form-control" placeholder="填写支付密码"/>
                </div>
            </div><br/>
            <div class="form-group">
              <label class="col-sm-2 control-label">是否验证真实姓名</label>
              <div class="col-sm-10"><select class="form-control" name="fenzhan_tixian_daifu" default="';
        echo $conf['zz_fenzhan_tixian_realname'] != "" ? $conf['zz_fenzhan_tixian_realname'] : 1;
        echo '"><option value="0">关闭验证</option><option value="1">开启验证</option></select></div>
            </div><br/>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
             </div>
            </div>
          </form>
        </div>
        </div>
  ';
    } elseif ($mod == 'tixian') {
        echo '
        <link href="../assets/public/select2/4.0.13/css/select2.min.css" rel="stylesheet"/>
        <script src="../assets/public/select2/4.0.13/js/select2.js"></script>
        <div class="block">
        <div class="block-title">
         <div class="form-inline">
            <h3 class="panel-title">代理提现设置</h3>
            <div class="form-group">
            <a href="./set.php?mod=tixian_df" class="btn btn-primary btn-xs">代付接口设置</a>
            <a href="./set.php?mod=fenzhan_price" class="btn btn-info btn-xs">分站价格设置</a>
            </div>
            </div>
        </div>
        <div class="">
          <form action="./set.php?mod=tixian_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
            <div class="form-group">
              <label class="col-sm-2 control-label">是否开启分站提现</label>
              <div class="col-sm-10"><select class="form-control" name="fenzhan_tixian" default="';
        echo $conf['zz_fenzhan_tixian'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">是否启用收款图</label>
              <div class="col-sm-10"><select class="form-control" name="fenzhan_skimg" default="';
        echo $conf['zz_fenzhan_skimg'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select>
                </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">可提现账户类型</label>
              <div class="col-sm-10"><select class="form-control" name="fenzhan_tixian_type" default="';
        echo $conf['zz_fenzhan_tixian_type'];
        echo '"><option value="0">仅余额账户（默认）</option><option value="1">提成和余额均可提现</option><option value="2">仅提成账户</option></select>
                </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">是否启用提成转余额</label>
              <div class="col-sm-10"><select class="form-control" name="fenzhan_point_to_money" default="';
        echo $conf['fenzhan_point_to_money'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select>
                </div>
            </div>
            <input type="hidden" name="fenzhan_tixian_pays" value="';
        echo $conf['fenzhan_tixian_pays'];
        echo '"/>
            <div class="form-group">
              <label class="col-sm-2 control-label">支付提现方式可选</label>
              <div class="col-sm-10"><select class="form-control" id="fenzhan_tixian_pays" multiple="multiple" data-json="';
        echo json_encode($conf['fenzhan_tixian_pays']);
        echo '"><option value="1">支付宝</option><option value="2">微信</option><option value="3">QQ钱包</option></select>
                <small>默认全部支付方式都可用。可多选组合，至少选择一个方式并保存后生效</small>
                </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">图片大小限制（logo和收款码）</label>
              <div class="col-sm-10">
              <input type="text" name="fenzhan_imglimit" value="';
        echo $conf['zz_fenzhan_imglimit'] > 0 ? $conf['zz_fenzhan_imglimit'] : '1024';
        echo '" class="form-control" placeholder="填写整数"/>
                <pre>单位KB。填写整数，不填默认最大1024KB！提示：1MB=1024KB,1GB=1024MB</pre>
                </div>
            </div>
            <div class="form-group hide">
              <label class="col-sm-2 control-label">是否启用代付提现(更安全)</label>
              <div class="col-sm-10"><select class="form-control" name="fenzhan_tixian_daifu" default="';
        echo $conf['zz_fenzhan_tixian_daifu'] != "" ? $conf['zz_fenzhan_tixian_daifu'] : 0;
        echo '"><option value="0">关闭</option><option value="1">开启</option></select>
                <pre>在代付可用的条件下，建议大站只使用代付，比收款图提现更安全，推荐指数五星</pre>
                </div>
            </div>

            <div class="form-group hide">
              <label class="col-sm-2 control-label">代付提现是否需要审核</label>
              <div class="col-sm-10"><select class="form-control" name="fenzhan_tixian_check" default="';
        echo $conf['zz_fenzhan_tixian_check'] != "" ? $conf['zz_fenzhan_tixian_check'] : '1';
        echo '"><option value="0">关闭</option><option value="1">开启</option></select>
                <pre>开启后代付提现方式时不会自动提现，需要人工操作才能提现到账</pre>
                </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">代理提现公告</label>
              <div class="col-sm-10">
               <textarea style="min-height: 80px;height: 80px;" placeholder="支持简单的html" class="form-control" name="fenzhan_tixian_alert" rows="6">' . htmlspecialchars($conf['zz_fenzhan_tixian_alert']) . '</textarea>
              <pre>只支持简单的html（尽量不要使用单引号）</pre>
                </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">代理提现成功自定义提示</label>
              <div class="col-sm-10">
               <textarea style="min-height: 80px;height: 80px;" placeholder="支持简单的html" class="form-control" name="fenzhan_tixian_succ" rows="6">' . htmlspecialchars($conf['zz_fenzhan_tixian_succ']) . '</textarea>
              <pre>只支持简单的html（尽量不要使用单引号）</pre>
                </div>
            </div>
            <div class="form-group hide">
              <label class="col-sm-2 control-label">代理提现失败自定义提示</label>
              <div class="col-sm-10">
               <textarea style="min-height: 80px;height: 80px;" placeholder="支持简单的html" class="form-control" name="fenzhan_tixian_err" rows="6">' . htmlspecialchars($conf['zz_fenzhan_tixian_err']) . '</textarea>
              <pre>只支持简单的html（尽量不要使用单引号）</pre>
                </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">提现退回默认原因</label>
              <div class="col-sm-10">
               <textarea style="min-height: 80px;height: 80px;" placeholder="支持简单的html" class="form-control" name="fenzhan_tixian_refund" rows="6">' . htmlspecialchars($conf['zz_fenzhan_tixian_refund']) . '</textarea>
              <pre>只支持简单的html（尽量不要使用单引号）</pre>
                </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">代付提现余额比例</label>
              <div class="col-sm-10"><input type="text" name="fenzhan_tixian_daifu_rate" value="';
        echo $conf['zz_fenzhan_tixian_daifu_rate'];
        echo '" class="form-control" placeholder="填写百分数"/>
                <pre>代付提现比例最高100，即表示不要手续费！填写95，表示手续费5%（用户选择极速提现才使用此费率）</pre>
                </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">分站提现余额比例</label>
              <div class="col-sm-10"><input type="text" name="fenzhan_tixian_rate" value="';
        echo $conf['zz_fenzhan_tixian_rate'];
        echo '" class="form-control" placeholder="填写百分数"/>
                <pre>提现比例最高100，即表示不要手续费！填写95，表示手续费5%，以此类推</pre>
                </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">分站提现最低余额</label>
              <div class="col-sm-10"><input type="text" name="fenzhan_tixian_min" value="';
        echo $conf['zz_fenzhan_tixian_min'];
        echo '" class="form-control"/></div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">是否开启用户提现</label>
              <div class="col-sm-10"><select class="form-control" name="user_tixian" default="';
        echo $conf['zz_user_tixian'];
        echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">用户提现余额比例</label>
              <div class="col-sm-10"><input type="text" name="user_tixian_rate" value="';
        echo $conf['zz_user_tixian_rate'];
        echo '" class="form-control" placeholder="填写百分数"/>
                <pre>提现比例最高100，即表示不要手续费！填写95，表示手续费5%，以此类推</pre>
                </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">用户提现最低余额</label>
              <div class="col-sm-10"><input type="text" name="user_tixian_min" value="';
        echo $conf['zz_user_tixian_min'];
        echo '" class="form-control"/></div>
            </div>
            <div class="form-group">
              <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/>
             </div>
            </div>
          </form>
        </div>
        </div>
        <script type="text/javascript">
        var values = ' . json_encode(json_decode('[' . $conf['fenzhan_tixian_pays'] . ']', true)) . ';
        $(document).ready(function($) {
            $("#fenzhan_tixian_pays").select2({
                placeholder: "请选择提现可选支付方式",
                language: "zh-CN",
                amultiple: true,
            });
            $("#fenzhan_tixian_pays").on("change", function(el){
                var val = $(this).val();
                if(typeof val === "object" && typeof val.join === "function"){
                   var data = val.join(",");
                }
                else{
                    var data =  val;
                }
                console.log("data", data);
                $("input[name=\'fenzhan_tixian_pays\']").val(data);
            });
            $("#fenzhan_tixian_pays").val(values).trigger("change");
        });
        </script>
  ';
    } else {
        if ($mod == 'template_set') {
            $type = input('get.type', 1);
            if ($type == 'wap') {
                $templateSetFile = TEMPLATE_ROOT . $conf['zz_template_mobile'] . '/set.php';
            } else {
                $templateSetFile = TEMPLATE_ROOT . $conf['zz_template'] . '/set.php';
            }
            if (file_exists($templateSetFile)) {
                include $templateSetFile;
            } else {
                showmsg('该模板设置文件不存在，可能没有自定义设置', 4);
            }
        } elseif ($mod == 'template') {
            $mblist = \core\Template::getList();
            echo '<div class="block">
                        <div class="nav-tabs-alt">
                            <ul class="nav nav-tabs nav-justified" data-toggle="tabs">
                                <li class="active text-cmgg-xs">
                                  <a href="#tab1" data-toggle="tab">
                                    PC模板
                                  </a>
                                </li>
                                <li class="text-cmgg-xs">
                                  <a href="#tab3" data-toggle="tab">
                                    手机模板
                                  </a>
                                </li>
                                <li class="text-cmgg-xs">
                                  <a href="#tab2" data-toggle="tab">
                                    模板设置
                                  </a>
                                </li>
                            </ul>
                            <div class="modal-body">
                                <div id="myTabContent" class="tab-content">
                                    <!--模板切换-->
                                    <div class="tab-pane fade in active" id="tab1">
                                            <div class="template-title">
                                            <p>说明：系统已安装共' . count($mblist) . '个模板。点击下方模板图片即可设置对应的模板为PC模板</p>
                                            <p style="color:red">注意：可在<a href="./plugin.php?type=template">插件市场</a>安装更多模板，请勿轻易使用未经审核的第三方模板，否则有被黑的风险且造成巨大损失</p>
                                            </div>
                                            <div class="template">
                                                    <div class="template-view col-sm-12 col-lg-6 col-md-6" style="padding:3px 0;float: none;    display: inline-block;">
                                                        <h4>模板缩略图</h4>
                                                        <div class="view"><img src="../template/' . $conf['template'] . '/preview.jpg"/></div>
                                                    </div>
                                                    <div class="template-option col-sm-12 col-lg-6 col-md-6">
                                                        <div class="option">
                                                        当前模板：' . $conf['template'] . '<br>
                                                        ';
            if (file_exists(TEMPLATE_ROOT . $conf['zz_template'] . '/set.php')) {
                echo '<a href="./set.php?mod=template_set&type=pc">进入该模板独立配置</a>';
            } else {
                echo '该模板无独立配置选项';
            }
            echo '
                                                        </div>
                                                    </div>
                                            </div>
                                            <div class="template-box">
                                                <ul class="template-list">
                                                ';
            foreach ($mblist as $key => $name) {
                $img = '../template/' . $name . '/preview.jpg';
                if (!file_exists($img)) {
                    $img = '../template/default/preview.jpg';
                }
                echo '
                                <li>
                                    <div class="template-image" onclick="setTemplate(\'' . $name . '\')">
                                    <img src="' . $img . '"/>
                                    </div>
                                    <div class="template-title">
                                    <a onclick="setTemplate(\'' . $name . '\')">' . $name . '</a>
                                    </div>
                                 </li>
                                  ';
            }

            echo '                            </ul>
                                          </div>
                                          <div class="panel-footer">
                                            <span class="glyphicon glyphicon-info-sign"></span>
                                            网站模板对应template目录里面的名称，会自动获取
                                          </div>
                                    </div>
                                    <!--模板切换 end-->

                                    <!--模板切换-->
                                    <div class="tab-pane fade" id="tab3">
                                            <div class="template-title">
                                            <p>说明：系统已安装共' . count($mblist) . '个模板。点击下方模板图片即可设置对应的模板为PC模板</p>
                                            <p style="color:red">注意：可在<a href="./plugin.php?type=template">插件市场</a>安装更多模板，请勿轻易使用未经审核的第三方模板，否则有被黑的风险且造成巨大损失</p>
                                            </div>
                                            <div class="template">
                                                    <div class="template-view col-sm-12 col-lg-6 col-md-6" style="padding:3px 0;float: none;    display: inline-block;">
                                                        <h4>模板缩略图</h4>
                                                        <div class="view"><img src="../template/' . $conf['template_mobile'] . '/preview.jpg"/></div>
                                                    </div>
                                                    <div class="template-option col-sm-12 col-lg-6 col-md-6">
                                                        <div class="option">
                                                        当前模板：' . (!empty($conf['template_mobile']) ? $conf['template_mobile'] : '与PC模板一致') . '<br>
                                                        ';
            if (file_exists(TEMPLATE_ROOT . $conf['zz_template'] . '/set.php')) {
                echo '<a href="./set.php?mod=template_set&type=wap">进入该模板独立配置</a>';
            } else {
                echo '该模板无独立配置选项';
            }
            echo '
                                                        </div>
                                                    </div>
                                            </div>
                                            <div class="template-box">
                                                <ul class="template-list">
                                                ';
            foreach ($mblist as $key => $name) {
                $img = '../template/' . $name . '/preview.jpg';
                if (!file_exists($img)) {
                    $img = '../template/default/preview.jpg';
                }
                echo '
                                <li>
                                    <div class="template-image" onclick="setTemplateMobile(\'' . $name . '\')">
                                    <img src="' . $img . '"/>
                                    </div>
                                    <div class="template-title">
                                    <a onclick="setTemplateMobile(\'' . $name . '\')">' . $name . '</a>
                                    </div>
                                 </li>
                                  ';
            }

            echo '                            </ul>
                                          </div>
                                          <div class="panel-footer">
                                            <span class="glyphicon glyphicon-info-sign"></span>
                                            网站模板对应template目录里面的名称，会自动获取
                                          </div>
                                    </div>
                                    <!--模板切换 end-->

                                    <!--模板设置-->
                                    <div class="tab-pane fade" id="tab2">
                                        <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
                                          <input type="hidden" name="do" value="submit"/>
                                           <input type="hidden" name="action" value="首页模板"/>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">手机模板启用方案</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="' . $conf['zz_template_mobile_type'] . '" name="template_mobile_type">
                                                 <option value="0">自定义</option>
                                                 <option value="1">与PC模板一致</option>
                                                </select>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">公共静态资源CDN</label>
                                              <div class="col-sm-10">
                                              <select class="form-control" name="cdnpublic" onchange="cdnShow(this.value)" default="';
            echo $conf['zz_cdnpublic'];
            echo '">
                                              <option value="0">本地资源(默认|最稳定)</option>
                                              <option value="1">75CDN静态资源（推荐二|速度快）</option>
                                              <option value="-1">自定义公共CDN接口</option>
                                              </select>
                                              <pre>可对带宽配置较低的服务器站点加速，但如果出现卡顿请切换回本地</pre>
                                              </div>
                                            </div><br/>
                                            <div class="form-group" id="cdnpublic_display" style="display:none">
                                              <label class="col-sm-2 control-label">公共静态资源地址</label>
                                              <div class="col-sm-10">
                                              <input type="text" name="cdnpublic_url" value="';
            echo $conf['zz_cdnpublic_url'];
            echo '" class="form-control"/><pre>为避免内置的失效，站长们可自定义静态资源CDN地址,填写后请先测试是否可用！</pre>
                                              </div>
                                            </div>
                                            <div class="form-group" style="display: none">
                                              <label class="col-sm-2 control-label">私有静态资源地址</label>
                                              <div class="col-sm-10">
                                              <select class="form-control" name="cdnserver" onchange="cdnShow2(this.value)" default="';
            echo $conf['zz_cdnserver'];
            echo '">
                                              <option value="0">关闭</option>
                                              <option value="-1">自定义私有CDN接口</option>
                                              </select>
                                              </div>
                                            </div><br/>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">私有静态资源地址</label>
                                              <div class="col-sm-10">
                                              <input type="text" name="cdnserver_url" value="';
            echo $conf['zz_cdnserver_url'];
            echo '" class="form-control"/><pre style="color:red;">如果模板或插件有使用伪静态且无法正常下单，请在此处填/或完整首页完整地址！</pre>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">是否关闭商品搜索功能</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="';
            echo $conf['zz_search_close'] > 0 ? '1' : '0';
            echo '" name="search_close">
                                                 <option value="0">启用搜索(默认)</option>
                                                 <option value="1">关闭搜索</option>
                                                </select>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">是否开启批量下单功能</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="';
            echo $conf['zz_shop_batch'];
            echo '" name="shop_batch">
                                                 <option value="0">关闭</option>
                                                 <option value="1">开启登录用户批量</option>
                                                 <option value="2">开启所有可批量（含游客）</option>
                                                </select>
                                                <samll>网站首页下单时仅部分模板支持，请自行测试是否可用</samll>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">自动使用模板后台</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="' . $conf['template_layout_off'] . '" name="template_layout_off">
                                                 <option value="0">开启</option>
                                                 <option value="1">关闭</option>
                                                </select>
                                                <small>如果当前模板存在对应的分站后台模板，则自动使用模板分站后台</small>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">使用模板的登录UI</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="' . $conf['template_layout_login_off'] . '" name="template_layout_login_off">
                                                 <option value="0">开启</option>
                                                 <option value="1">关闭</option>
                                                </select>
                                                <small>选择开启后，如果当前模板存在对应的后台登录页面，则自动使用</small>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">使用模板的注册UI</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="' . $conf['template_layout_reg_off'] . '" name="template_layout_reg_off">
                                                 <option value="0">开启</option>
                                                 <option value="1">关闭</option>
                                                </select>
                                                <small>选择开启后，如果当前模板存在对应的后台注册页面，则自动使用</small>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">使用模板的找回密码UI</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="' . $conf['template_layout_findpwd_off'] . '" name="template_layout_findpwd_off">
                                                 <option value="0">开启</option>
                                                 <option value="1">关闭</option>
                                                </select>
                                                <small>选择开启后，如果当前模板存在对应的后台注册页面，则自动使用</small>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">是否展示销量</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="' . $conf['zz_template_sale'] . '" name="template_sale">
                                                 <option value="0">关闭</option>
                                                 <option value="1">开启</option>
                                                </select>
                                                <small>支持部分模板，后续将更新支持更多</small>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">全站固定模板</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="' . $conf['zz_template_fixed'] . '" name="template_fixed">
                                                 <option value="0">关闭</option>
                                                 <option value="1">开启</option>
                                                </select>
                                                <small>开启后，将全站固定使用主站设置的模板</small>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">是否开启购物车功能</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="';
            echo $conf['zz_shoppingcart'] > 0 ? '1' : '0';
            echo '" name="shoppingcart">
                                                 <option value="0">关闭</option>
                                                 <option value="1">开启</option>
                                                </select>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">登录后才能下单</label>
                                                  <div class="col-sm-10"><select class="form-control" name="index_regbuy" default="';
            echo $conf['zz_index_regbuy'];
            echo '"><option value="0">关闭</option><option value="1">开启</option></select>
                                                <pre>开启后游客访问网站会自动跳转登录页面</pre>
                                                </div>
                                            </div><br/>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">Bing随机壁纸背景</label>
                                              <div class="col-sm-10"><select class="form-control" name="ui_bing" default="';
            echo $conf['zz_ui_bing'];
            echo '"><option value="0">关闭</option><option value="1">每天自动随机换</option><option value="2">每3分钟自动随机换</option></select>
                                                <pre>注意：仅部分模板可以使用背景，如store等商城就没有背景</pre>
                                                </div>
                                            </div><br/>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">首页下单显示风格</label>
                                              <div class="col-sm-10"><select class="form-control" name="ui_shop" default="';
            echo $conf['zz_ui_shop'];
            echo '"><option value="0">经典模式</option><option value="1">分类图片宫格</option><option value="2">分类图片列表</option><option value="3">分类图片宫格2</option></select></div>
                                            </div><br/>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">首页商品显示风格</label>
                                              <div class="col-sm-10"><select class="form-control" name="ui_tool" default="';
            echo $conf['zz_ui_tool'] > 0 ? '1' : '0';
            echo '"><option value="0">经典下拉框</option><option value="1">新版全名称</option></select>
                                              <pre>为解决下拉框在苹果手机下商品过长显示不全的问题，新增了一个可以显示商品全名称，按需设置即可！</pre>
                                              </div>
                                            </div><br/>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">商品列表默认显示</label>
                                              <div class="col-sm-10"><select class="form-control" name="tool_show" default="';
            echo $conf['zz_tool_show'] != "" ? $conf['zz_tool_show'] : '1';
            echo '"><option value="0">关闭显示</option><option value="1">开启显示</option></select></div>
                                            </div><br/>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">模板缓存开关【beta 测试版】</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="' . $conf['zz_template_cache'] . '" name="template_cache">
                                                 <option value="0">关闭</option>
                                                 <option value="1">开启</option>
                                                </select>
                                                <small>开启后，可大幅提升部分加载时间过长的模板访问速度，如faka和table模板！<span style="color:red">测试阶段：如某模板部分功能有问题请关闭并提交工单（需提供模板名称和功能异常截图&描述）</span></small>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-2 control-label">模板缓存时间</label>
                                                <div class="col-sm-10">
                                                    <div class="input-group">
                                                    <input type="text" name="template_expires" value="';
            echo $conf['zz_template_expires'];
            echo '" class="form-control"/>
                                                    <span class="input-group-addon">分钟</span>
                                                    </div>
                                                    <pre>网站前台首页缓存时间，最低设置1分钟，一般5~10分钟最佳。&nbsp;如需清除模板缓存请<a href="./set.php?mod=removeCache">点我清除模板缓存</a></pre>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-2 control-label">首页自动播放音乐链接</label>
                                                <div class="col-sm-10">
                                                    <div class="input-group">
                                                    <input type="text" name="musicurl" value="';
            echo $conf['zz_musicurl'];
            echo '"   class="form-control"/>
                                                    </div>
                                                    <pre>部分模板支持播放音乐，填写可以播放音乐的直连，不能是歌单主页等链接！</pre>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">价格下方推荐注册</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="' . $conf['zz_tjreg_open'] . '" name="tjreg_open">
                                                 <option value="0">关闭</option>
                                                 <option value="1">开启</option>
                                                </select>
                                                <small>开启后，前台选中商品后。在商品价格下方固定显示一个注册优惠引导！如需修改显示内容，请在template/default/shop.inc.php里修改</small>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">分站开通默认模板</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" default="' . $conf['zz_template_default'] . '" name="template_default">';
            foreach ($mblist as $row) {
                echo '<option value="' . $row . '">' . $row . '</option>';
            }
            ;
            echo '</select>
                                                <small>设置好后新开分站或模板不存在时将默认使用该模板</small>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">是否隐藏默认模板</label>
                                              <div class="col-sm-10">
                                                <select class="form-control" name="template_default_off" default="' . $conf['zz_template_default_off'] . '">
                                                <option value="0">关闭</option>
                                                <option value="1">开启</option>
                                                </select>
                                                <small>开启后，分站将无法选择默认模板（不会影响其他功能）</small>
                                              </div>
                                            </div>
                                            <div class="form-group">
                                              <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
                                             </div>
                                            </div>
                                          </form>
                                    </div>
                                    <!--模板设置 end-->
                                </div>
                            </div>
                       </div>
                </div>

</div>
</div>
<script>
function setTemplate(name){
    var ii = layer.load(2, {
        shade: [0.1, "#fff"]
    });
    $.ajax({
        type: "POST",
        url: "./ajax.php?act=setTemplate",
        dataType: "json",
        data: {
            template: name
        },
        success: function(data) {
            layer.close(ii);
            if (data.code == 0) {
                layer.msg("设置PC端模板为【" + name + "】成功",{
                    time:1000,
                    end:function(){
                        window.location.reload();
                    }
                });
            }
            else {
                layer.alert(data.msg);
            }
        },
        error: function(data) {
            layer.close(ii);
            layer.msg("出现错误，请提交工单处理处理！");
            return false;
        }
    });
}

function setTemplateMobile(name){
    var ii = layer.load(2, {
        shade: [0.1, "#fff"]
    });
    $.ajax({
        type: "POST",
        url: "./ajax.php?act=setTemplateMobile",
        dataType: "json",
        data: {
            template: name
        },
        success: function(data) {
            layer.close(ii);
            if (data.code == 0) {
                layer.msg("设置手机端模板为【" + name + "】成功",{
                    time:1000,
                    end:function(){
                        window.location.reload();
                    }
                });
            }
            else {
                layer.alert(data.msg);
            }
        },
        error: function(data) {
            layer.close(ii);
            layer.msg("出现错误，请提交工单处理处理！");
            return false;
        }
    });
}
function cdnShow(val){
    if(val == (-1)){
        $("#cdnpublic_display").show();
    }
    else{
        $("#cdnpublic_display").hide();
    }
}
function cdnShow2(val){
    if(val == (-1)){
        $("#cdnserver_display").show();
    }
    else{
        $("#cdnserver_display").hide();
    }
}

var items = $("select[default]");
for (i = 0; i < items.length; i++) {
    $(items[i]).val($(items[i]).attr("default")||0);
}
$("select[name=cdnpublic]").change();
</script>
';
        } elseif ($mod == 'removeCache') {
            $data = \core\Template::removeCache();
            if ($data === true) {
                showmsg('清除所有模板缓存文件成功！', 1);
            } else {
                showmsg('清除失败，请手动去FTP文件管理的/cache/目录清除！', 3);
            }
        } else {
            if ($mod == 'defend_n' && $_POST['do'] == 'submit') {
                $defendid = intval($_POST['defendid']);
                $file     = "<?php\r\n//防CC模块设置\r\ndefine('CC_Defender', " . $defendid . ");\r\n?>";
                file_put_contents(SYSTEM_ROOT . 'base.php', $file);
                showmsg('修改成功！', 1);
            } elseif ($mod == 'defend') {
                echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">防CC模块设置</h3></div>
<div class="">
  <form action="./set.php?mod=defend_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
    <div class="form-group">
      <label class="col-sm-2 control-label">CC防护等级</label>
      <div class="col-sm-10"><select class="form-control" name="defendid" default="';
                echo CC_Defender;
                echo '">
      <option value="0">关闭</option>
      <option value="1">低(推荐)</option>
      <option value="2">中</option>
      <option value="3">高</option>
      </select></div>
    </div><br/>
    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
     </div>
    </div>
  </form>
</div>
<div class="panel-footer">
<span class="glyphicon glyphicon-info-sign"></span>CC防护说明<br/>
高：全局使用防CC，会影响网站APP和对接软件的正常使用<br/>
中：会影响搜索引擎的收录，建议仅在正在受到CC攻击且防御不佳时开启<br/>
低：用户首次访问进行验证（推荐）<br/>
</div>
</div>
';
            } elseif ($mod == 'shequ_n' && $_POST['do'] == 'submit') {
                $shequ_tixing = intval(input('post.shequ_tixing'));
                $mail_rmb     = intval(input('post.mail_rmb'));
                saveSetting('shequ_tixing', $shequ_tixing);
                saveSetting('mail_rmb', $mail_rmb);
                $ad = $CACHE->clear();
                if ($ad) {
                    showmsg('修改成功！', 1);
                } else {
                    showmsg('修改失败！<br/>' . $DB->error(), 4);
                }
            } elseif ($mod == 'shequ') {
                echo '<div class="block">
                    <div class="block-title"><h3 class="panel-title">网站对接配置</h3></div>
                <div class="">
                  <form action="./set.php?mod=shequ_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
                    <div class="form-group">
                      <label class="col-sm-2 control-label">余额不足提醒</label>
                      <div class="col-sm-10"><select class="form-control" name="mail_rmb" default="';
                echo $conf['zz_mail_rmb'] > 0 ? '1' : '0';
                echo '"><option value="0">关闭</option><option value="1">开启</option></select>
                                <small>开启后，对接站余额不足时可自动提醒，发送间隔3小时一次（不可修改提醒间隔，需配置好发信邮箱）！<a href="./set.php?mod=mail">设置发信邮箱</a></small>
                                </div>
                    </div><br/>
                    <div class="form-group">
                      <label class="col-sm-2 control-label">下单失败发送提醒邮件</label>
                      <div class="col-sm-10"><select class="form-control" name="shequ_tixing" default="';
                echo $conf['zz_shequ_tixing'];
                echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
                    </div><br/>
                    <div class="form-group">
                      <label class="col-sm-2 control-label">亿樂api接口地址</label>
                      <div class="col-sm-10">
                      <input type="text" name="shequ_yile_api" class="form-control" value="';
                if (empty($conf['shequ_yile_api'])) {
                    $conf['shequ_yile_api'] = 'api.yilesup.net';
                }
                echo $conf['shequ_yile_api'];
                echo '"/></div>
                    </div><br/>
                    <div class="form-group">
                      <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
                     </div>
                    </div>
                  </form>
                </div>
                <div class="panel-footer">
                <span class="glyphicon glyphicon-info-sign"></span>
                使用此功能，用户下单后提交到社区失败会发生一封邮件提醒给主站设置的客服QQ。<br/>
                <span class="glyphicon glyphicon-info-sign"></span>
                如果对接网站开启了金盾等防火墙可能导致无法成功提交！<br/>
                <span class="glyphicon glyphicon-info-sign"></span>
                API对接设置：<a href="./set.php?mod=api_set">点击配置</a>
                </div>
                </div>
                ';
            } elseif ($mod == 'cloneset') {
                $key = md5($password_hash . md5(SYS_KEY) . $conf['zz_apikey']);
                echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">克隆站点配置</h3></div>
<div class="">
  <form action="./set.php?mod=shequ_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
    <div class="form-group">
      <label class="col-sm-2 control-label">克隆密钥</label>
      <div class="col-sm-10"><input type="text" name="key" value="';
                echo $key;
                echo '" class="form-control" disabled/></div>
    </div>
  </form>
</div>
<div class="panel-footer">
<span class="glyphicon glyphicon-info-sign"></span>
此密钥是用于其他站点克隆本站商品<br/>
提示：修改API对接密钥可同时重置克隆密钥。<br/>
</div>
</div>
';
            } elseif ($mod == 'dwz_n') {
                if ($_POST['do'] != 'submit') {
                    showmsg('请通过页面保存提交数据！', 3);
                }

                foreach ($_POST as $key => $value) {
                    if ($key == 'do' || $key == 'dwz_api_url' && empty($value)) {
                        continue;
                    }
                    saveSetting($key, addslashes($value));
                }
                $ad = $CACHE->clear();
                if ($ad) {
                    showmsg('防红接口配置修改成功！', 1);
                } else {
                    showmsg('防红接口配置修改失败！<br/>' . $DB->error(), 4);
                }

            } elseif ($mod == 'dwz') {
                $selects  = '';
                $selects2 = '<option value="2">跳转防红</option><option value="3">直连防红</option>';
                if ($conf['zz_dwz_api'] == 3 || empty($conf['zz_dwz_api'])) {
                    $result = getDwzApiList();
                    if ($result['code'] == 0 || $result['code'] == 'ok') {
                        foreach ($result['data'] as $key => $item) {
                            $selects .= '<option value="' . $item['type'] . '" >' . $item['name'] . '</option>';
                        }
                    }
                    if (!$conf['dwz_type']) {
                        $conf['dwz_type'] = 'mi';
                    }
                    if (!$conf['dwz_pattern']) {
                        $conf['dwz_pattern'] = '2';
                    }
                }
                echo '
              <div class="block">
              <div class="block-title"><h3 class="panel-title">防红链接生成接口设置</h3></div>
              <div class="">
                <form action="./set.php?mod=dwz_n" method="post" role="form"><input type="hidden" name="do" value="submit"/>
                <div class="alert alert-info">
                    短链接口的配置信息需要选择保存后刷新页面才能看到！
                </div>
                <div class="form-group">
                <label>短链接口选择：</label>
                  <select class="form-control" name="dwz_api" default="' . $conf['zz_dwz_api'] . '">
                    <option value="0">不使用防红接口</option>
                    <option value="3">AE防红网-专业域名防红</option>
                    <option value="-1">自定义防红接口</option>
                  </select>
                  <pre id="dwz_tips" style="display:none;"></pre>
                </div>
                <div class="form-group" id="fanghong_token" style="display:none;">
                    <label>短链Token：</label>
                    <input type="text" name="dwz_token" value="' . $conf['dwz_token'] . '" class="form-control" placeholder="防洪API的使用token"/>
                    <pre>还没有？<a href="http://www.aedwz.com/" target="_blank">点击注册获取Token</a></pre>
                </div>
                <div class="form-group" id="fanghong_type" style="display:none;">
                    <label>选择短链接口通道：</label>
                    <select class="form-control" name="dwz_type" default="' . $conf['dwz_type'] . '">
                    ' . $selects . '
                    </select>
                </div>
                <div class="form-group" id="fanghong_pattern" style="display:none;">
                    <label>选择短链缩短类型：</label>
                    <select class="form-control" name="dwz_pattern" default="' . $conf['dwz_pattern'] . '">
                    ' . $selects2 . '
                    </select>
                </div>
                <div class="form-group" id="fanghong_diy" style="display:none;">
                <label>自定义接口地址：</label>
                  <div class="input-group">
                  <input type="text" name="dwz_api_url" value="' . $conf['zz_dwz_api_url'] . '" class="form-control" placeholder="不填写则关闭防红链接生成"/>
                  <div class="input-group-addon" onclick="checkurl()"><small>检测地址</small></div>
                </div></div>
                <div class="form-group">
                   <label>授权域名列表：</label>
                   <input type="text" name="dwz_api_urls" value="' . $conf['zz_dwz_api_urls'] . '" class="form-control" placeholder="不填写则所有域名都使用防洪"/>
                      <pre>填写已授权防洪接口权限的域名，多个用英文逗号,隔开！留空则所有域名都使用</pre>
                </div>
                <div class="form-group">
                  <input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
                </div>
                </form>
              </div>
              <div class="panel-footer" id="fanghong_diy1">
              <span class="glyphicon glyphicon-info-sign"></span>
              <span>一般防红接口地址为 http://防红网站域名/dwz.php?longurl= 具体可以咨询相应站长</span>
              </div>
              </div>
              <div class="block">
                <div class="block-title"><h3 class="panel-title">获取防红链接</h3></div>
                <div class="">
                  <div class="input-group">
                    <span class="input-group-addon">网址</span>
                    <input class="form-control" id="longurl" value="' . $_SERVER['HTTP_HOST'] . '">
                  </div>
                  <div class="well well-sm">如果您的网址在QQ内报毒或者打不开，您可以使用此功能生成防毒链接！</div>
                  <a class="btn btn-block btn-success" id="create_url">生成我的防红链接</a>
                </div>
              </div>
              <script src="//cdn.staticfile.org/layer/2.3/layer.js"></script>
              <script src="//cdn.staticfile.org/clipboard.js/1.7.1/clipboard.min.js"></script>
              <div class="modal fade in" id="fanghongurl" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog">
                  <div class="modal-content">
                    <div class="modal-header"><button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">�</span><span class="sr-only">关闭</span></button><h4 class="modal-title">防红链接生成</h4></div>
                    <div class="modal-body">
                      <div class="form-group"><div class="input-group"><div class="input-group-addon">防红链接</div><input type="text" id="target_url" value="" class="form-control" disabled=""></div></div>
                      <center><button class="btn btn-info btn-sm" id="recreate_url">重新生成</button>&nbsp;<button class="btn btn-warning btn-sm copy-btn" id="copyurl" data-clipboard-text="">一键复制链接</button></center>
                    </div>
                    <div class="modal-footer"><button type="button" class="btn btn-white" data-dismiss="modal">关闭</button></div>
                  </div>
                </div>
              </div>
              <div class="modal fade in" id="fanghongvip" tabindex="-1" role="dialog" aria-hidden="true">
                <div class="modal-dialog">
                  <div class="modal-content">
                    <div class="modal-header"><button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">�</span><span class="sr-only">关闭</span></button><h4 class="modal-title">防红链接生成</h4></div>
                    <div class="modal-body" id="fanghonglist">

                    </div>
                    <div class="modal-footer"><button type="button" class="btn btn-white" data-dismiss="modal">关闭</button></div>
                  </div>
                </div>
              </div>
              <script>
              function checkurl(){
                var url = $("input[name=\'fanghong_url\']").val();
                if(url.indexOf(\'http\')>=0 && url.indexOf(\'=\')>=0){
                  var ii = layer.load(2, {shade:[0.1,\'#fff\']});
                  $.ajax({
                    type : "POST",
                    url : "ajax.php?act=checkdwz",
                    data : {url:url},
                    dataType : \'json\',
                    success : function(data) {
                      layer.close(ii);
                      if(data.code == 1){
                        layer.msg(\'检测正常\');
                      }else if(data.code == 2){
                        layer.alert(\'链接无法访问或返回内容不是json格式\');
                      }else{
                        layer.alert(\'该链接无法访问\');
                      }
                    } ,
                    error:function(data){
                      layer.close(ii);
                      layer.msg(\'目标URL连接超时\');
                      return false;
                    }
                  });
                }else{
                  layer.alert(\'链接地址错误\');
                }
              }
              $(document).ready(function(){
              $("select[name=\'dwz_api\']").change(function(){
                if($(this).val() == (0-1) ){
                  $("#fanghong_diy").css("display","inherit");
                  $("#fanghong_diy1").css("display","inherit");
                }else{
                  $("#fanghong_diy").css("display","none");
                  $("#fanghong_diy1").css("display","none");
                }
                if($(this).val() == 3){
                    $("#dwz_tips").html("已认证|官方推荐防红：AE防红网www.aedwz.com").css("display","inherit");
                    $("#fanghong_token").css("display","inherit");
                    $("#fanghong_type").css("display","inherit");
                    $("#fanghong_pattern").css("display","inherit");
                }
                else{
                    $("#dwz_tips").css("display","none");
                    $("#fanghong_token").css("display","none");
                    $("#fanghong_type").css("display","none");
                    $("#fanghong_pattern").css("display","none");
                }
              });

              var url = $("input[id=\'longurl\']").val();
              var clipboard = new Clipboard(\'.copy-btn\', {
                  container: document.getElementById(\'fanghongurl\')
              });
              clipboard.on(\'success\', function (e) {
                layer.msg(\'复制成功！\');
              });
              clipboard.on(\'error\', function (e) {
                layer.msg(\'复制失败，请长按链接后手动复制\');
              });
              var clipboard = new Clipboard(\'.copy-btn\', {
                  container: document.getElementById(\'fanghongvip\')
              });
              clipboard.on(\'success\', function (e) {
                layer.msg(\'复制成功！\');
              });
              clipboard.on(\'error\', function (e) {
                layer.msg(\'复制失败，请长按链接后手动复制\');
              });
              $("#create_url").click(function(){
                var self = $(this);
                if (self.attr("data-lock") === "true") return;
                else self.attr("data-lock", "true");
                var ii = layer.load(1, {shade: [0.1, \'#fff\']});
                $.get("ajax.php?act=create_url&longurl="+url, function(data) {
                  layer.close(ii);
                  if(data.code == 0){
                    $("#target_url").val(data.url);
                    $("#copyurl").attr(\'data-clipboard-text\',data.url);
                    $(\'#fanghongurl\').modal(\'show\');
                  }else{
                    layer.alert(data.msg);
                  }
                  self.attr("data-lock", "false");
                }, \'json\');
              });
              $("#recreate_url").click(function(){
                var self = $(this);
                if (self.attr("data-lock") === "true") return;
                else self.attr("data-lock", "true");
                var ii = layer.load(1, {shade: [0.1, \'#fff\']});
                $.get("ajax.php?act=create_url&force=1&longurl="+url, function(data) {
                  layer.close(ii);
                  if(data.code == 0){
                    layer.msg(\'生成链接成功\');
                    $("#target_url").val(data.url);
                    $("#copyurl").attr(\'data-clipboard-text\',data.url);
                  }else{
                    layer.alert(data.msg);
                  }
                  self.attr("data-lock", "false");
                }, \'json\');
              });
              $("#create_url2").click(function(){
                var self = $(this);
                if (self.attr("data-lock") === "true") return;
                else self.attr("data-lock", "true");
                var ii = layer.load(1, {shade: [0.1, \'#fff\']});
                $("#fanghonglist").empty();
                $.get("ajax.php?act=create_dwz&longurl="+url, function(data) {
                  layer.close(ii);
                  if(data.code == 0){
                    $.each(data.data, function(k, v) {
                      $(\'#fanghonglist\').append(\'<div class="form-group"><div class="input-group"><div class="input-group-addon">\'+v.name+\'</div><input type="text" value="\'+v.url+\'" class="form-control" disabled=""><div class="input-group-addon"><a class="copy-btn" data-clipboard-text="\'+v.url+\'" href="javascript:;"><i class="fa fa-copy"></i></a></div></div></div>\');
                    });
                    $(\'#fanghongvip\').modal(\'show\');
                  }else{
                    layer.alert(data.msg);
                  }
                  self.attr("data-lock", "false");
                }, \'json\');
              });
              $("select[name=\'dwz_api\']").change();
              });
              </script>
              <script>
              var items = $("select[default]");
              for (i = 0; i < items.length; i++) {
                $(items[i]).val($(items[i]).attr("default")||0);
              }
              </script>
                  </div>
                </div>';
            } elseif ($mod == 'mailtest') {
                $mail_name = $conf['zz_mail_recv'] ? $conf['zz_mail_recv'] : $conf['zz_mail_name'];
                if (!empty($mail_name)) {
                    $result = send_mail($mail_name, '邮件发送测试。', '这是一封测试邮件！<br/><br/>来自：' . $siteurl);
                    if ($result == 1) {
                        showmsg('邮件发送成功！<br>如果未收到请检查垃圾箱或是否已拦截', 1);
                    } else {
                        showmsg('邮件发送失败！' . $result, 3);
                    }
                } else {
                    showmsg('您还未设置邮箱！', 3);
                }
            } elseif ($mod == 'mail_n' && $_POST['do'] == 'submit') {
                $mail_cloud = $_POST['mail_cloud'];
                $mail_name  = $mail_cloud == 1 ? $_POST['mail_name2'] : $_POST['mail_name'];
                if (!$_POST['submit']) {
                    showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
                } else {
                    foreach ($_POST as $key => $value) {
                        if ($key == 'mail_name') {
                            saveSetting('mail_name', $mail_name);
                        } elseif ($key != 'submit') {
                            saveSetting($key, $value);
                        }
                    }
                }
                $ad = $CACHE->clear();
                if ($ad) {
                    showmsg('修改成功！', 1);
                } else {
                    showmsg('修改失败！<br/>' . $DB->error(), 4);
                }
            } elseif ($mod == 'mail') {
                echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">发信邮箱配置</h3></div>
<div class="">
  <form action="./set.php?mod=mail_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
    <div class="form-group">
      <label class="col-sm-2 control-label">收信邮箱</label>
      <div class="col-sm-10"><input type="text" name="mail_recv" value="';
                echo $conf['zz_mail_recv'];
                echo '" class="form-control" placeholder="测试用，不填默认为发信邮箱"/></div>
    </div><br/>
    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>';
                echo '[<a href="set.php?mod=mailtest">给 ';
                echo $conf['zz_mail_recv'] ? $conf['zz_mail_recv'] : $conf['zz_mail_name'];
                echo ' 发一封测试邮件</a>]';
                echo '   </div><br/>
    </div>
  </form>
</div>
<div class="panel-footer">
<span class="glyphicon glyphicon-info-sign"></span>
此功能为用户下单时给自己发邮件提醒。<br/>使用普通模式发信时，建议使用QQ邮箱，SMTP服务器smtp.qq.com，端口465，密码不是QQ密码也不是邮箱独立密码，是QQ邮箱设置界面生成的<a href="http://service.mail.qq.com/cgi-bin/help?subtype=1&&no=1001256&&id=28"  target="_blank" rel="noreferrer">授权码</a>。为确保发信成功率，发信邮箱和收信邮箱最好同一个
</div>
</div>
<script>
$("select[name=\'mail_cloud\']").change(function(){
    if($(this).val() == 0){
        $("#frame_set1").css("display","inherit");
        $("#frame_set2").css("display","none");
        $("#frame_set3").css("display","none");
    }
    else if($(this).val() == 2){
        $("#frame_set1").css("display","none");
        $("#frame_set2").css("display","none");
        $("#frame_set3").css("display","inherit");
    }
    else{
        $("#frame_set1").css("display","none");
        $("#frame_set2").css("display","inherit");
        $("#frame_set3").css("display","none");
    }

    $("#tips_name").hide();

    if($(this).val() == 1){
        $("#tips").html(\'搜狐Sendcloud注册地址：<a href="https://sendcloud.sohu.com/signup.html?auth=1#/">点我去注册</a>\').show();
    }
    else if($(this).val() == 2){
        $("#tips").html(\'需要先登录阿里云控制台申请好【用户AccessKey】，并进入【邮件推送服务】开通，再配置好发信域名和地址，等待通过！<a href="https://usercenter.console.aliyun.com/#/manage/ak">点我去管理</a>&nbsp;<a href="https://dm.console.aliyun.com/?spm=5176.2020520153.nav-right.51.43b5415d6HESzI#/directmail/Domain/cn-hangzhou">点我去开通</a>\').show();
        $("#tips_name").html(\'此处必须填写阿里云配置好的发信地址，是域名类型！例如********************\').show();
    }
    else{
        $("#tips").hide();
    }
});

</script>
';
            } else {
                if ($mod == 'gonggao_n' && $_POST['do'] == 'submit') {
                    if (!$_POST['submit']) {
                        showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
                    } else {
                        foreach ($_POST as $key => $value) {
                            if ($key != 'submit') {
                                saveSetting($key, $value);
                            }
                        }
                    }
                    $ad = $CACHE->clear();
                    if ($ad) {
                        showmsg('修改成功！', 1);
                    } else {
                        showmsg('修改失败！<br/>' . $DB->error(), 4);
                    }
                } elseif ($mod == 'gonggao') {
                    echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">网站公告配置</h3></div>
<div class="">
  <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form">
  <input type="hidden" name="do" value="submit"/>
  <input type="hidden" name="act" value="gonggao"/>
  <input type="hidden" name="action" value="网站公告"/>
    <div class="form-group">
      <label class="col-sm-2 control-label">首页公告</label>
      <div class="col-sm-10"><textarea class="form-control" name="anounce" rows="6">';
                    echo htmlspecialchars($conf['zz_anounce']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">首页弹出公告</label>
      <div class="col-sm-10"><textarea class="form-control" name="modal" rows="5">';
                    echo htmlspecialchars($conf['zz_modal']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">首页弹出类型</label>
      <div class="col-sm-10">
      <select name="modal_type" class="form-control" default="';
                    echo $conf['zz_modal_type'] > 0 ? '1' : '0';
                    echo '"><option value="0">一天一次（默认）</option><option value="1">始终弹出</option>
      </select>
      </div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">首页底部排版</label>
      <div class="col-sm-10"><textarea class="form-control" name="bottom" rows="5">';
                    echo htmlspecialchars($conf['zz_bottom']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">在线下单提示</label>
      <div class="col-sm-10"><textarea class="form-control" name="alert" rows="5">';
                    echo htmlspecialchars($conf['zz_alert']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">用户/分站充值提示</label>
      <div class="col-sm-10"><textarea class="form-control" name="fenzhan_chongzhi_alert" rows="5">';
                    echo htmlspecialchars($conf['fenzhan_chongzhi_alert']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">订单查询页面公告</label>
      <div class="col-sm-10"><textarea class="form-control" name="gg_search" rows="5">';
                    echo htmlspecialchars($conf['zz_gg_search']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">分站后台公告</label>
      <div class="col-sm-10"><textarea class="form-control" name="gg_panel" rows="5">';
                    echo htmlspecialchars($conf['zz_gg_panel']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">用户后台公告</label>
      <div class="col-sm-10"><textarea class="form-control" name="gg_user" rows="5">';
                    echo htmlspecialchars($conf['zz_gg_user']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">分站开通页面公告</label>
      <div class="col-sm-10"><textarea class="form-control" name="gg_reguser" rows="5">';
                    echo htmlspecialchars($conf['zz_gg_reguser']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group hide">
      <label class="col-sm-2 control-label">首页聊天代码</label>
      <div class="col-sm-10"><textarea class="form-control" name="chatframe" rows="5">';
                    echo htmlspecialchars($conf['zz_chatframe']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">APP启动弹出内容</label>
      <div class="col-sm-10"><textarea class="form-control" name="app_client_alert" rows="5">';
                    echo htmlspecialchars($conf['zz_app_client_alert']);
                    echo '</textarea></div>
    </div><br/>
        <div class="form-group">
      <label class="col-sm-2 control-label">分站后台弹出公告</label>
      <div class="col-sm-10"><textarea class="form-control" name="modalht" rows="5">';
                    echo htmlspecialchars($conf['zz_modalht']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">首页商品推荐</label>
      <div class="col-sm-10"><textarea class="form-control" name="sptz" rows="4">';
                    echo htmlspecialchars($conf['zz_sptz']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">首页新手教程</label>
      <div class="col-sm-10"><textarea class="form-control" name="ylzzjc" rows="4">';
                    echo htmlspecialchars($conf['zz_ylzzjc']);
                    echo '</textarea></div>
    </div><br/>
    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
     </div>
    </div>
  </form>
</div>
<div class="panel-footer">
<span class="glyphicon glyphicon-info-sign"></span>
实用工具：<a href="set.php?mod=copygg">一键复制其他站点排版</a>｜<a href="http://www.w3school.com.cn/tiy/t.asp?f=html_basic" target="_blank" rel="noreferrer">HTML在线测试</a>｜<a href="http://pic.xiaojianjian.net/" target="_blank" rel="noreferrer">图床</a>｜<a href="https://mp3.ltyuanfang.cn/" target="_blank" rel="noreferrer">音乐外链</a><br/>
聊天代码获取地址：<a href="http://www.uyan.cc/getcode/mobile" target="_blank" rel="noreferrer">友言</a>、<a href="http://changyan.kuaizhan.com/" target="_blank" rel="noreferrer">搜狐畅言</a>
</div>
</div>
';
                } else {
                    if ($mod == 'copygg_n' && $_POST['do'] == 'submit') {
                        $url     = $_POST['url'];
                        $content = $_POST['content'];
                        $url_arr = parse_url($url);
                        if ($url_arr['host'] == $_SERVER['HTTP_HOST']) {
                            showmsg('无法自己复制自己', 3);
                        }
                        $data = get_curl($url . 'api.php?act=siteinfo');
                        $arr  = json_decode($data, true);
                        if (array_key_exists('sitename', $arr)) {
                            if (in_array('anounce', $content)) {
                                saveSetting('anounce', str_replace($arr['kfqq'], $conf['zz_kfqq'], $arr['anounce']));
                            }
                            if (in_array('modal', $content)) {
                                saveSetting('modal', str_replace($arr['kfqq'], $conf['zz_kfqq'], $arr['modal']));
                            }
                            if (in_array('bottom', $content)) {
                                saveSetting('bottom', str_replace($arr['kfqq'], $conf['zz_kfqq'], $arr['bottom']));
                            }
                            $ad = $CACHE->clear();
                            if ($ad) {
                                showmsg('修改成功！', 1);
                            } else {
                                showmsg('修改失败！<br/>' . $DB->error(), 4);
                            }
                        } else {
                            showmsg('获取数据失败，对方网站无法连接或非彩虹系统。', 4);
                        }
                    } elseif ($mod == 'copygg') {
                        echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">一键复制其他站点排版</h3></div>
<div class="">
  <form action="./set.php?mod=copygg_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
    <div class="form-group">
      <label class="col-sm-2 control-label">站点URL</label>
      <div class="col-sm-10"><input type="text" name="url" value="" class="form-control" placeholder="http://www.qq.com/" required/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">复制内容</label>
      <div class="col-sm-10"><label><input name="content[]" type="checkbox" value="anounce" checked/> 首页公告</label><br/><label><input name="content[]" type="checkbox" value="modal" checked/> 弹出公告</label><br/><label><input name="content[]" type="checkbox" value="bottom" checked/> 底部排版</label></div>
    </div>
    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
     </div>
    </div>
  </form>
</div>
</div>
';
                    } elseif ($mod == "pay_n" && $_POST["do"] == "submit") {
                        if (file_exists("pay.lock") && !empty($conf['zz_epay_pid']) && is_numeric($conf["epay_pid"]) && strlen($conf["epay_key"]) > 20 && ($_POST["epay_pid"] != $conf["epay_pid"] || $_POST["epay_key"] != $conf["epay_key"])) {
                            $admin_path = getNowDirName();
                            showmsg("为保障您的资金安全，如需修改支付商户和密钥，请删除<font color=red> " . $admin_path . "/pay.lock </font>文件后再修改！", 3);
                        }
                        if ($_POST["epay_url"] == "http://52yunpay.cn/" || $_POST["epay_url"] == "http://www.52yunpay.cn/" || $_POST["epay_url"] == "http://pay.qxu8.top/") {
                            showmsg("该支付接口存在于对接黑名单中，为了您的资金安全，请勿对接！", 3);
                        }

                        if (!$_POST['submit']) {
                            showmsg('请通过页面保存提交数据！<br/>' . $DB->error(), 4);
                        } else {
                            foreach ($_POST as $key => $value) {
                                if ($key != 'submit') {
                                    saveSetting($key, $value);
                                }
                            }
                        }

                        $ad = $CACHE->clear();
                        if ($ad) {
                            showmsg('修改成功！', 1);
                        } else {
                            showmsg('修改失败！<br/>' . $DB->error(), 4);
                        }
                    } elseif ($mod == 'pay') {
                        // $epayStr_footer .= '';

                        $epayStr = getAddPayList(1);

                        // $epayStr .= $epayStr_footer;

                        $codePayStr = getAddCodePayList();

                        // $server_url = getAuthServer();

                        // $epayStr = $epayStr_footer;

                        // $codePayStr = '';

                        $server_url = '';

                        echo '
                        <div class="block">
                          <div class="nav-tabs-alt">
                          <ul class="nav nav-tabs nav-justified" data-toggle="tabs">
                            <li class="active text-cmgg-xs">
                              <a href="#pay" data-toggle="tab">
                                基础支付配置
                              </a>
                            </li>
                            <li class="text-cmgg-xs">
                              <a href="./pay.channel.php" >
                                支付多接口
                              </a>
                            </li>
                             <li class="text-cmgg-xs">
                              <a href="#payMsg" data-toggle="tab">
                                支付限额设置
                              </a>
                            </li>
                          </ul>
                          <div class="modal-body">
                            <div id="myTabContent" class="tab-content">
                                <!--默认接口配置-->
                                <div class="tab-pane fade in active" id="pay">
                                        <form action="./set.php?mod=pay_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">支付宝即时到账</label>
                                        <div class="col-lg-8">
                                            <select class="form-control" name="alipay_api" default="';
                        echo $conf['zz_alipay_api'];
                        echo '"><option value="0">关闭</option><option value="1">支付宝官方即时到账接口</option><option value="3">支付宝当面付扫码支付</option><option value="2">易支付免签约接口</option><option value="5">码支付免签约接口</option></select>
                                            <pre id="payapi_other">选择不同接口后先点击最下方“修改”按钮后再刷新本页可填写接口信息等</pre>
                                        </div>
                                    </div>
                                    <div id="payapi_06" style="display:none;">
                                      <div class="form-group">
                                          <label class="col-lg-3 control-label">当面付应用ID</label>
                                          <div class="col-lg-8">
                                              <input type="text" name="alipay_app_id" class="form-control" value="';
                        echo $conf['zz_alipay_app_id'];
                        echo '">
                                          </div>
                                      </div>
                                      <div class="form-group">
                                          <label class="col-lg-3 control-label">当面付支付宝公钥</label>
                                          <div class="col-lg-8">
                                              <input type="text" name="alipay_public_key" class="form-control" value="';
                        echo $conf['zz_alipay_public_key'];
                        echo '">
                                          </div>
                                      </div>
                                      <div class="form-group">
                                          <label class="col-lg-3 control-label">当面付商户私钥</label>
                                          <div class="col-lg-8">
                                              <input type="text" name="alipay_private_key" class="form-control" value="';
                        echo $conf['zz_alipay_private_key'];
                        echo '">
                                          </div>
                                      </div>
                                    </div>
                                    <div id="payapi_01" style="';
                        if ($conf['zz_alipay_api'] != 1) {
                            echo 'display:none;';
                        }
                        echo '">
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">合作者身份(PID)</label>
                                        <div class="col-lg-8">
                                            <input type="text" name="alipay_pid" class="form-control" value="';
                        echo $conf['zz_alipay_pid'];
                        echo '">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">安全校验码(Key)</label>
                                        <div class="col-lg-8">
                                            <input type="text" name="alipay_key" class="form-control" value="';
                        echo $conf['zz_alipay_key'];
                        echo '">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">支付宝手机网站支付</label>
                                        <div class="col-lg-8">
                                            <select class="form-control" name="alipay2_api" default="';
                        echo $conf['zz_alipay2_api'];
                        echo '"><option value="0">关闭</option><option value="1">支付宝手机网站支付接口</option></select>
                                            <pre id="payapi_02"  style="';
                        if ($conf['zz_alipay2_api'] != 1) {
                            echo 'display:none;';
                        }
                        echo '">相关信息与以上支付宝即时到账接口一致，开启前请确保已开通支付宝手机支付，否则会导致手机用户无法支付！</pre>
                                        </div>
                                    </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">QQ钱包支付接口</label>
                                        <div class="col-lg-8">
                                            <select class="form-control" name="qqpay_api" default="';
                        echo $conf['zz_qqpay_api'];
                        echo '"><option value="0">关闭</option><option value="1">QQ钱包官方支付接口</option><option value="2">易支付免签约接口</option><option value="5">码支付免签约接口</option></select>
                                            <pre id="payapi_other">选择不同接口后先点击最下方“修改”按钮后再刷新本页可填写接口信息等</pre>
                                            <pre id="payapi_05"  style="';
                        if ($conf['zz_qqpay_api'] != 1) {
                            echo 'display:none;';
                        }
                        echo '"><font color="green">*QQ钱包支付接口配置请修改other/qqpay/qpayMch.config.php</font></pre>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">微信支付接口</label>
                                        <div class="col-lg-8">
                                            <select class="form-control" name="wxpay_api" default="';
                        echo $conf['zz_wxpay_api'];
                        echo '"><option value="0">关闭</option><option value="1">微信官方扫码+公众号支付接口</option><option value="3">微信官方扫码+H5支付接口</option><option value="2">易支付免签约接口</option><option value="5">码支付免签约接口</option></select>
                                            <pre id="payapi_other">选择不同接口后先点击最下方“修改”按钮后再刷新本页可填写接口信息等</pre>
                                            <pre id="payapi_04"  style="';
                        if ($conf['zz_wxpay_api'] != 1 && $conf['zz_wxpay_api'] != 3) {
                            echo 'display:none;';
                        }
                        echo '"><font color="green">*微信支付接口配置请修改other/wxpay/WxPay.Config.php</font></pre>
                                        </div>
                                    </div>
                                    ';
                        if ($conf['zz_alipay_api'] == 2 || $conf['zz_qqpay_api'] == 2 || $conf['zz_wxpay_api'] == 2) {
                            echo '  <div class="form-group">
                                        <label class="col-lg-3 control-label">易支付接入商</label>
                                        <div class="col-lg-8">
                                            <div class="input-group">
                                            <select class="form-control" name="payapi" id="payapi" onChange="setTips(this)" default="';
                            echo $conf['zz_payapi'];
                            echo '">';
                            echo $epayStr;
                            echo '
                                            </select>
                                            <a id="getEpayList" href="javascript:void(0);" class="input-group-addon">刷新接口</a>
                                            </div>
                                            <pre id="paytips"></pre>
                                            ';
                            echo '
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">回调是否启用https</label>
                                        <div class="col-lg-8">
                                            <select class="form-control" name="epay_https" default="';
                            echo $conf['epay_https'];
                            echo '"><option value="0">关闭（默认）</option><option value="1">开启</option></select>
                                            <pre style="colo:red">谨慎开启，误操作后果自负！支付回调域名被拦截？<a id="diycall">点我使用自定义回调地址</a></pre>
                                        </div>
                                    </div>
                                    <div class="form-group ';

                            echo '" id="payapi_07" style="';
                            if ($conf['zz_payapi'] != -1) {
                                echo 'display:none;';
                            }
                            echo '">
                                        <label class="col-lg-3 control-label">易支付接口网址</label>
                                        <div class="col-lg-8">
                                            <input type="text" name="epay_url" class="form-control"
                                                   value="';
                            echo $conf['zz_epay_url'];
                            echo '" placeholder="http://pay.chenmpay.com/">
                                            <pre>自定义接口有利益损失风险，请谨慎选择哦~</pre>
                                        </div>
                                    </div>
                                    <div class="form-group" id="display_diycall" style="display:none">
                                        <label class="col-lg-3 control-label">自定义回调网址</label>
                                        <div class="col-lg-8">
                                            <input type="text" name="epay_callback_url" placeholder="完整域名格式：http://www.testds.cn/" class="form-control"
                                                   value="';
                            echo $conf['zz_epay_callback_url'];
                            echo '">    <pre>可留空！<span style="color:red">填当前云商城站点绑定的网址，不是填易支付地址不要瞎填！</span>该功能用于有些域名被墙时导致支付后回调打不开问题</pre>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">易支付商户ID</label>
                                        <div class="col-lg-8">
                                            <input type="text" name="epay_pid" class="form-control"
                                                   value="';
                            echo $conf['zz_epay_pid'];
                            echo '">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">易支付商户密钥</label>
                                        <div class="col-lg-8">
                                            <input type="text" name="epay_key" class="form-control" value="';
                            echo $conf['zz_epay_key'];
                            echo '">
                                        </div>
                                    </div>
                                    ';
                        }
                        if ($conf['alipay_api'] == 5 || $conf['qqpay_api'] == 5 || $conf['wxpay_api'] == 5) {

                            echo '  <div class="form-group">
                                        <label class="col-lg-3 control-label">码支付接入商</label>
                                        <div class="col-lg-8">
                                            <div class="input-group">
                                            <select class="form-control" id="codepayapi" name="codepayapi" onChange="setCodeTips(this)" default="';
                            echo $conf['zz_codepayapi'];
                            echo '">';
                            echo $codePayStr;
                            echo '<option value="99999">其他接口看下方说明</option>';
                            echo '
                                    </select>
                                    <a id="getCodepayList" href="javascript:void(0);" class="input-group-addon">刷新接口列表</a>
                                    </div>
                                    <pre id="codepaytips" style="display:none"></pre>
                                    </div>
                                 </div>
                            ';
                            echo '
                             <div class="form-group">
                                        <label class="col-lg-3 control-label">码支付接入模式</label>
                                        <div class="col-lg-8">
                                            <select class="form-control" name="codepay_type" default="';
                            echo $conf['zz_codepay_type'] != "" ? $conf['zz_codepay_type'] : '0';
                            echo '">';
                            echo '<option value="1">易支付模式(可用时推荐)</option>';
                            echo '<option value="0">默认码支付模式</option>';
                            echo '<option value="2">新版安全模式(仅部分新码支付支持)</option>';
                            echo '
                                </select>
                                <pre style="color:red">在选择【易支付模式】或【新版安全模式】时，请先咨询你对接的码支付平台是否支持该模式</pre>
                            ';
                            echo '
                                        </div>
                                    </div>
                                    <script type="text/javascript">
                                    function setCodeTips(el) {
                                        var content = $("select[name=\'codepayapi\'] option:selected").attr("content");
                                        if("string" === typeof content && content!=""){
                                            content = unescape(decodeURI(content)).replace(new RegExp("\\\+")," ");
                                            $("#codepaytips").html(content).show();
                                        }
                                        else{
                                            if (el.value=="40001") {
                                                $("#codepaytips").html(\'官方认证|三网免挂，支付官网：<a href="http://pay.42izf.cn/" target="_blank">http://pay.42izf.cn/</a>\').show();
                                            }
                                            else {
                                                $("#codepaytips").html(\'\').hide();
                                                //$("#paytips").html(\'官方已认证，请放心使用！\').show();
                                            }
                                        }
                                    }
                                    $(document).ready(function($) {
                                        $(document).on("click", "#getCodepayList", function(){
                                             var ii = layer.load(2, {
                                                shade: [0.1, "#fff"]
                                            });
                                            $.ajax({
                                                url: "?mod=codepay_update",
                                                type: "GET",
                                                dataType: "json",
                                                cache: false,
                                                success: function(data) {
                                                    layer.close(ii);
                                                    if (data.code == 0) {
                                                        $("select[name=\'codepayapi\']").empty();
                                                        $("select[name=\'codepayapi\']").append(data.data.selects);
                                                        if(!!$("select[name=\'codepayapi\']").attr("default")){
                                                             $("select[name=\'codepayapi\']").val($("select[name=\'codepayapi\']").attr("default"));
                                                        }
                                                    } else {
                                                        layer.alert(data.msg);
                                                    }
                                                },
                                                error: function(data) {
                                                    layer.close(ii);
                                                    layer.msg("刷新易支付接口列表失败，请刷新页面后再试！");
                                                    return false;
                                                }
                                            });
                                        });
                                        $("select[name=\'codepayapi\']").change();
                                    });
                                    </script>';
                            echo '  <div class="form-group">
                                        <label class="col-lg-3 control-label">码支付ID</label>
                                        <div class="col-lg-8">
                                        <input type="text" name="codepay_id" class="form-control"
                                        value="';
                            echo $conf['codepay_id'];
                            echo '">
                                        <samll>码支付接入模式为【易支付模式】时，该对接平台未特殊说明下，ID、密钥是和默认码支付模式一样的</samll>
                                        </div>

                                        </div>
                                        <div class="form-group">
                                        <label class="col-lg-3 control-label">码支付通信密钥</label>
                                        <div class="col-lg-8">
                                        <input type="text" name="codepay_key" class="form-control" value="';
                            echo $conf['codepay_key'];
                            echo '">
                                        <pre><font color="green">普通码支付的接口地址配置在other/codepay/codepay_config.php，码支付支付宝和QQ需要挂电脑软件，微信不需要挂软件</font></pre>
                                        </div>
                                        </div>
                                        ';
                        }
                        echo '  <div class="form-group">
                                      <div class="col-lg-offset-3 col-lg-8 col-sm-12"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
                                     </div>

                                    </div>
                                    <div class="form-group">
                                       <div class="col-lg-offset-3 col-lg-8 col-sm-12">
                                      <a href="set.php?mod=epay">进入易支付结算设置及订单查询页面</a>
                                      </div>
                                    </div>
                                          </form>
                                    </div>
                                    <script type="text/javascript">
                                    function setTips(el) {
                                        var content = $("select[name=\'payapi\'] option:selected").attr("content");
                                        var v = $(el).val();
                                        if("string" === typeof content && content!=""){
                                            content = unescape(decodeURI(content)).replace(new RegExp("\\\+")," ");
                                            $("#paytips").html(content).show();
                                        }
                                        else{
                                            if (v=="0") {
                                                $("#paytips").html(\'官方运营|自助申请地址：<a href="http://pay.chenmds.cn/user/reg.php" target="_blank">http://pay.chenmds.cn/</a>\').show();
                                            }
                                            else if (v=="10") {
                                                $("#paytips").html(\'官方认证|自助申请地址：<a href="https://pay.76cm.cn/" target="_blank">https://pay.76cm.cn/</a>\').show();
                                            }
                                            else if (v== -1) {
                                                $("#payapi_07").show();
                                                $("#paytips").html(\'使用第三方支付时请注意资金安全\').show();
                                            }
                                            else {
                                                $("#paytips").html(\'\').hide();
                                                //$("#paytips").html(\'官方已认证，请放心使用！\').show();
                                            }
                                        }
                                    }

                                    $(document).on("click", "#getEpayList", function(){
                                         var ii = layer.load(2, {
                                            shade: [0.1, "#fff"]
                                        });
                                        $.ajax({
                                            url: "?mod=epay_update",
                                            type: "GET",
                                            dataType: "json",
                                            cache: false,
                                            success: function(data) {
                                                layer.close(ii);
                                                if (data.code == 0) {
                                                    $("#payapi").empty();
                                                    $("#payapi").append(data.data.selects);
                                                    if(!!$("#payapi").attr("default")){
                                                         $("#payapi").val($("#payapi").attr("default"));
                                                    }
                                                } else {
                                                    layer.alert(data.msg);
                                                }
                                            },
                                            error: function(data) {
                                                layer.close(ii);
                                                layer.msg("刷新易支付接口列表失败，请刷新页面后再试！");
                                                return false;
                                            }
                                        });
                                    });

                                    $("select[name=\'payapi\']").change();
                                    </script>

                                <!--默认接口配置 end-->

                                <!--多个接口配置-->
                                <div class="tab-pane fade in" id="pays">
                                      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
                                       <input type="hidden" name="action" value="多个支付对接"/>
                                      <div class="alert alert-info">
                                        <span style="color:red;">注意：本功能仅在【基础支付配置】中选择易支付时才有效<br>
                                        使用本功能可一定程度上避免损失！</span>
                                        </div><br/>
                                        <div class="form-group">
                                          <label class="col-sm-2 control-label">开启多个独立对接</label>
                                          <div class="col-sm-10"><select class="form-control" name="epays_open" default="';
                        echo $conf['zz_epays_open'];
                        echo '"><option value="0">关闭</option><option value="1">开启</option></select></div>
                                        </div><br/>
                                        <div class="form-group">
                                          <label class="col-sm-2 control-label">QQ钱包易支付账号(qqpay_pid)</label>
                                          <div class="col-sm-10"><input type="text" name="qqpay_pid" value="';
                        echo $conf['zz_qqpay_pid'];
                        echo '" class="form-control" placeholder="QQ钱包支付账号"/>
                                        </div>
                                        </div><br/>
                                        <div class="form-group">
                                          <label class="col-sm-2 control-label">QQ钱包易支付密钥(qqpay_key)</label>
                                          <div class="col-sm-10"><input type="text" name="qqpay_key" value="';
                        echo $conf['zz_qqpay_key'];
                        echo '" class="form-control" placeholder="QQ钱包接口密码"/>
                                        </div>
                                        </div><br/>
                                        <div class="form-group">
                                          <label class="col-sm-2 control-label">QQ钱包易支付接口地址(qqpay_url)</label>
                                          <div class="col-sm-10"><input type="text" name="qqpay_url" value="';
                        echo $conf['zz_qqpay_url'];
                        echo '" class="form-control" placeholder="QQ钱包接口地址"/>
                                           <pre>请填写需要单独对接QQ钱包的完整易支付接口地址，此QQ钱包配置不完整时将使用默认接口</pre>
                                        </div>
                                        </div><br/>
                                        <div class="form-group">
                                          <label class="col-sm-2 control-label">支付宝易支付账号(alipay_pid)</label>
                                          <div class="col-sm-10"><input type="text" name="alipay_pid" value="';
                        echo $conf['zz_alipay_pid'];
                        echo '" class="form-control" placeholder="支付宝钱包接口账号"/>
                                        </div>
                                        </div><br/>
                                        <div class="form-group">
                                          <label class="col-sm-2 control-label">支付宝易支付密钥(alipay_key)</label>
                                          <div class="col-sm-10"><input type="text" name="alipay_key" value="';
                        echo $conf['zz_alipay_key'];
                        echo '" class="form-control" placeholder="支付宝接口密码"/>
                                        </div>
                                        </div><br/>
                                        <div class="form-group">
                                          <label class="col-sm-2 control-label">支付宝易支付地址(alipay_url)</label>
                                          <div class="col-sm-10"><input type="text" name="alipay_url" value="';
                        echo $conf['zz_alipay_url'];
                        echo '" class="form-control" placeholder="支付宝接口地址"/>
                                           <pre>请填写需要单独对接支付宝的完整易支付接口地址，此支付宝配置不完整时将使用默认接口</pre>
                                        </div>
                                        </div><br/>
                                        <div class="form-group">
                                          <label class="col-sm-2 control-label">微信易支付账号(wxpay_pid)</label>
                                          <div class="col-sm-10"><input type="text" name="wxpay_pid" value="';
                        echo $conf['zz_wxpay_pid'];
                        echo '" class="form-control" placeholder="微信接口账号"/>
                                        </div>
                                        </div><br/>
                                        <div class="form-group">
                                          <label class="col-sm-2 control-label">微信易支付密钥(wxpay_key)</label>
                                          <div class="col-sm-10"><input type="text" name="wxpay_key" value="';
                        echo $conf['zz_wxpay_key'];
                        echo '" class="form-control" placeholder="微信接口密码"/>
                                        </div>
                                        </div><br/>
                                        <div class="form-group">
                                          <label class="col-sm-2 control-label">微信易支付地址(wxpay_url)</label>
                                          <div class="col-sm-10"><input type="text" name="wxpay_url" value="';
                        echo $conf['zz_wxpay_url'];
                        echo '" class="form-control" placeholder="微信接口地址"/>
                                           <pre>请填写需要单独对接微信支付的完整易支付接口地址，此微信配置不完整时将使用默认接口</pre>
                                            </div>
                                            </div><br/>
                                            <div class="form-group">
                                              <div class="col-sm-12"><input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/><br/>
                                             </div>
                                            </div>
                                          </form>
                                    </div>
                                    <!--多个接口配置 end-->
                                    <!--支付其他设置-->
                                    <div class="tab-pane fade in" id="payMsg">
                                      <form action="./set.php?mod=save_n" method="post" class="form-horizontal" role="form"><input type="hidden" name="do" value="submit"/>
                                       <input type="hidden" name="action" value="支付其他设置"/>
                                       <div class="alert alert-info">限制说明：游客和代理的订单都会生效，当订单金额大于0且不满足设置要求的就会无法下单并给出提示！金额单位：元。</div>
                                            <div class="form-group">
                                              <label class="col-sm-2 control-label">支付方式页面提示</label>
                                              <div class="col-sm-10"><textarea class="form-control" name="pay_alert" rows="5">';
                        echo htmlspecialchars($conf['zz_pay_alert']);
                        echo '</textarea>
                                            <small>下单时支付方式页面的自定义提示内容，支持html代码</small>
                                            </div>
                                            </div><br/>
                                            <div class="form-group">
                                                  <label class="col-sm-2 control-label">QQ钱包限制开关</label>
                                                  <div class="col-sm-10">
                                                  <select class="form-control" name="qqpay_limit_open" default="';
                        echo $conf['qqpay_limit_open'] > 0 ? '1' : '0';
                        echo '"><option value="0" >关闭</option><option value="1">开启</option></select>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                  <label class="col-sm-2 control-label">QQ钱包最低限制金额</label>
                                                  <div class="col-sm-10">
                                                  <input class="form-control" name="qqpay_limit" value="';
                        echo $conf['qqpay_limit'];
                        echo '"/>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                  <label class="col-sm-2 control-label">支付宝限制开关</label>
                                                  <div class="col-sm-10">
                                                  <select class="form-control" name="alipay_limit_open" default="';
                        echo $conf['alipay_limit_open'] > 0 ? '1' : '0';
                        echo '"><option value="0" >关闭</option><option value="1">开启</option></select>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                  <label class="col-sm-2 control-label">支付宝最低限制金额</label>
                                                  <div class="col-sm-10">
                                                  <input class="form-control" name="alipay_limit" value="';
                        echo $conf['alipay_limit'];
                        echo '"/>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                  <label class="col-sm-2 control-label">微信限制开关</label>
                                                  <div class="col-sm-10">
                                                  <select class="form-control" name="wxpay_limit_open" default="';
                        echo $conf['wxpay_limit_open'] > 0 ? '1' : '0';
                        echo '"><option value="0" >关闭</option><option value="1">开启</option></select>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                  <label class="col-sm-2 control-label">微信最低限制金额</label>
                                                  <div class="col-sm-10">
                                                  <input class="form-control" name="wxpay_limit" value="';
                        echo $conf['wxpay_limit'];
                        echo '"/>
                                                </div>
                                            </div>
                                             <div class="form-group">
                                              <div class="col-sm-12">
                                              <input type="submit" name="submit" value="修改" class="btn btn-primary form-control"/>
                                              <br/>
                                             </div>
                                       </form>
                                    </div>
                                    <!--支付其他设置 end-->
                                </div>
                              </div>
                          </div>
                          </div>
                        <script>
                        $("select[name=\'alipay_api\']").change(function(){
                            if($(this).val() == 1){
                                $("#payapi_01").css("display","inherit");
                                $("#payapi_06").css("display","none");
                            }else if($(this).val() == 3){
                                $("#payapi_01").css("display","none");
                                $("#payapi_06").css("display","inherit");
                            }else{
                                $("#payapi_01").css("display","none");
                                $("#payapi_06").css("display","none");
                            }
                        });
                        $("select[name=\'tenpay_api\']").change(function(){
                            if($(this).val() == 1){
                                $("#payapi_03").css("display","inherit");
                            }else{
                                $("#payapi_03").css("display","none");
                            }
                        });
                        $("select[name=\'wxpay_api\']").change(function(){
                            if($(this).val() == 1 || $(this).val() == 3){
                                $("#payapi_04").css("display","inherit");
                            }else{
                                $("#payapi_04").css("display","none");
                            }
                        });
                        $("select[name=\'qqpay_api\']").change(function(){
                            if($(this).val() == 1){
                                $("#payapi_05").css("display","inherit");
                            }else{
                                $("#payapi_05").css("display","none");
                            }
                        });
                        $("select[name=\'alipay2_api\']").change(function(){
                            if($(this).val() == 1){
                                $("#payapi_02").css("display","inherit");
                            }else{
                                $("#payapi_02").css("display","none");
                            }
                        });
                        $("select[name=\'payapi\']").change(function(){
                            var api = $(this).val();
                            console.log("api", api);
                            if(api == -1){
                                $("#payapi_07").css("display","inherit");
                            }else{
                                $("#payapi_07").css("display","none");
                            }
                        });
                        $(document).on("click", "#diycall", function(){
                            $("#display_diycall").show();
                        });
                        </script>
                        ';
                    } elseif ($mod == 'epay_n') {
                        $payapi = payApi(true);
                        if ($conf['zz_payapi'] == -1 && $payapi == false) {
                            showmsg('风险提示：为保障您的资金安全，请勿使用非官方认证的易支付接口！', 4);
                        }
                        $account  = $_POST['account'];
                        $username = $_POST['username'];
                        if ($account == null || $username == null) {
                            showmsg('保存错误,请确保每项都不为空!', 3);
                        } else {
                            $data = get_curl($payapi . 'api.php?act=change&pid=' . $conf['zz_epay_pid'] . '&key=' . $conf['zz_epay_key'] . '&account=' . $account . '&username=' . $username . '&url=' . $_SERVER['HTTP_HOST']);
                            $arr  = json_decode($data, true);
                            if ($arr['code'] == 1) {
                                showmsg('修改成功!', 1);
                            } else {
                                showmsg($arr['msg']);
                            }
                        }
                    } elseif ($mod == 'epay') {
                        if (!empty($conf['zz_epay_pid']) && !empty($conf['zz_epay_key'])) {
                            $payapi = payApi();
                            /*if ($conf['zz_payapi'] == -1 && $payapi == false) {
                            showmsg('风险提示：为保障您的资金安全，请勿使用非官方认证的易支付接口！', 4);
                            }*/
                            $data = get_curl($payapi . 'api.php?act=query&pid=' . $conf['zz_epay_pid'] . '&key=' . $conf['zz_epay_key'] . '&url=' . $_SERVER['HTTP_HOST']);
                            $arr  = json_decode($data, true);
                            if ($arr['code'] == -2) {
                                showmsg('易支付KEY校验失败！');
                            } elseif (!array_key_exists('code', $arr)) {
                                showmsg('获取失败，请刷新重试！');
                            }
                            $stype = $arr['stype'] ? $arr['stype'] : '支付宝';
                        } else {
                            showmsg('您还未填写易支付商户ID和密钥，请返回填写！');
                        }

                        if (array_key_exists('active', $arr) && $arr['active'] == 0) {
                            showmsg('该商户已被封禁');
                        }

                        $key = substr($arr['key'], 0, 8) . '****************' . substr($arr['key'], 24, 32);
                        if (!file_exists('pay.lock')) {
                            file_put_contents('pay.lock', 'pay.lock');
                            error_reporting(0);
                        }

                        echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">易支付设置</h3></div>
<div class="">
<ul class="nav nav-tabs"><li class="active"><a href="#">易支付设置</a></li><li><a href="./set.php?mod=epay_order">订单记录</a></li><li><a href="./set.php?mod=epay_settle">结算记录</a></li></ul>
  <form action="./set.php?mod=epay_n" method="post" class="form-horizontal" role="form">
    <h4>商户信息查看：</h4>
    <div class="form-group">
      <label class="col-sm-2 control-label">商户ID</label>
      <div class="col-sm-10"><input type="text" name="pid" value="';
                        echo $arr['pid'];
                        echo '" class="form-control" disabled/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">商户KEY</label>
      <div class="col-sm-10"><input type="text" name="key" value="';
                        echo $key;
                        echo '" class="form-control" disabled/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">商户余额</label>
      <div class="col-sm-10"><input type="text" name="money" value="';
                        echo $arr['money'];
                        echo '" class="form-control" disabled/></div>
    </div><br/>
    <h4>收款账号设置：</h4>
    <div class="form-group">
      <label class="col-sm-2 control-label">结算方式</label>
      <div class="col-sm-10"><input type="text" name="type" value="';
                        echo $stype;
                        echo '" class="form-control" disabled/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">结算账号</label>
      <div class="col-sm-10"><input type="text" name="account" value="';
                        echo $arr['account'];
                        echo '" class="form-control"/></div>
    </div><br/>
    <div class="form-group">
      <label class="col-sm-2 control-label">真实姓名</label>
      <div class="col-sm-10"><input type="text" name="username" value="';
                        echo $arr['username'];
                        echo '" class="form-control"/></div>
    </div><br/>
    <div class="form-group">
      <div class="col-sm-offset-2 col-sm-10"><input type="submit" name="submit" value="确定修改" class="btn btn-primary form-control"/><br/>
     </div>
     </div>
     <h4><span class="glyphicon glyphicon-info-sign"></span> 注意事项</h4>
1.结算账号和真实姓名请仔细核对，一旦错误将无法结算到账！<br/>2.每笔交易会有';
                        echo 100 - $arr['money_rate'];
                        echo '%的手续费，这个手续费是支付宝、微信和财付通收取的，非本接口收取。<br/>3.结算为T+1规则，当天满';
                        echo $arr['settle_money'];
                        echo '元在第二天会自动结算
  </form>
</div>
</div>
';
                    } elseif ($mod == 'epay_settle') {
                        $payapi = payApi(true);
                        if ($conf['zz_payapi'] == -1 && $payapi == false) {
                            showmsg('风险提示：为保障您的资金安全，请勿使用非官方认证的易支付接口！', 4);
                        }
                        $data = get_curl($payapi . 'api.php?act=settle&pid=' . $conf['zz_epay_pid'] . '&key=' . $conf['zz_epay_key'] . '&limit=20&url=' . $_SERVER['HTTP_HOST']);
                        $arr  = json_decode($data, true);
                        if ($arr['code'] == -2) {
                            showmsg('易支付KEY校验失败！');
                        }
                        echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">易支付结算记录</h3></div>
    <div class="table-responsive">
        <table class="table table-striped">
          <thead><tr><th>ID</th><th>结算账号</th><th>结算金额</th><th>手续费</th><th>结算时间</th></tr></thead>
          <tbody>';
                        foreach ($arr['data'] as $res) {
                            echo '<tr><td><b>' . $res['id'] . '</b></td><td>' . $res['account'] . '</td><td><b>' . $res['money'] . '</b></td><td><b>' . $res['fee'] . '</b></td><td>' . $res['time'] . '</td></tr>';
                        }
                        echo '</tbody>
        </table>
      </div>
      </div>';
                    } elseif ($mod == 'epay_order') {
                        $payapi = payApi(true);
                        if ($conf['zz_payapi'] == -1 && $payapi == false) {
                            showmsg('风险提示：为保障您的资金安全，请勿使用非官方认证的易支付接口！', 4);
                        }
                        $data = get_curl($payapi . 'api.php?act=orders&pid=' . $conf['zz_epay_pid'] . '&key=' . $conf['zz_epay_key'] . '&page=1&limit=50');
                        $arr  = json_decode($data, true);
                        if ($arr['code'] == -2) {
                            showmsg('易支付KEY校验失败！');
                        }
                        echo '<div class="block">
                        <div class="block-title"><h3 class="panel-title">易支付订单记录</h3></div>订单只展示最新前50条[<a href="set.php?mod=epay">返回</a>]
                        <div class="table-responsive">
                            <table class="table table-striped">
                              <thead><tr><th>交易号/商户订单号</th><th>付款方式</th><th>商品名称/金额</th><th>订单来源域名</th><th>创建时间/完成时间</th><th>状态</th></tr></thead>
                              <tbody>';
                        foreach ($arr['data'] as $key => $res) {
                            echo '<tr><td>' . $res['trade_no'] . '<br/>' . $res['out_trade_no'] . '</td><td>' . $res['type'] . '</td><td>' . $res['name'] . '<br/>￥ <b>' . $res['money'] . '</b></td><td>' . (isset($res['domain']) ? $res['domain'] : '无数据') . '</td><td>' . $res['addtime'] . '<br/>' . $res['endtime'] . '</td><td>' . ($res['status'] == 1 ? '<font color=green>已完成</font>' : '<font color=red>未完成</font>') . '</td></tr>';
                        }
                        echo '</tbody>
                        </table>
                      </div>
                      </div>';
                    } elseif ($mod == 'logo') {
                        echo '<div class="block">
                        <div class="block-title"><h3 class="panel-title">更改首页LOGO&nbsp;<a class="label label-info pull-right" href="set.php?mod=upbgimg">更改背景图</a></h3></div><div class="panel-body">';
                        if ($_POST['s'] == 1 && stripos($_SERVER['HTTP_REFERER'], 'logo') !== false) {
                            $extension = explode('.', $_FILES['logofile']['name']);
                            $length    = count($extension);
                            if ($length > 1) {
                                $ext = strtolower($extension[$length - 1]);
                            }

                            $uploaded_size = $_FILES['logofile']['size'];
                            $uploaded_tmp  = $_FILES['logofile']['tmp_name'];
                            $uploaded_type = $_FILES['logofile']['type'];

                            if ($ext == 'png' || $ext == 'gif' || $ext == 'jpg' || $ext == 'jpeg') {
                                $oldurl = '';
                                if (stripos($conf['zz_logo'], 'http') !== false) {
                                    $oldurl = $conf['zz_logo'];
                                }
                                $scsk = uploadFile('logofile', '', 'syslogo', $oldurl);
                                if ($scsk['code'] == 0) {
                                    $logoimg         = $scsk['imgSrc'];
                                    $conf['zz_logo'] = $logoimg;
                                    saveSetting('logo', $logoimg);
                                    $ad = $CACHE->clear();
                                    if ($ad) {
                                        echo '成功上传文件!';
                                    } else {
                                        echo '文件上传成功，logo保存失败!';
                                    }
                                } else {
                                    echo '上传错误：' . $scsk;
                                }
                            } else {
                                echo '该文件格式不支持，请重新上传!';
                            }
                        }

                        if (stripos($conf['zz_logo'], 'http') === false) {
                            $logo = '/' . $conf['zz_logo'];
                            if (empty($logo)) {
                                $logo = '../assets/img/logo.png?r=' . rand(10000, 99999);
                            }
                        } else {
                            $logo = $conf['zz_logo'];
                        }

                        echo '<form action="set.php?mod=logo" method="POST" enctype="multipart/form-data"><label for="file"></label><input type="file" name="logofile" id="file" /><input type="hidden" name="s" value="1" /><br><input type="submit" class="btn btn-primary btn-block" value="确认上传" /></form><br>现在的图片：<br><img src="' . $logo . '" style="max-width:100%">';
                        echo '</div></div>';
                    } elseif ($mod == 'upbgimg') {
                        echo '<div class="block">
    <div class="block-title"><h3 class="panel-title">更改首页背景图&nbsp;<a class="label label-info pull-right" href="set.php?mod=logo">更改LOGO</a></h3></div><div class="panel-body">';
                        if ($_POST['s'] == 1) {
                            $extension = explode('.', $_FILES['file']['name']);
                            $length    = count($extension);
                            if ($length > 1) {

                                $ext = strtolower($extension[$length - 1]);
                            }

                            $uploaded_size = $_FILES['file']['size'];
                            $uploaded_tmp  = $_FILES['file']['tmp_name'];
                            $uploaded_type = $_FILES['file']['type'];

                            if ($ext == 'png' || $ext == 'gif' || $ext == 'jpg' || $ext == 'jpeg') {
                                $oldurl = '';
                                if (stripos($conf['ui_bing_img'], 'http') !== false) {
                                    $oldurl = $conf['ui_bing_img'];
                                }
                                $scsk = uploadFile('file', '', 'bgimg', $oldurl);
                                if ($scsk['code'] == 0) {
                                    $conf['ui_bing_img'] = $scsk['imgSrc'];
                                    saveSetting('ui_bing_img', $conf['ui_bing_img']);
                                    $ad = $CACHE->clear();
                                    if ($ad) {
                                        echo '成功上传文件!';
                                    } else {
                                        echo '文件上传成功，logo保存失败!';
                                    }
                                } else {
                                    echo '上传错误：' . $scsk['msg'];
                                }
                            } else {
                                echo '该文件不支持，请重新上传!';
                            }
                        }

                        if (stripos($conf['ui_bing_img'], 'http') === false) {
                            $bj = '/' . $conf['ui_bing_img'];
                            if (empty($bj)) {
                                $bj = '../assets/img/bj.png?r=' . rand(10, 999999);
                            }
                        } else {
                            $bj = $conf['ui_bing_img'];
                        }

                        echo '<form action="set.php?mod=upbgimg" method="POST" enctype="multipart/form-data"><label for="file"></label><input type="file" name="file" id="file" /><input type="hidden" name="s" value="1" /><br><input type="submit" class="btn btn-primary btn-block" value="确认上传" /></form><br>现在的图片：<br><img src="' . $bj . '" style="max-width:100%">';
                        echo '</div></div>';
                    } elseif ($mod == 'cleancache') {
                        $rs = $DB->query('SELECT * FROM pre_cache');
                        while ($res = $DB->fetch($rs)) {
                            $CACHE->clear($res['k']);
                        }
                        $_SESSION = [];
                        saveSetting('index_jsver', date('YmdHis'));
                        showmsg('清理系统设置缓存成功！', 1);
                    } else {
                        showmsg('该页面不存在，如确定有该功能可尝试将程序更新到最新版后再试！[' . $mod . ']', 3);
                    }
                }
            }
        }
    }
}

echo '
    </div>
  </div>';
include_once 'footer.php';
