<?php
if (!is_object($Menu)) {
    $Menu = new \core\Menu();
}
?>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8" />
  <title><?php echo $title ?>-神秘人商城Plus后台管理</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <link href="<?php echo $cdnpublic ?>twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet" />
  <link href="<?php echo $cdnpublic ?>font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" />
  <!-- <link href="<?php echo $cdnpublic; ?>jqueryui/1.12.1/jquery-ui.min.css" rel="stylesheet"/> -->
  <link rel="stylesheet" href="../assets/cmui/css/main.css?v=<?php echo $jsver ?>">
  <link id="theme-link" rel="stylesheet" href="<?php echo $_COOKIE['optionThemeColor'] ? $_COOKIE['optionThemeColor'] : '../assets/appui/css/themes/amethyst-2.4.css'; ?>">
  <script src="<?php echo $cdnpublic ?>jquery/1.12.4/jquery.min.js"></script>
  <script src="<?php echo $cdnpublic ?>twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
  <link rel="stylesheet" href="<?php echo $cdnserver ?>assets/user/css/animate.css" type="text/css" />
  <link rel="stylesheet" href="<?php echo $cdnserver ?>assets/user/css/app.css" type="text/css" />
  <link rel="stylesheet" href="<?php echo $cdnpublic ?>toastr.js/latest/toastr.css" type="text/css" />
  <script src="<?php echo $cdnserver ?>assets/appui/js/plugins.js"></script>
  <script src="<?php echo $cdnserver ?>assets/user/js/app.js"></script>
  <script src="<?php echo $cdnpublic ?>layer/3.5.1/layer.js?<?php echo $jsver ?>"></script>
  <script src="<?php echo $cdnserver ?>assets/public/tooltip/tooltip.js?<?php echo $jsver ?>"></script>
  <script>
    layer.config({
        anim: 4, //默认动画风格
        // skin: 'layui-layer-lan',
        area: ['auto', 'auto'],
        maxWidth: 420,
        maxHeight : 570,
    })
  </script>
  <!--[if lt IE 9]>
    <script src="<?php echo $cdnpublic ?>html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="<?php echo $cdnpublic ?>respond.js/1.4.2/respond.min.js"></script>
  <![endif]-->
  <style type="text/css">
    <?php if (checkmobile()): ?>body {
      overflow: auto;
    }

    /* html,body{-webkit-overflow-scrolling: touch;overflow-scrolling: touch;overflow: auto;}.block{overflow: scroll; height: 100%;} */
    <?php endif?>
    #aside {
        position: fixed;
        top: 50px;
        left: 0;
        bottom: 0;
        overflow: auto;
        z-index: 99999999999;
    }

    .alert a {
      color: #3156ed;
    }

    .alert a:hover {
      color: #0333f5;
    }

    .nav-icon {
      width: 15px;
      margin-left: 5px;
    }

    .a-blur{
        color: #43a3d9 !important;
    }

    .a-blur:hover,
    .a-blur:active,
    .a-blur:focus{
        color: #398bb9 !important;
    }

    .block .navbar-fixed-bottom {
        bottom: -15px;
        margin-bottom: 0;
        border-width: 1px 0 0;
    }

    .paging-navbar{
        width: 100%;
        margin-left: 220px;
        display:block;
        background-color: #fff;
        border-color: 1px 2px #cfdadd;
        min-height: 40px;
        padding: 10px 4px;
    }

    ul.pagination{
        margin-bottom: 25px;
    }

    @media screen and (max-width: 768px) {
        .paging-navbar{
            margin-left: 10px;
            min-height: 75px;
            padding-right: 10px;
        }

        ul.pagination{
            margin-bottom: 35px;
        }
    }

    .btn-primary,
    .btn-info,
    .btn-warning,
    .btn-danger{
        color: #fff;
    }

    .btn-primary a,
    .btn-info a,
    .btn-warning a,
    .btn-danger a{
        color: #fff;
    }

    .btn-primary span,
    .btn-info span,
    .btn-warning span,
    .btn-danger span{
        color: #fff;
    }

    .btn-primary button,
    .btn-info button,
    .btn-warning button,
    .btn-danger button{
        color: #fff;
    }

    .text-break{
        word-break: break-all !important;
        overflow-wrap: break-word !important;
        white-space: normal !important;
    }
  </style>
  <script type="text/javascript">
    var ie_version = (function() {
      var userAgent = navigator.userAgent,
        isLessIE11 = userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1,
        isEdge = userAgent.indexOf('Edge') > -1 && !isLessIE11,
        isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf('rv:11.0') > -1;
      if (isLessIE11) {
        var IEReg = new RegExp('MSIE (\\d+\\.\\d+);');
        IEReg.test(userAgent);
        var IEVersionNum = parseFloat(RegExp['$1']);
        if (IEVersionNum === 7) { // IE7
          return 7
        } else if (IEVersionNum === 8) { // IE8
          return 8
        } else if (IEVersionNum === 9) { // IE9
          return 9
        } else if (IEVersionNum === 10) { // IE10
          return 10
        } else {
          return 6
        }
      } else if (isEdge) { // edge
        return 'edge'
      } else if (isIE11) { // IE11
        return 11
      } else { // 不是ie浏览器
        return -1
      }
    }());
    if (ie_version != -1 && ie_version < 10 && ie_version != 'edge') {
      var title = document.createElement('div');
      title.setAttribute('class', 'content')
      title.setAttribute('style', 'height: 50px;position: absolute;top: 0;left: 0;right: 0;line-height: 50px;z-index: 9999999;background: rgba(0,0,0,.5);text-align: center;color: #ff922e;font-size: 19px;font-weight: 600;')
      title.innerHTML = '<span>当前浏览器IE版本较低，推荐使用QQ浏览器/谷歌浏览器/国产浏览器或使用极速模式访问</span><span class="compatibility_tips" style="position: absolute;right: 15px;top: 10px;font-size: 14px;display: inline-block; height: 30px;line-height: 28px;padding: 0 12px;font-weight: 500;color: #ffffff; border-radius: 4px;cursor: pointer;border: 2px solid #ffffff;font-weight: 500;">关闭提示</span>'
      title.querySelector('.compatibility_tips').addEventListener('click', function(res) {
        var parentNode = this.parentElement;
        parentNode.parentElement.removeChild(parentNode)
      })
      document.querySelector('html').appendChild(title)
    } else if (ie_version != -1 && (ie_version >= 10 || ie_version === 'edge')) {
      var title = document.createElement('div');
      title.setAttribute('class', 'content')
      title.setAttribute('style', 'height: 50px;position: absolute;top: 0;left: 0;right: 0;line-height: 50px;z-index: 9999999;background: rgba(0,0,0,.5);text-align: center;color: #ff922e;font-size: 19px;font-weight: 600;')
      title.innerHTML = '<span>当前浏览器可能存在视图不兼容的情况，推荐使用QQ浏览器/谷歌浏览器/国产浏览器极速模式访问</span><span class="compatibility_tips" style="position: absolute;right: 15px;top: 10px;font-size: 14px;display: inline-block; height: 30px;line-height: 28px;padding: 0 12px;font-weight: 500;color: #ffffff; border-radius: 4px;cursor: pointer;border: 2px solid #ffffff;font-weight: 500;">关闭提示</span>'
      title.querySelector('.compatibility_tips').addEventListener('click', function(res) {
        var parentNode = this.parentElement;
        parentNode.parentElement.removeChild(parentNode)
      })
      document.querySelector('html').appendChild(title)
    }
  </script>
</head>
<?php if ($isLogin == 1): ?>
  <div class="app app-header-fixed  ">
    <header id="header" class="app-header navbar ng-scope" role="menu">
      <div class="navbar-header <?php echo $ui_user[0] ?>">
        <button class="pull-right visible-xs" ui-toggle="off-screen" target=".app-aside" ui-scroll="app">
          <i class="glyphicon glyphicon-align-justify"></i>
        </button>
        <a href="./" class="navbar-brand text-lt">
          <i class="fa fa-desktop hidden-xs"></i>
          <span class="hidden-folded m-l-xs">系统管理中心</span>
        </a>
      </div>

      <div class="collapse pos-rlt navbar-collapse box-shadow <?php echo $ui_user[1] ?>">
        <!-- buttons -->
        <div class="nav navbar-nav hidden-xs">
          <a href="#" class="btn no-shadow navbar-btn" ui-toggle="app-aside-folded" target=".app">
            <i class="fa fa-dedent fa-fw text"> 菜单</i>
            <i class="fa fa-indent fa-fw text-active">菜单</i>
          </a>
        </div>
        <!-- / buttons -->

        <!-- nabar right -->
        <ul class="nav navbar-nav navbar-right">
          <li class="dropdown">
            <a href="#" data-toggle="dropdown" class="dropdown-toggle clear" data-toggle="dropdown">
              <span class="thumb-sm avatar pull-right m-t-n-sm m-b-n-sm m-l-sm">
                <img src="//q4.qlogo.cn/headimg_dl?dst_uin=<?php echo $head_qq ?>&spec=100">
                <i class="on md b-white bottom"></i>
              </span>
              <span class="hidden-sm hidden-md"><?php echo $head_user ?></span> <b class="caret"></b>
            </a>
            <!-- dropdown -->
            <ul class="dropdown-menu animated fadeInRight w">
              <li>
                <a href="./">
                  <span>用户中心</span>
                </a>
              </li>
              <li>
                <a href="./set.php?mod=user">
                  <span>修改资料</span>
                </a>
              </li>
              <li>
                <a href="./set.php?mod=cleancache">
                  <span>清除缓存</span>
                </a>
              </li>
              <li>
                <a href="./update.php">
                  <span>检测更新</span>
                </a>
              </li>
              <li>
                <a href="../">
                  <span>返回首页</span>
                </a>
              </li>
              <li class="divider"></li>
              <li>
                <a ui-sref="access.signin" href="login.php?logout">退出登录</a>
              </li>
            </ul>
            <!-- / dropdown -->
          </li>
        </ul>
        <!-- / navbar right -->
      </div>
      <!-- / navbar collapse -->
    </header>
    <!-- / header -->
    <!-- aside -->
    <aside id="aside" class="app-aside hidden-xs <?php echo $ui_user[2] ?>" style="">
      <div class="aside-wrap">
        <div class="navi-wrap">
          <a id="navIndex" class="hide">跳到当前页面菜单</a>
          <!-- nav -->
          <nav ui-nav class="navi">
            <?php echo $Menu->showList(); ?>
          </nav>
        </div>
      </div>
    </aside>
    <script type="text/javascript">
      $(document).ready(function() {
        setTimeout(function() {
            var scroll = false;
            var navs = $("aside#aside li[class='active']");
            for (var i = 0; i < navs.length; i++) {
            //console.log($(navs[i]));
                var navId = $(navs[i]).attr("id");
                if (i == navs.length - 1) {
                    try {
                            var offset = $(navs[i]).offset();
                            if (offset.top > $(window).height() - 150) {
                            $("#aside").animate({
                                scrollTop: $(navs[i]).offset().top - 400
                            }, 500);
                            }
                            scroll = true;
                    } catch (e) {
                            //如果scrollTop方法没效果，则用click方法
                            scroll = false;
                            $("a#navIndex").attr('href', '#' + navId);
                            $("a#navIndex")[0].click();
                    }
                }
            }
        }, 100);
      });
    </script>
    <div id="content" class="app-content" role="main">
      <div class="app-content-body ">
        <div class="bg-light lter b-b wrapper-sm ng-scope">
          <ul class="breadcrumb" style="padding: 0;margin: 0;">
            <li><i class="fa fa-home"></i><a href="/">前台首页</a></li>
            <li><a href="./">管理中心</a></li>
            <li><?php echo $title ?></li>
          </ul>
        </div>

      <?php endif;?>

      <?php
if (false !== ($ver = getIeVersion())) {
    if (floatval($ver) < 10) {
        showmsg("当前浏览器环境是IE兼容模式且版本过低，请切换到极速模式再访问", 3);
    }
}
?>