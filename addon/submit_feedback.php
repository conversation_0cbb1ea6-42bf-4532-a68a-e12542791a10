<?php

use core\Db;

include "../includes/common.php";
define('AJAX_VERSION', '2.8.3');
define('AJAX_BUILD', 1043);
$act = isset($_GET['act']) ? daddslashes($_GET['act']) : null;
@header('Content-Type: application/json; charset=UTF-8');


switch ($act) {

    case 'addlink':
    $pro_name = trim(input('post.pro_name', ''));
    $pro_id = trim(input('post.pro_id', ''));
    $error_type = intval(trim(input('post.content', '')));
    if (empty($pro_name) || empty($pro_id)) {
        echo json_encode(['code' => -1, 'msg' => '提交非法！'], JSON_UNESCAPED_UNICODE);
        $DB->close();exit();
    }
    if ($error_type === -1) {
        echo json_encode(['code' => -1, 'msg' => '请选择投稿类型'], JSON_UNESCAPED_UNICODE);
        $DB->close();exit();
    }
    if (!in_array($error_type, [0, 1, 2])) {
        echo json_encode(['code' => -1, 'msg' => '无效的投稿类型'], JSON_UNESCAPED_UNICODE);
        $DB->close();exit();
    }
    $data = [
        ':pro_name' => $pro_name,
        ':pro_id' => $pro_id,
        ':status' => '待处理',
        ':content' => $error_type,
        ':addtime' => time(),
        ':user_id' => isset($userrow['zid']) ? $userrow['zid'] : 0,
        ':ip' => getIp()
    ];
    $sql = "INSERT INTO `pre_pro_link` (`pro_name`,`pro_id`,`status`,`content`,`addtime`,`user_id`,`ip`) 
            VALUES (:pro_name,:pro_id,:status,:content,:addtime,:user_id,:ip)";
    if ($DB->query($sql, $data)) {
        $result = ["code" => 0, "msg" => "反馈提交成功"];
    } else {
        $result = ["code" => -1, "msg" => '提交失败，' . $DB->error()];
    }
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    $DB->close();exit();
    
   case 'addReport':
    $pro_name = trim(input('post.pro_name', ''));
    $pro_id = trim(input('post.pro_id', ''));
    $error_type = intval(trim(input('post.content', '')));
    if (empty($pro_name) || empty($pro_id)) {
        echo json_encode(['code' => -1, 'msg' => '提交非法！'], JSON_UNESCAPED_UNICODE);
        $DB->close();exit();
    }
    if ($error_type === -1) {
        echo json_encode(['code' => -1, 'msg' => '请选择投稿类型'], JSON_UNESCAPED_UNICODE);
        $DB->close();exit();
    }
    if (!in_array($error_type, [0, 1, 2])) {
        echo json_encode(['code' => -1, 'msg' => '无效的投稿类型'], JSON_UNESCAPED_UNICODE);
        $DB->close();exit();
    }
    $data = [
        ':pro_name' => $pro_name,
        ':pro_id' => $pro_id,
        ':status' => '待处理',
        ':content' => $error_type,
        ':addtime' => time(),
        ':user_id' => isset($userrow['zid']) ? $userrow['zid'] : 0,
        ':ip' => getIp()
    ];
    $sql = "INSERT INTO `pre_pro_report` (`pro_name`,`pro_id`,`status`,`content`,`addtime`,`user_id`,`ip`) 
            VALUES (:pro_name,:pro_id,:status,:content,:addtime,:user_id,:ip)";
    if ($DB->query($sql, $data)) {
        $result = ["code" => 0, "msg" => "反馈提交成功"];
    } else {
        $result = ["code" => -1, "msg" => '提交失败，' . $DB->error()];
    }
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    $DB->close();exit();
   case 'addcomplaint':
    $pro_name = trim(input('post.pro_name', ''));
    $pro_id = trim(input('post.pro_id', ''));
    $error_type = intval(trim(input('post.content', '')));
    if (empty($pro_name) || empty($pro_id)) {
        echo json_encode(['code' => -1, 'msg' => '提交非法！'], JSON_UNESCAPED_UNICODE);
        $DB->close();exit();
    }
    if ($error_type === -1) {
        echo json_encode(['code' => -1, 'msg' => '请选择投稿类型'], JSON_UNESCAPED_UNICODE);
        $DB->close();exit();
    }
    if (!in_array($error_type, [0, 1, 2])) {
        echo json_encode(['code' => -1, 'msg' => '无效的投稿类型'], JSON_UNESCAPED_UNICODE);
        $DB->close();exit();
    }
    $data = [
        ':pro_name' => $pro_name,
        ':pro_id' => $pro_id,
        ':status' => '待处理',
        ':content' => $error_type,
        ':addtime' => time(),
        ':user_id' => isset($userrow['zid']) ? $userrow['zid'] : 0,
        ':ip' => getIp()
    ];
    $sql = "INSERT INTO `pre_pro_complaint` (`pro_name`,`pro_id`,`status`,`content`,`addtime`,`user_id`,`ip`) 
            VALUES (:pro_name,:pro_id,:status,:content,:addtime,:user_id,:ip)";
    if ($DB->query($sql, $data)) {
        $result = ["code" => 0, "msg" => "反馈提交成功"];
    } else {
        $result = ["code" => -1, "msg" => '提交失败，' . $DB->error()];
    }
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    $DB->close();exit();
}