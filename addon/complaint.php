<?php
use core\Db;

include "../includes/common.php";
define('AJAX_VERSION', '2.8.3');
define('AJAX_BUILD', 1043);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目投诉反馈</title>
    <style>
       body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        th, td {
            font-size: 12px; 
            padding: 10px 10px;
            border: 0.8px solid #ddd;
            text-align: center;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .no-feedback {
            text-align: center;
            font-size: 0.8em;
            color: #555;
        }
        .red {
            color: red; /* 处理中内容的颜色 */
        }
        .green {
            color: green; /* 其他内容的颜色 */
        }
        .search-box {
            text-align: center;
            margin: 20px 0;
        }
        /* 分页按钮样式 */

        .pagination a {
            color: #4CAF50;
            padding: 10px 15px;
            text-decoration: none;
            border: 1px solid #ddd;
            margin: 0 5px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .pagination a:hover {
            background-color: #4CAF50;
            color: white;
        }
        .pagination a.active {
            background-color: #4CAF50;
            color: white;
            border: 1px solid #4CAF50;
        }
        .pagination a.disabled {
            color: #ddd;
            pointer-events: none;
            border: 1px solid #ddd;
        }
        .pagination {
            display: inline-block;
            padding-left: 0;
            margin: 20px 0;
            border-radius: 4px;
        }
        .pagination>li {
            display: inline;
        }
        .pagination>li>a, .pagination>li>span,.pageSize ,.pageInput{
            position: relative;
            float: left;
            padding: 6px 12px;
            margin-left: -1px;
            line-height: 1.42857143;
            color: #4caf50;
            text-decoration: none;
            background-color: #fff;
            border: 1px solid #ddd;
        }
        .pagination>li:first-child>a, .pagination>li:first-child>span {
            margin-left: 0;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }
        .pagination>li:last-child>a, .pagination>li:last-child>span {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
        .pagination>li>a:hover, .pagination>li>span:hover, .pagination>li>a:focus, .pagination>li>span:focus {
            z-index: 2;
            color: #4caf50;
            background-color: #eee;
            border-color: #ddd;
        }
        .pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus {
            z-index: 3;
            color: #fff;
            cursor: default;
            background-color: #4caf50;
            border-color: #4caf50;
        }
        .pagination>.disabled>span, .pagination>.disabled>span:hover, .pagination>.disabled>span:focus, .pagination>.disabled>a, .pagination>.disabled>a:hover, .pagination>.disabled>a:focus {
            color: #777;
            cursor: not-allowed;
            background-color: #fff;
            border-color: #ddd;
        }
        .pagination-lg>li>a, .pagination-lg>li>span {
            padding: 10px 16px;
            font-size: 18px;
            line-height: 1.3333333;
        }
        .pagination-lg>li:first-child>a, .pagination-lg>li:first-child>span {
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
        }
        .pagination-lg>li:last-child>a, .pagination-lg>li:last-child>span {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }
        .pagination-sm>li>a, .pagination-sm>li>span {
            padding: 5px 10px;
            font-size: 12px;
            line-height: 1.5;
        }
        .pagination-sm>li:first-child>a, .pagination-sm>li:first-child>span {
            border-top-left-radius: 3px;
            border-bottom-left-radius: 3px;
        }
        .pagination-sm>li:last-child>a, .pagination-sm>li:last-child>span {
            border-top-right-radius: 3px;
            border-bottom-right-radius: 3px;
        }
        ul.pagination .pageInput{line-height:2;outline: none;}
        ul.pagination .pageInput:focus {
            border-color: #4caf50; 
        }
    </style>
    <script src="<?php echo $cdnpublic ?>jquery/1.12.4/jquery.min.js"></script>
</head>
<body>

<div class="search-box">
    <form method="GET" action="">
        <input type="text" name="pro_name" placeholder="搜索商品名称..." value="">
        <button type="submit">搜索</button>
        <button type="button" onclick="window.location.href='?'">清空搜索</button>
    </form>
</div>
<div class="table-responsive">
<table><tr><th>ID</th><th style="width: 120px;">商品名称</th><th>时间</th><th>原因</th><th style="width: 40px;">状态</th><th style="width: 40px;">投稿ID</th></tr>
<?php
$pagesize = 30;
$total = $DB->count("SELECT count(*) from pre_pro_complaint");
$pages    = intval($total / $pagesize);
if ($total % $pagesize) {
    $pages++;
}

$sql     = "1";
$pro_name = isset($_GET['pro_name']) ? $_GET['pro_name'] : "";

if (isset($_GET['page'])) {
    $page = intval($_GET['page']);
} else {
    $page = 1;
}
$offset = $pagesize * ($page - 1);
if ($pro_name) {
    $pro_name = daddslashes($pro_name);
    $sql     .= " and pro_name like '%{$pro_name}%'";
    $rs     = $DB->query("SELECT * FROM pre_pro_complaint WHERE pro_name like ? order by id DESC limit $offset,$pagesize", ['%' . $pro_name . '%']);
} else {
    $rs     = $DB->query("SELECT * FROM pre_pro_complaint order by id DESC limit $offset,$pagesize");
}

while ($res = $DB->fetch($rs)) {
    if ($res['status'] == '处理中') {
        $class = 'red';
    } else {
        $class = 'green';
    }

    switch ($res['content']) {
        case 0:
            $content = "没有效果";
            $content_style = 'color:#0000ff';
            break;
        case 1:
            $content = "二改垃圾";
            $content_style = 'color:#0000ff';
            break;
        case 2:
            $content = "空壳项目";
            $content_style = 'color:#0000ff';
            break;
    }
    $user_id = $res['user_id']; 
    $updatetime = $res['updatetime'] ? date('Y-m-d H:i:s', $res['updatetime']) : '正在处理';
    $class = '';
    switch ($res['status']) {
        case '处理中':
            $class = 'orange';
            break;
        case '已下架':
            $class = 'red';
            break;
        case '待处理':
            $class = 'yellow';
            break;
        default:
            $class = 'green';
            break;
    }
    echo '<tr>
        <td>' . $res['id'] . '</td>
        <td>' . $res['pro_name'] . '</td>
        <td>' . date('Y-m-d H:i:s', $res['addtime']) . '<br><span style="color: red;">' . $updatetime . '</span></td>
        <td style="' . $content_style . ';">' . $content . '</td>
        <td class="' . $class . '">' . $res['status'] . '</td>
        <td>' . $user_id . '</td></tr>';
}
?>
   </table>
   </div>
<div style="text-align: center;">
    <div class="pagination">
        <?php
        $pageList = new \core\Page($total, $pagesize, 0, '');
        echo $pageList->showPage();
        ?>
    </div>
</div>
</body>
<script>
        document.addEventListener('contextmenu', function(event) {
            event.preventDefault();
        });
        document.addEventListener('dragstart', function(event) {
            event.preventDefault();
        });
        document.addEventListener('copy', function(event) {
            event.preventDefault();
        });
        document.addEventListener('keydown', function(event) {
            if (event.key === 'F12' || event.ctrlKey && event.shiftKey && event.key === 'I') {
                event.preventDefault();
            }
        });
    </script>
</html>
