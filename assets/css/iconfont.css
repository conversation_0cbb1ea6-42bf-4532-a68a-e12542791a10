
@font-face {font-family: "iconfont";
  src: url('../fonts/iconfont/iconfont.eot?t=1487643189178'); /* IE9*/
  src: url('../fonts/iconfont/iconfont.eot?t=1487643189178#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('../fonts/iconfont/iconfont.woff?t=1487643189178') format('woff'), /* chrome, firefox */
  url('../fonts/iconfont/iconfont.ttf?t=1487643189178') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('../fonts/iconfont/iconfont.svg?t=1487643189178#iconfont') format('svg'); /* iOS 4.1- */
}

.iconfont {
  font-family:"iconfont" !important;
  font-size:16px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-max:before { content: "\e623"; }

.icon-restore:before { content: "\e624"; }

.icon-min:before { content: "\e625"; }

.icon-close:before { content: "\e626"; }

