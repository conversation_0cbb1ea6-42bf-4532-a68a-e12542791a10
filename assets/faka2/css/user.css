﻿@charset "utf-8";
/* CSS Document */

.user_top {
	height: 140px;
	background-image: url(../images/user_bg.jpg);
	background-repeat: no-repeat;
	background-position: center;
	background-size: 100%;
	text-align: center;
}
.user_logo {
	margin-top: 0px;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
}


.user_logo .img {
	position: relative;
	width: 55px;
	height: 55px;
	border-radius: 50%;
	margin-top: 15px;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 0px;
}

.user_logo .img:before {
    position: absolute;
    display: block;
    content: '';
    width: 100%;
    height: 100%;
    border-radius: 50%;
    box-shadow: inset 0 0 0 4px rgba(255, 255, 255, 0.6), 0 1px 2px rgba(0, 0, 0, 0.3);
    -webkit-transition: all 0.35s ease-in-out;
    -moz-transition: all 0.35s ease-in-out;
    transition: all 0.35s ease-in-out;
}

.user_logo img {
    width: 100%;
    height: 100%;
}

.user_logo .img img {
    border-radius: 50%;
}.user_info {
}
.user_info .user_name {
	font-size: 16px;
	color: #FFFFFF;
	line-height: 35px;
}
.user_info .user_dengji {
	font-size: 12px;
	color: #F3F3F3;
	line-height: 25px;
}
.user_nav_list {
	border-top-width: 1px;
	border-left-width: 1px;
	border-top-style: solid;
	border-left-style: solid;
	border-top-color: #EBEBEB;
	border-left-color: #EBEBEB;
}
.user_nav_list  li {
	border-right-width: 1px;
	border-bottom-width: 1px;
	border-right-style: solid;
	border-bottom-style: solid;
	border-right-color: #EBEBEB;
	border-bottom-color: #EBEBEB;
	height: 50px;
}
.user_nav_list a {
	display: block;
	color: #666666;
	text-decoration: none;
	width: 100%;
	float: left;
}
.user_nav_list .u_nav_icon {
	float: left;
	margin-left: 2%;
	height: 30px;
	width: 30px;
	background-size: 90%;
	margin-top: 10px;
}
.user_nav_list .znx {
	background-image: url(../images/iconfont-zhanneixin.png);
	background-repeat: no-repeat;
}
.user_nav_list .money {
	background-image: url(../images/iconfont-jiaofei.png);
	background-repeat: no-repeat;
}
.user_nav_list .huibi {
	background-image: url(../images/iconfont-hui.png);
	background-repeat: no-repeat;
}
.user_nav_list .dingdan {
	background-image: url(../images/iconfont-dingdan .png);
	background-repeat: no-repeat;
}
.user_nav_list .tixian {
	background-image: url(../images/iconfont-wiappquxianguanli.png);
	background-repeat: no-repeat;
}
.user_nav_list   .qiandao {
	background-image: url(../images/iconfont-qiandaotubiao.png);
	background-repeat: no-repeat;
}
.user_nav_list .anquan {
	background-image: url(../images/iconfont-zhanghuanquan.png);
	background-repeat: no-repeat;
}
.user_nav_list .loginpassword {
	background-image: url(../images/iconfont-denglumima.png);
	background-repeat: no-repeat;
}
.user_nav_list .paypassword {
	background-image: url(../images/iconfont-shezhijiaoyimima.png);
	background-repeat: no-repeat;
}
.user_nav_list .findpaypwd {
	background-image: url(../images/iconfont-zhaohuimima.png);
	background-repeat: no-repeat;
}
.user_nav_list .bangdingshouji {
	background-image: url(../images/iconfont-shoujibangding.png);
	background-repeat: no-repeat;
}
.user_nav_list .zhifubao {
	background-image: url(../images/iconfont-zhifubao.png);
	background-repeat: no-repeat;
}
.user_nav_list .yinhangka {
	background-image: url(../images/iconfont-yinxingqia.png);
	background-repeat: no-repeat;
}





.user_nav_list .u_nav_name {
	float: left;
	margin-left: 1.5%;
	line-height: 50px;
	font-size: 15px;
}
.user_nav_list .u_money {
	position: relative;
	font-size: 13px;
	color: #666666;
	line-height: 51px;
	float: right;
}
.user_nav_list .u_money i {
	color: #ff6063;
	font-style: normal;
}

.user_nav_list .nt_icon {
	background-image: url(../images/iconfont-jiantou.png);
	background-repeat: no-repeat;
	background-position: center;
	background-size: 15px;
	height: 50px;
	width: 32px;
	float: right;
	margin-right: 1%;
}
.login_out {
	text-align: center;
}
.login_out  a span {
	height: 20px;
	width: 20px;
	position: relative;
	top: 4px;
	right: 5px;
}
.login_out  a  span img {
	height: 18px;
	width: 18px;
}

.login_out a {
	line-height: 45px;
	text-decoration: none;
	text-align: center;
	float: left;
	color: #FFFFFF;
	background-color: #8c6a5e;
	width: 100%;
}
.login_out  a  i {
	font-size: 15px;
	font-style: normal;
}
.my_money {
	line-height: 50px;
}
.my_money span {
	font-size: 14px;
	color: #333333;
	margin-left: 10px;
}
.my_money  i {
	font-style: normal;
	font-weight: bold;
	color: #ff6063;
}
.my_money .tixian_btn {
	float: right;
	margin-right: 10px;
}
.my_money   .tixian_btn    a  {
	text-decoration: none;
	color: #FFFFFF;
	background-color: #ff6063;
	float: right;
	line-height: 35px;
	padding-right: 10px;
	padding-left: 10px;
	margin-top: 8px;
	border-radius: 3px;
}
.my_title {
	line-height: 45px;
	font-size: 16px;
	color: #00bb9c;
	padding-left: 10px;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #EEEEEE;
}
.my_title_right {
	float: right;
	margin-right: 10px;
	color: #333333;
	font-size: 14px;
}
.my_title_right i {
	font-style: normal;
	color: #ff6063;
	font-weight: bold;
	font-size: 15px;
}


.my_info {
	padding-right: 10px;
	padding-left: 10px;
}
.my_info li {
	float: left;
	border-bottom-width: 1px;
	border-bottom-style: dashed;
	border-bottom-color: #EBEBEB;
	line-height: 50px;
	width: 100%;
}
.my_info   div {
	width: 33%;
	float: left;
	font-size: 14px;
	color: #333333;
}
.my_info .my_form {
	width: 20%;
}

.my_info .my_time {
	text-align: center;
	width: 43%;
}
.my_info .my_moneys {
	text-align: right;
	color: #ff6063;
	font-weight: bold;
	float: right;
}
.tx_info {
}

.tx_info  li {
	float: left;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #F0F0F0;
	line-height: 35px;
	width: 97%;
	padding-top: 5px;
	padding-bottom: 5px;
	padding-right: 1.5%;
	padding-left: 1.5%;
}
.tx_info  li:nth-child(2n){
	background-color: #FDFDFD;
}
.tx_info .tx_jilu {
	width: 50%;
	float: left;
}
.tx_info .tx_jilu p {
	font-size: 14px;
	color: #333333;
	margin: 0px;
}
.tx_info      .right     p  {
	text-align: right;
}
.tx_info .tx_jilu p b {
	color: #ff6063;
}
.add_tixian li {
	margin-top: 15px;
	margin-bottom: 15px;
}

.add_tixian #money {
	font-weight: bold;
	color: #ff6063;
}

.add_tixian .ui-border-t {
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	margin-bottom: 15px;
}
.add_tixian #yzm {
	width: 67%;
}
.add_tixian  .request_yzm  {
	height: 40px;
	width: 28%;
	margin-top: 1.5%;
	font-size: 12px;
}
.add_tixian  .sqtx  {
	margin-right: 1.5%;
	margin-left: 1.5%;
	width: 97%;
}
.my_order {
	border-bottom-width: 3px;
	border-bottom-style: solid;
	border-bottom-color: #EBEBEB;
	font-size: 14px;
	color: #333333;
}
.order_top {
	line-height: 35px;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #f5f5f5;
	float: left;
	width: 100%;
}
.order_mall {
	float: left;
	margin-left: 10px;
}
.order_time {
	float: right;
	margin-right: 10px;
}
.order_content {
	float: left;
	width: 100%;
}
.order_content .order {
	line-height: 30px;
	float: left;
	padding-left: 10px;
	color: #666666;
}
.order_content .order_money, .order_content .order_status {
	float: right;
	margin-right: 10px;
	text-align: right;
}
.qiandao_content {
	text-align: center;
	padding-top: 20px;
	display: block;
}
.qiandao_ok {
	display: none;
}
.qiandao_content  a {
	text-decoration: none;
	display: block;
}
.qiandao_icon img {
	width: 100px;
}
.qiandao_content  span {
	line-height: 50px;
}
.qiandaojilu {
	font-size: 14px;
	color: #333333;
}
.qiandao_top {
	width: 100%;
	line-height: 40px;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #F0F0F0;
	float: left;
}
.qiandao_top  em {
	float: left;
	font-style: normal;
	margin-left: 10px;
}
.qiandao_num {
	float: right;
	margin-right: 10px;
}
.qiandaojilu ul {
	float: left;
	width: 100%;
}
.qiandaojilu li {
	line-height: 40px;
	margin-left: 10px;
	border-bottom-width: 1px;
	border-bottom-style: dashed;
	border-bottom-color: #F0F0F0;
	font-size: 13px;
	color: #666666;
}
.qiandaojilu li span {
	margin-right: 3px;
	margin-left: 3px;
}

.qiandaojilu li .qiandao_time {
	float: right;
	margin-right: 10px;
	color: #666666;
	font-size: 13px;
}
.msg {
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #E4E4E4;
}

.msg_title {

	line-height: 40px;
	float: left;
	width: 100%;
	background-color: #f0f9ff;
	color: #585858;
	border-top : 1px solid #e1f3ff;
	font-size: 14px;
	
	
}
.msg_title2 {
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #EEEEEE;
	line-height: 30px;
	float: left;
	width: 100%;
	font-size: 12px;
	color: #666666;
	margin-left: 6px;
}
.msg_title2 h1 {
    font-size: 12px;
	color: #666666;
	margin-left: 6px;
}
.msg_title2 span {
	float: right;
	font-size: 12px;
	color: #666666;
	margin-right: 10px;
}
.msg_content {
	float: left;
	width: 96%;
	padding: 10px;
	font-size: 14px;
	color: #666666;
	line-height: 24px;
}
.bank {
	background-image: url(../images/iconfont-i.png);
	background-repeat: no-repeat;
	background-position: right center;
	background-size: 25px;
}
