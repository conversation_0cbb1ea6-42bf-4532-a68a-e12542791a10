﻿@charset "utf-8";
/* CSS Document */
body {
	margin: 0px;
	padding: 0px;
	font-family:Helvetica,"Microsoft YaHei", Arial, Helvetica, sans-serif;
	background-color: #f5f5f5;
	-webkit-text-size-adjust: none 
}
ul, li {
	margin: 0px;
	padding: 0px;
	list-style-type: none;
}

img {
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
    max-width: 100%;
}

form {
	margin: 0px;
	padding: 0px;
}

a, div, header, span {
  -webkit-tap-highlight-color: rgba(255,255,255,0);
}

.header .m_logo {
	float: left;
	margin-left: 10px;
	
}
.header .m_logo a {
	height: 45px;
	float: left;
}
.header .m_logo img {
	height: 35px;
	margin-top: 5px;
}
.mobile {
	background-color: #FFFFFF;
	min-width: 320px;
}
.w {
	background-color: #FFFFFF;
	float: left;
	min-width: 320px;
	width: 97%;
	margin-top: 1%;
	margin-right: 1.5%;
	margin-bottom: 1%;
	margin-left: 1.5%;
}
.m_user {

    line-height: 40px;
	font-size: 16px;
	color: #666666;
	text-align: center;
	float: left;
	width: 97%;
	background-color: #FFFFFF;
}
.m_userx {

    line-height: 40px;
	font-size: 12px;
	color: #666666;
	text-align: center;
	float: left;
	width: 97%;
	background-color: #FFFFFF;
}
.menux a {
	
	text-decoration: none;
	color: #eae9e9;
}

.m_more {
	line-height: 40px;
	text-align: center;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #F6F6F6;
	float: left;
	width: 100%;
	border-top-width: 1px;
	border-top-style: solid;
	border-top-color: #F6F6F6;
}
.m_more a {
	line-height: 40px;
	height: 40px;
	width: 100%;
	color: #666666;
	text-decoration: none;
	float: left;
	font-size: 12px;
}
.data_null {
	background-image: url(../images/nothing.png);
	background-repeat: no-repeat;
	background-position: center;
	height: 150px;
}

/*页面加载*/
#preloader {position:fixed;z-index:2500;top:0;left:0;right:0;bottom:0;background-color:#fff; z-index:999999; }
#status {
	position:fixed;
	z-index:999999;
	width:250px;
	height:250px;
	position:absolute;
	left:50%;
	top:50%;
	background-size:32px 32px;
	margin-top: -125px;
	margin-right: 0;
	margin-bottom: 0;
	margin-left: -115px;
}
#status p{top:70%;}
.center-text{
	text-align:center;
	background-image: url(../images/loading.jpg);
	background-repeat: no-repeat;
	background-position: center top;
	height: 182px;
	width: 234px;
}
.center-text span {
	font-size: 14px;
	color: #999999;
	position: relative;
	top: 170px;
}

/*header*/
.header{
	height:44px;
	background:#4eabe8;
	position: relative;
	z-index: 10;
}
.new-a-back {
	height: 40px;
	position: absolute;
	width: 45px;
}
.new-a-back span {
	
	display:block;
	height: 35px;
	text-indent:17px;
	width: 50px;
	color:#FFF;
	font-size:14px;
	padding-top: 8px;
	margin-left: -10px;
}
.new-a-back span img {
	width: 25px;
}

.header h2 {
	color: #fff;
	font-size: 16px;
	font-weight: normal;
	height: 44px;
	line-height: 44px;
	text-align: center;
	font-weight:bold;
}
.header .header_right {
	position: absolute;
	right: 10px;
	top: 10px;
}
.header .header_right img {
	height: 25px;
}
.head_right {
	position: absolute;
	top: 15px;
	right: 10px;
}
.head_right    a   {
	color:#FFFFFF;
	font-size:12px;
	padding-top:5px;
	text-decoration: none;
}
.search {
	margin-top: 2%;
}



.search_input {
	margin: 0px;
	padding-top: 12px;
	padding-right: 0px;
	padding-bottom: 10px;
	padding-left: 40px;
	float: left;
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	outline: none;
	font-family:Helvetica, "Microsoft YaHei", Arial, Helvetica, sans-serif;
	font-size: 13px;
	color: #666666;
	background-image: url(../images/iconfont-sousuo.png);
	background-repeat: no-repeat;
	background-position: 10px center;
	background-size: 20px;
	width: 80%;
}
.search_input2 {
	width: 230px;
	font-size: 14px;
	color: #FF7200;
}
.search_submit {
	float: right;
	width: 20%;
	height: 37px;
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	outline: none;
	font-family:Helvetica, "Microsoft YaHei", Arial, Helvetica, sans-serif;
	font-size: 14px;
	line-height: 37px;
	color: #FFFFFF;
	background-color: #009999;
	position: absolute;
	right: 1.5%;
}
.m_input {
	width: 97%;
	padding-right: 1.5%;
	padding-left: 1.5%;
	font-size: 15px;
	color: #333333;
	height: 40px;
	font-family: Helvetica, "Microsoft YaHei", Arial, Helvetica, sans-serif;
	border: 1px solid #D7D7D7;
	margin: 1.5%;
	outline: none;
	border-radius: 3px;
}
.color_red {
	font-weight: bold;
	color: #ff6063;
}
.color_luse {
	font-weight: bold;
	color: #00bb9c;
}

.color_chunlu {
	font-weight: bold;
	color: #91C756;
}






.footer a {
	float: left;
	width: 33%;
	height: 80px;
	text-align: center;
	text-decoration: none;
}
.footer .ico_img {
	margin-top: 15px;
}
.footer .ico_img img {
	width: 30px;
}

.footer a span {
	font-size: 12px;
	color: #666666;
	line-height: 30px;
}
.copyright {
	line-height: 40px;
	font-size: 12px;
	color: #999999;
	text-align: center;
	float: left;
	width: 100%;
}
.copyright a {
	text-decoration: none;
	color: #999999;
}
.gotop {
	background-image: url(../images/iconfont-fanhuidingbu.png);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 40px;
	height: 40px;
	width: 40px;
	position: fixed;
	right: 10px;
	bottom: 50px;
	z-index: 9999;
	cursor: pointer;
}
.no_login_show {
	background-color: #FFFFFF;
	text-align: center;
	float: left;
	width: 360px;
	overflow: hidden;
}
.no_login_show h1 {
	font-size: 16px;
	color: #666666;
	margin: 0px;
	line-height: 50px;
	border-bottom-width: 1px;
	border-bottom-style: dotted;
	border-bottom-color: #E7E7E7;
	background-color: #F9F9F9;
}
.no_login_show a {
	width: 100%;
	line-height: 50px;
	text-decoration: none;
	color: #ff6063;
	float: left;
	border-bottom-width: 1px;
	border-bottom-style: dashed;
	border-bottom-color: #EEEEEE;
}
#Validform_msg{color:#7d8289; font: 12px/1.5 tahoma, arial, \5b8b\4f53, sans-serif; width:280px; -webkit-box-shadow:2px 2px 3px #aaa; -moz-box-shadow:2px 2px 3px #aaa; background:#fff; position:absolute; top:0px; right:50px; z-index:99999; display:none;filter: progid:DXImageTransform.Microsoft.Shadow(Strength=3, Direction=135, Color='#999999'); box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.1);}
#Validform_msg .iframe{position:absolute; left:0px; top:-1px; z-index:-1;}
#Validform_msg .Validform_title{line-height:25px; height:25px; text-align:left; font-weight:bold; padding:0 8px; color:#fff; position:relative; background-color:#999;
background: -moz-linear-gradient(top, #999, #666 100%); background: -webkit-gradient(linear, 0 0, 0 100%, from(#999), to(#666)); filter:  progid:DXImageTransform.Microsoft.gradient(startColorstr='#999999', endColorstr='#666666');}
#Validform_msg a.Validform_close:link,#Validform_msg a.Validform_close:visited{line-height:22px; position:absolute; right:8px; top:0px; color:#fff; text-decoration:none;}
#Validform_msg a.Validform_close:hover{color:#ccc;}

/*分页样式*/

.pagination li {

	line-height: 20px;

	height: 20px;

	border: 1px solid #CCC;

	display: inline-block;

	text-align: center;

	color: #666;

	margin-right: 2px;

	margin-left: 2px;

}

.pagination li:hover {

	background-color: #0089C9;

	color: #FFF;

	border: 1px solid #0089C9;

}

.pagination .active {

	background-color: #0089C9;

	color: #FFF;

	border: 1px solid #0089C9;

	margin-left: 2px;

	padding-right: 8px;

	padding-left: 8px;

	line-height: 20px;

}

.pagination li a {

	text-align: center;

	display: block;

	line-height: 20px;

	height: 100%;

	padding-right: 8px;

	padding-left: 8px;

}

.pagination li:hover a {

	color: #fff;

	text-align: center;

	display: block;

	cursor: pointer;

	height: 100%;

	padding-right: 8px;

	padding-left: 8px;

}

.pagination li .rows{

	padding-right: 6px;

	padding-left: 6px;	

	line-height: 20px;

}
.text-right {

	text-align: right;

}