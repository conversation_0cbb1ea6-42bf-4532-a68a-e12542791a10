﻿@charset "utf-8";


.header .m_search {
	float: right;
	height: 25px;
	width: 25px;
	margin-top: 10px;
	margin-right: 15px;
}

.top .m_banner {
	padding: 5px;

}
.top .m_nav {
	margin-top: 10px;
	float: left;
	width: 100%;
}
.top .m_nav img {
	height: 44px;
}

.top .m_nav a {
	width: 24%;
	text-decoration: none;
	height: 100px;
	text-align: center;
	float: left;
	margin-top: 10px;
}
.top .m_nav a span {
	width: 100%;
	float: left;
	line-height: 40px;
	font-size: 14px;
	color: #666666;
	font-family: "Microsoft YaHei", Arial, Helvetica, sans-serif;
}

.m_mall .mall_title {
	line-height: 40px;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #F9F9F9;
	width: 100%;
	height: 40px;
}
.m_mall .mall_title span {
	float: left;
	margin-left: 10px;
	color: #00bb9c;
	font-size: 16px;
}
.m_mall .mall_title em {
	float: right;
	margin-right: 10px;
	font-size: 12px;
	font-style: normal;
	color: #666666;
}
.m_mall .mall_title a {
	color: #666666;
	text-decoration: none;
}

.m_mall .mall_list {
	float: left;
	width: 100%;
	padding-top: 25px;
}
.m_mall .mall_list .mall {
	float: left;
	width: 25%;
	text-align: center;
	text-decoration: none;
	margin-bottom: 25px;
	display: block;
}
.m_mall .mall_list .mall .mall_logo {
	height: 50px;
	overflow: hidden;
}

.m_mall .mall_list .mall .mall_logo img {
	width: 80px;
}

.m_mall .mall_list .mall span {
	text-decoration: none;
	color: #666666;
	font-size: 12px;
	line-height: 30px;
}
.m_mall .mall_list .mall span i {
	color: #FF3300;
	font-size: 13px;
	font-style: normal;
}
.list_mall {
	width: 33%;
	float: left;
	text-align: center;
	text-decoration: none;
	display: block;
	margin-bottom: 20px;
}
.list_mall .mall_logo {
	overflow: hidden;
	height: 40px;
}
.list_mall .mall_logo img {
	max-height:40px;
}
.list_mall span {
	text-decoration: none;
	color: #666666;
	font-size: 14px;
	line-height: 30px;
}
.list_mall span i {
	color: #FF3300;
	font-size: 14px;
	font-style: normal;
}

/*淘宝返利*/
.tao_search {
	background-image: url(../images/bg.jpg);
	background-size: cover;
	background-repeat: no-repeat;
	background-position: center top;
}
.taobao_form {
	margin-top: 18%;
	width: 100%;
	margin-right: auto;
	margin-bottom: 0px;
	margin-left: auto;
}

.search_taobao {
	width: 80%;
	padding-top: 17px;
	padding-right: 0px;
	padding-bottom: 15px;
	padding-left: 35px;
	float: left;
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	outline: none;
	font-family:Helvetica, "Microsoft YaHei", Arial, Helvetica, sans-serif;
	font-size: 13px;
	color: #666666;
	background-image: url(../images/iconfont-sousuo.png);
	background-repeat: no-repeat;
	background-position: 10px center;
	background-size: 20px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	margin-left: 0px;
}
.taobao_submit {
	float: left;
	width: 20%;
	height: 46px;
	border-top-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 0px;
	border-left-width: 0px;
	border-top-style: none;
	border-right-style: none;
	border-bottom-style: none;
	border-left-style: none;
	outline: none;
	font-family:Helvetica, "Microsoft YaHei", Arial, Helvetica, sans-serif;
	font-size: 14px;
	line-height: 46px;
	color: #FFFFFF;
	background-color: #ee4444;
	background-image: url(../images/top_alpha.png);
	background-repeat: no-repeat;
	background-size: 100%;
	cursor: pointer;
	background-position: center;
}
.taobao_liucheng {
	width: 100%;
	background-color: #fffaea;
	text-align: center;
}


.m_baoliao  .baoliao_title {
	line-height: 40px;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #F9F9F9;
	width: 100%;
	height: 40px;
}
.m_baoliao .baoliao_title span {
	float: left;
	margin-left: 10px;
	color: #00bb9c;
	font-size: 16px;
}
.m_baoliao .baoliao_title em {
	float: right;
	margin-right: 10px;
	font-size: 12px;
	font-style: normal;
	color: #666666;
}
.m_baoliao .baoliao_title a {
	color: #666666;
	text-decoration: none;
}
.m_baoliao .baoliao_title img {
	width: 20px;
	margin-top: 10px;
}


.m_baoliao .baoliao_list {
	float: left;
}
.baoliao_content   {
	float: left;
	width: 100%;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #F2F2F2;
	margin-top: 15px;
}
.baoliao_content .bl_img {
	float: left;
	margin: 5px;
	width: 15%;
}
.baoliao_content .bl_img img {
	width: 90px;
}
.baoliao_content .bl_right {
	float: left;
	margin-left: 10px;
	width: 78%;
}

.baoliao_content .bl_title {
	float: left;
	font-size: 12px;
	color: #666666;
	line-height: normal;
	width: 100%;
}
.baoliao_content .bl_note {
	float: left;
	font-size: 12px;
	line-height: normal;
	color: #FF3300;
	margin-top: 5px;
	width: 100%;
}
.baoliao_content .bl_tag {
	float: left;
	font-size: 12px;
	width: 100%;
	line-height: 30px;
	margin-top: 10px;
}
.baoliao_content .bl_right .bl_tag .bl_price {
	float: left;
	font-size: 12px;
	color: #FF3300;
}
.baoliao_content .bl_right .bl_tag .bl_oprice {
	font-size: 12px;
	text-decoration: line-through;
	float: left;
	margin-left: 10px;
	color: #999999;
}
.baoliao_content .bl_right .bl_tag .bl_mall {
	float: right;
	font-size: 12px;
	color: #666666;
}
.baoliao_content .bl_right .bl_tag .bl_time {
	float: right;
	font-size: 12px;
	color: #666666;
	margin-left: 10px;
}
.m_baoliao .bl_more {
	line-height: 40px;
	text-align: center;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #EEEEEE;
	float: left;
	width: 100%;
}

.m_baoliao .bl_more a {
	line-height: 40px;
	height: 40px;
	width: 100%;
	color: #666666;
	text-decoration: none;
	float: left;
	font-size: 12px;
}
