﻿/*main css*/

.main-im {
    position: fixed;
    right: 10px;
    top: 300px;
    z-index: 100;
    width: 110px;
    height: 272px;
}
.main-im .qq-a {
    display: block;
    width: 106px;
    height: 116px;
    font-size: 14px;
    color: #0484cd;
    text-align: center;
    position: relative;
}
.main-im .qq-a span {
    bottom: 5px;
    position: absolute;
    width: 90px;
    left: 10px;
}
.main-im .qq-hover-c {
    width: 70px;
    height: 70px;
    border-radius: 35px;
    position: absolute;
    left: 18px;
    top: 10px;
    overflow: hidden;
    z-index: 9;
}
.main-im .qq-container {
    z-index: 99;
    position: absolute;
    width: 109px;
    height: 118px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom: 1px solid #dddddd;
    background: url(../images/qq-icon-bg.png) no-repeat center 8px;
}
.main-im .img-qq {
    max-width: 60px;
    display: block;
    position: absolute;
    left: 6px;
    top: 3px;
    -moz-transition: all 0.5s;
    -webkit-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
}
.main-im .im-qq:hover .img-qq {
    max-width: 70px;
    left: 1px;
    top: 8px;
    position: absolute;
}
.main-im .im_main {
    background: #F9FAFB;
    border: 1px solid #dddddd;
    border-radius: 10px;
    background: #F9FAFB;
    display: block;
}
.main-im .im_main .im-tel {
    color: #000000;
    text-align: center;
    width: 109px;
    height: auto;
    border-bottom: 1px solid #dddddd;
}
.main-im .im_main .im-tel div {
    font-weight: bold;
    font-size: 12px;
    margin-top: 6px;
}
.main-im .im_main .im-tel .tel-num {
    font-family: Arial;
    font-weight: bold;
    color: #e66d15;
    margin-bottom: 6px;
}
/*.main-im .im_main .im-tel:hover { background: #fafafa; }*/

.main-im .im_main .weixing-container {
    width: 55px;
    height: 47px;
    border-right: 1px solid #dddddd;
    background: #f5f5f5;
    border-bottom-left-radius: 10px;
    background: url(../images/weixing-icon.png) no-repeat center center;
    float: left;
}
.main-im .im_main .weixing-show {
    width: 112px;
    height: 150px;
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid #dddddd;
    position: absolute;
    left: -125px;
    top: -126px;
}
.main-im .im_main .weixing-show .weixing-sanjiao {
    width: 0;
    height: 0;
    border-style: solid;
    border-color: transparent transparent transparent #ffffff;
    border-width: 6px;
    left: 112px;
    top: 134px;
    position: absolute;
    z-index: 2;
}
.main-im .im_main .weixing-show .weixing-sanjiao-big {
    width: 0;
    height: 0;
    border-style: solid;
    border-color: transparent transparent transparent #dddddd;
    border-width: 8px;
    left: 112px;
    top: 132px;
    position: absolute;
}
.main-im .im_main .weixing-show .weixing-ma {
    width: 104px;
    height: 103px;
    padding-left: 5px;
    padding-top: 5px;
}
.main-im .im_main .weixing-show .weixing-txt {
    position: absolute;
    top: 110px;
    left: 7px;
    width: 100px;
    margin: 0 auto;
    text-align: center;
    line-height: 16px;
}
.main-im .im_main .go-top {
    width: 50px;
    height: 47px;
    background: #f5f5f5;
    border-bottom-right-radius: 10px;
    background: url(../images/totop-icon.png) no-repeat center center;
    float: right;
}
.main-im .im_main .go-top a {
    display: block;
    width: 52px;
    height: 47px;
}
.main-im .close-im {
    position: absolute;
    right: 10px;
    top: -12px;
    z-index: 100;
    width: 24px;
    height: 24px;
}
.main-im .close-im a {
    display: block;
    width: 24px;
    height: 24px;
    background: url(../images/close_im.png) no-repeat left top;
}
.main-im .close-im a:hover {
    text-decoration: none;
}
.main-im .open-im {
    cursor: pointer;
    margin-left: 68px;
    width: 40px;
    height: 133px;
    background: url(../images/open_im.png) no-repeat left top;
    display: none;
}
.label-name {
    display: inline;
    font-weight: 700;
    margin: 0;
    display: inline-block;
    text-decoration: none;
    text-align: center;
    font-family: "Helvetica Neue Light", "Helvetica Neue", Helvetica, Arial, "Lucida Grande", sans-serif;
    transition-property: all;
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    box-sizing: border-box;
    -webkit-transition-property: all;
    cursor: pointer;
    border: none;
}
.label-name:hover {
    color: #fff;
    text-decoration: none;
}
.label-succ {
    font-size: 12px;
    background-color: #a5de37;
    border-color: #ffffff;
    color: #fff;
}
.label-warn {
    font-size: 12px;
    background-color: #FF9800;
    border-color: #ffffff;
    color: #fff;
}
.label-circle {
    border-radius: 3px;
}
.label-xs {
    font-size: 1.2rem;
    padding: 3px 5px !important;
}
.label-sm {
    font-size: 1.4rem;
    padding: 5px 8px !important;
}
.label-md {
    font-size: 1.6rem;
    padding: 8px 12px !important;
}
.label-lg {
    font-size: 2.0rem;
    padding: 12px 16px !important;
}