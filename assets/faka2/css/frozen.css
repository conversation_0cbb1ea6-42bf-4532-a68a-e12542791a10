﻿@charset "UTF-8";
	body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td,header,hgroup,nav,section,article,aside,footer,figure,figcaption,menu,button {
	margin:0;
	padding:0
}
body {
	font-family:Helvetica, "Microsoft YaHei", Arial, Helvetica, sans-serif;
	line-height:1.5;
	font-size:14px;
	color:#000;
	background-color:#f8f9fa;
	-webkit-user-select:none;
	-webkit-text-size-adjust:none;
	-webkit-tap-highlight-color:rgba(255,255,255,0);
	outline:0
}
h1,h2,h3,h4,h5,h6 {
	font-size:inherit;
	font-weight:400
}
table {
	border-collapse:collapse;
	border-spacing:0
}
fieldset,img {
	border:0
}
li {
	list-style:none
}
input,button,textarea,select {
	font-family:inherit;
	font-size:inherit;
	font-weight:inherit;
	border:0;
	background:0 0;
	-webkit-appearance:none;
	outline:0
}
button:focus,input:focus,select:focus,textarea:focus {
	outline:0;
	-webkit-tap-highlight-color:transparent
}
a {
	-webkit-touch-callout:none;
	text-decoration:none;
	color:#00a5e0;
	outline:0
}
em,i {
	font-style:normal
}
em {
	color:#f28c48
}
::-webkit-input-placeholder {
	color:#999
}
.ui-txt-default {
	color:#000
}
.ui-txt-info {
	color:gray
}
.ui-txt-muted {
	color:#a6a6a6
}
.ui-txt-warning {
	color:#fc4226
}
.ui-txt-highlight {
	color:#f28c48
}
.ui-border-t {
	border-top:1px solid #dedfe0
}
.ui-border-b {
	border-bottom:1px solid #dedfe0
}
.ui-border-tb {
	background-image:none
}
.ui-border-l {
	border-left:1px solid #dedfe0
}
.ui-border-r {
	border-right:1px solid #dedfe0
}
.ui-border {
	border:1px solid #dedfe0
}
@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-border {
	position:relative;
	border:0
}
.ui-border-t,.ui-border-b,.ui-border-l,.ui-border-r,.ui-border-tb {
	border:0
}
.ui-border:before {
	content:"";
	width:200%;
	height:200%;
	position:absolute;
	top:0;
	left:0;
	border:1px solid #dedfe0;
	-webkit-transform:scale(0.5);
	-webkit-transform-origin:0 0;
	padding:1px;
	-webkit-box-sizing:border-box
}
.ui-border-t {
	background-position:left top;
	background-image:-webkit-gradient(linear,left bottom,left top,color-stop(0.5,transparent),color-stop(0.5,#dedfe0),to(#dedfe0))
}
.ui-border-b {
	background-position:left bottom;
	background-image:-webkit-gradient(linear,left top,left bottom,color-stop(0.5,transparent),color-stop(0.5,#dedfe0),to(#dedfe0))
}
.ui-border-t,.ui-border-b {
	background-repeat:repeat-x;
	-webkit-background-size:100% 1px
}
.ui-border-tb {
	background:-webkit-gradient(linear,left bottom,left top,color-stop(0.5,transparent),color-stop(0.5,#dedfe0),to(#dedfe0)) left top repeat-x,-webkit-gradient(linear,left top,left bottom,color-stop(0.5,transparent),color-stop(0.5,#dedfe0),to(#dedfe0)) left bottom repeat-x
}
.ui-border-tb {
	-webkit-background-size:100% 1px
}
.ui-border-l {
	background-position:left top;
	background-image:-webkit-gradient(linear,left top,right top,color-stop(0.5,transparent),color-stop(0.5,#dedfe0),to(#dedfe0))
}
.ui-border-r {
	background-position:right top;
	background-image:-webkit-gradient(linear,right top,left top,color-stop(0.5,transparent),color-stop(0.5,#dedfe0),to(#dedfe0))
}
.ui-border-l,.ui-border-r {
	background-repeat:repeat-y;
	-webkit-background-size:1px 100%
}
}.ui-center {
	width:100%;
	display:-webkit-box;
	-webkit-box-orient:vertical;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	text-align:center;
	height:200px
}
.ui-avatar {
	width:50px;
	height:50px;
	overflow:hidden;
	-webkit-border-radius:200px;
	display:block;
	-webkit-background-size:100% 100%;
	background-image:url(../images/default.png)
}
.ui-avatar span {
	width:50px;
	height:50px;
	overflow:hidden;
	-webkit-border-radius:200px;
	display:block;
	-webkit-background-size:100% 100%
}
.ui-avatar-lg {
	width:54px;
	height:54px;
	overflow:hidden;
	-webkit-border-radius:200px;
	display:block;
	-webkit-background-size:100% 100%;
	background-image:url(../images/default.png)
}
.ui-avatar-lg span {
	width:54px;
	height:54px;
	overflow:hidden;
	-webkit-border-radius: 50%;
	display:block;
	-webkit-background-size:100% 100%
}
.ui-avatar-s {
	width:40px;
	height:40px;
	overflow:hidden;
	-webkit-border-radius:50%;
	display:block;
	-webkit-background-size:100% 100%;
	background-image:url(../images/default.png)
}
.ui-avatar-s span {
	width:40px;
	height:40px;
	overflow:hidden;
	-webkit-border-radius: 50%;
	display:block;
	-webkit-background-size:100% 100%
}
.ui-avatar-one {
	width:64px;
	height:64px;
	overflow:hidden;
	-webkit-border-radius: 50%;
	display:block;
	-webkit-background-size:100% 100%;
	background-image:url(../images/default.png)
}
.ui-avatar-one span {
	width:64px;
	height:64px;
	overflow:hidden;
	-webkit-border-radius: 50%;
	display:block;
	-webkit-background-size:100% 100%
}
.ui-avatar-tiled {
	width:30px;
	height:30px;
	overflow:hidden;
	-webkit-border-radius: 50%;
	display:block;
	-webkit-background-size:100% 100%;
	background-image:url(../images/default.png);
	display:inline-block
}
.ui-avatar-tiled span {
	width:30px;
	height:30px;
	overflow:hidden;
	-webkit-border-radius: 50%;
	display:block;
	-webkit-background-size:100% 100%
}
.ui-reddot,.ui-reddot-border,.ui-reddot-s {
	position:relative;
	display:inline-block;
	line-height:22px;
	padding:0 6px
}
.ui-reddot:after,.ui-reddot-border:after,.ui-reddot-s:after {
	content:'';
	position:absolute;
	display:block;
	width:8px;
	height:8px;
	background-color:#f76249;
	border-radius:5px;
	right:-3px;
	top:-3px;
	-webkit-background-clip:padding-box;
	background-clip:padding-box
}
.ui-reddot-border:after {
	border:1px #fff solid
}
.ui-reddot-s:after {
	width:6px;
	height:6px;
	top:-5px;
	right:-5px
}
.ui-badge,.ui-badge-muted,.ui-badge-corner {
	display:inline-block;
	text-align:center;
	background:#f74c31;
	color:#fff;
	font-size:12px;
	height:22px;
	line-height:22px;
	-webkit-border-radius:12px;
	padding:0 7px;
	-webkit-background-clip:padding-box;
	background-clip:padding-box
}
.ui-badge-muted {
	background:#b6cae0
}
.ui-badge-wrap {
	position:relative;
	text-align:center
}
.ui-badge-corner {
	position:absolute;
	border:2px #fff solid;
	top:-2px;
	left:50%
}
.ui-icon,.ui-icon-refresh,.ui-icon-close,.ui-icon-search {
	display:inline-block;
	height:0;
	position:relative;
	overflow:visible
}
.ui-icon:before,.ui-icon-refresh:before,.ui-icon-close:before,.ui-icon-search:before {
	background:url(../images/icon.png) no-repeat 0 0;
	-webkit-background-size:150px auto;
	content:"";
	display:block;
	position:absolute;
	left:0
}
.ui-icon-refresh {
	width:22px
}
.ui-icon-refresh:before {
	width:22px;
	height:22px;
	top:-11px
}
.ui-icon-close {
	width:31px;
	height:31px;
	position:absolute;
	right:0;
	top:0
}
.ui-icon-close:before {
	background-position:-75px -50px;
	width:15px;
	height:15px;
	margin:8px;
	position:static
}
.ui-icon-search {
	width:16px;
	margin:4px
}
.ui-icon-search:before {
	width:16px;
	height:16px;
	background-position:-125px -54px;
	top:-7px
}
.ui-btn,.ui-btn-lg {
	height:30px;
	line-height:30px;
	padding:0 13px;
	min-width:56px;
	display:inline-block;
	position:relative;
	text-align:center;
	border:0;
	color:#0079ff;
	font-size:14px;
	background-color:#fdfdfd;
	background-image:-webkit-gradient(linear,left top,left bottom,color-stop(0.5,#fff),to(#fafafa));
	border-radius:3px;
	vertical-align:top;
	color:#00a5e0;
	text-shadow:0 1px 0 rgba(0,0,0,.1);
	-webkit-box-sizing:border-box;
	border:1px solid #cacccd;
	-webkit-background-clip:padding-box;
	background-clip:padding-box
}
.ui-btn:not(.disabled):not(:disabled):active,.ui-btn-lg:not(.disabled):not(:disabled):active,.ui-btn.active,.active.ui-btn-lg {
	background:#f2f2f2;
	color:rgba(0,165,224,.5);
	-webkit-background-clip:padding-box;
	background-clip:padding-box
}
.ui-btn:after,.ui-btn-lg:after {
	content:"";
	position:absolute;
	top:-12px;
	bottom:-12px;
	left:0;
	right:0
}
.ui-btn-primary {
	background-color:#18b4ed;
	border-color:#0baae4;
	background-image:-webkit-gradient(linear,left top,left bottom,color-stop(0.5,#1fbaf3),to(#18b4ed));
	color:#fff;
	-webkit-background-clip:padding-box;
	background-clip:padding-box
}
.ui-btn-primary:not(.disabled):not(:disabled):active,.ui-btn-primary.active {
	background:#1ca7da;
	border-color:#1ca7da;
	color:rgba(255,255,255,.5);
	-webkit-background-clip:padding-box;
	background-clip:padding-box
}
.ui-btn-danger {
	background-color:#f75549;
	background-image:-webkit-gradient(linear,left top,left bottom,color-stop(0.5,#fc6156),to(#f75549));
	color:#fff;
	border-color:#f43d30;
	-webkit-background-clip:padding-box;
	background-clip:padding-box
}
.ui-btn-danger:not(.disabled):not(:disabled):active,.ui-btn-danger.active {
	background:#e2574d;
	border-color:#e2574d;
	color:rgba(255,255,255,.5);
	-webkit-background-clip:padding-box;
	background-clip:padding-box
}
.ui-btn.disabled,.disabled.ui-btn-lg,.ui-btn:disabled,.ui-btn-lg:disabled {
	border:1px solid #e9ebec;
	color:#ccc;
	background:#e9ebec;
	text-shadow:none;
	-webkit-background-clip:padding-box;
	background-clip:padding-box
}
.ui-btn-lg {
	font-size:18px;
	height:44px;
	line-height:42px;
	display:block;
	width:100%;
	border-radius:5px
}
.ui-btn-wrap {
	padding:20px 15px
}
@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-btn,.ui-btn-lg,.ui-btn.disabled,.disabled.ui-btn-lg,.ui-btn:disabled,.ui-btn-lg:disabled {
	border:0
}
.ui-btn:before,.ui-btn-lg:before {
	content:"";
	width:200%;
	height:200%;
	position:absolute;
	top:0;
	left:0;
	border:1px solid #cacccd;
	border-radius:8px;
	-webkit-transform:scale(0.5);
	-webkit-transform-origin:0 0;
	padding:1px;
	-webkit-box-sizing:border-box
}
.ui-btn-lg:before {
	border-radius:12px
}
.ui-btn-primary:before {
	border:1px solid #0baae4
}
.ui-btn-danger:before {
	border:1px solid #f43d30
}
.ui-btn.disabled:before,.disabled.ui-btn-lg:before,.ui-btn:disabled:before,.ui-btn-lg:disabled:before {
	border:1px solid #e9ebec
}
}.ui-btn-group,.ui-btn-group-tiled {
	display:-webkit-box;
	width:100%;
	-webkit-box-sizing:border-box;
	box-sizing:border-box
}
.ui-btn-group button,.ui-btn-group-tiled button {
	display:block;
	-webkit-box-flex:1
}
.ui-btn-group-tiled .ui-btn:first-child,.ui-btn-group-tiled .ui-btn-lg:first-child {
	margin-right:15px
}
.ui-btn-group button {
	color:#00a5e0;
	line-height:50px;
	height:48px;
	text-align:center;
	font-size:16px;
	border-right:1px #c8c7cc solid;
	width:100%;
	border-top:1px solid #c8c7cc
}
.ui-btn-group button:active {
	background-color:rgba(0,0,0,.1)
}
.ui-btn-group button:last-child {
	border-right:0
}
.ui-btn-group-bottom {
	position:fixed;
	left:0;
	bottom:0;
	z-index:10;
	background-color:#fff
}
.ui-btn-group-bottom button {
	color:#00a5e0;
	background-image:-webkit-gradient(linear,left top,left bottom,color-stop(0,#f9f9f9),to(#e0e0e0))
}
.ui-btn-group-bottom button:active {
	background-color:none;
	color:rgba(0,165,224,.4)
}
@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-btn-group button {
	position:relative;
	border:0
}
.ui-btn-group button:before {
	content:"";
	position:absolute;
	-webkit-transform-origin:right top;
	width:0;
	border-right:1px solid #c8c7cc;
	right:0;
	top:0;
	bottom:0;
	-webkit-transform:scaleX(0.5)
}
.ui-btn-group button:after {
	content:"";
	position:absolute;
	-webkit-transform-origin:right top;
	left:0;
	right:0;
	height:0;
	border-top:1px solid #c8c7cc;
	top:0;
	-webkit-transform:scaleY(0.5)
}
.ui-btn-group button:last-child:before {
	border-right:0
}
}.ui-dialog {
	position:fixed;
	top:0;
	left:0;
	width:100%;
	height:100%;
	z-index:99999;
	display:-webkit-box;
	-webkit-box-orient:horizontal;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	background:rgba(0,0,0,.4);
	display:none;
	color:#000
}
.ui-dialog-notice {
	background:transparent

}
.ui-dialog.show {
	display:-webkit-box;
	display:box
}
.ui-dialog .ui-dialog-cnt {
	border-radius:6px;
	width:270px;
	-webkit-background-clip:padding-box;
	background-clip:padding-box;
	outline:0;
	pointer-events:auto;
	background-color:rgba(253,253,253,.95);
	position:relative
}
.ui-dialog .ui-dialog-bd {
	min-height:71px;
	border-top-left-radius:6px;
	border-top-right-radius:6px;
	padding:18px;
	font-size:16px;
	display:-webkit-box;
	display:box;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	-webkit-box-orient:vertical
}
.ui-dialog .ui-dialog-bd h4 {
	margin-bottom:4px;
	font-size:16px;
	width:100%;
	text-align:center
}
.ui-dialog .ui-dialog-bd div {
	width:100%
}
.ui-dialog .ui-dialog-ft {
	border-bottom-left-radius:6px;
	border-bottom-right-radius:6px
}
.ui-dialog .ui-dialog-ft button {
	height:42px;
	line-height:42px;
	background:transparent
}
.ui-dialog .ui-dialog-ft button:active {
	background:rgba(0,0,0,.1)
}
.ui-dialog .ui-dialog-ft button:first-child {
	border-bottom-left-radius:6px;
	background-image:none
}
.ui-dialog .ui-dialog-ft button:last-child {
	border-bottom-right-radius:6px
}
.ui-dialog-notice .ui-dialog-cnt {
	width:130px;
	height:110px;
	display:-webkit-box;
	-webkit-box-orient:vertical;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	text-align:center;
	background:rgba(0,0,0,.65);
	border-radius:6px;
	color:#fff
}
.ui-tips {
	padding:20px 15px;
	text-align:center;
	font-size:16px;
	color:gray
}
.ui-tips i {
	display:inline-block;
	width:23px;
	height:1px;
	position:relative;
	overflow:visible;
	margin-right:8px;
	margin-left:0;
	font-size:20px;
	vertical-align:middle
}
.ui-tips i:before {
	background:url(../images/icon.png) no-repeat -25px 0;
	-webkit-background-size:150px auto;
	content:"";
	display:block;
	width:23px;
	height:23px;
	position:absolute;
	left:0;
	top:-13px
}
.ui-tips-success i:before {
	background-position:0 -25px
}
.ui-tips-warn i:before {
	background-position:-50px 0
}
.ui-tips-success {
	color:#000
}
.ui-poptips {
	width:100%;
	position:fixed;
	top:0;
	left:0;
	z-index:10
}

.ui-poptips .ui-poptips-cnt {
	margin:0 10px;
	background-color:rgba(0,0,0,.6);
	line-height:40px;
	height:40px;
	color:#fff;
	font-size:16px;
	text-align:center;
	border-bottom-left-radius:3px;
	border-bottom-right-radius:3px;
	overflow:hidden
}
.ui-poptips i {
	display:inline-block;
	width:20px;
	height:1px;
	position:relative;
	overflow:visible;
	margin-right:8px;
	margin-left:0;
	font-size:20px;
	vertical-align:middle
}
.ui-poptips i:before {
	background:url(../images/icon.png) no-repeat 0 0;
	-webkit-background-size:150px auto;
	content:"";
	display:block;
	width:20px;
	height:20px;
	position:absolute;
	left:0;
	top:-11px
}
.ui-poptips-info i:before {
	background-position:0 -50px
}
.ui-poptips-success i:before {
	background-position:-25px -50px
}
.ui-poptips-warn i:before {
	background-position:-50px -50px
}
.ui-tooltips {
	width:100%;
	position:relative;
	z-index:10
}
.ui-tooltips-cnt {
	margin:0;
	color:#00a5e0;
	line-height:45px;
	height:45px;
	text-align:left;
	padding-left:10px;
	font-size:16px;
	overflow:hidden
}
.ui-tooltips-warn .ui-tooltips-cnt {
	background-color:rgba(255,242,183,.95);
	color:#000
}
.ui-tooltips-guide .ui-tooltips-cnt {
	background-color:rgba(255,255,255,.95)
}
.ui-tooltips-state .ui-tooltips-cnt {
	background-color:rgba(205,242,255,.95)
}
.ui-tooltips-cnt-link:after {
	background:url(../images/icon.png) no-repeat -75px 0;
	-webkit-background-size:150px auto;
	content:"";
	display:block;
	width:8px;
	height:14px;
	position:absolute;
	right:10px;
	top:50%;
	margin-top:-8px
}
.ui-tooltips-warn i {
	display:inline-block;
	width:23px;
	height:1px;
	position:relative;
	overflow:visible;
	margin-right:8px;
	vertical-align:middle
}
.ui-tooltips-warn i:before {
	background:url(../images/icon.png) no-repeat -50px 0;
	-webkit-background-size:150px auto;
	content:"";
	display:block;
	width:23px;
	height:23px;
	position:absolute;
	left:0;
	top:-14px
}
.ui-tooltips .ui-btn,.ui-tooltips .ui-btn-lg {
	position:absolute;
	top:50%;
	right:10px;
	margin-top:-15px
}
.ui-tooltips-state .ui-tooltips-cnt-link:after {
	background-position:-125px 0
}
.ui-tips-news-wrap {
	padding:15px 5px;
	text-align:center
}
.ui-tips-news {
	background:rgba(0,0,0,.5);
	position:relative;
	height:36px;
	line-height:36px;
	display:inline-block;
	padding:0 25px 0 50px;
	border-radius:3px;
	font-size:14px;
	color:#fff
}
.ui-tips-news:before {
	content:"";
	width:20px;
	height:18px;
	overflow:hidden;
	display:block;
	position:absolute;
	background:url(../images/icon.png) no-repeat -100px -50px;
	-webkit-background-size:150px auto;
	left:25px;
	top:9px;
	z-index:1
}
.ui-tips-news:active {
	opacity:.7
}
.ui-tab-nav {
	width:100%;
	background-color:#fff;
	display:box;
	display:-webkit-box;
	font-size:16px;
	height:45px;
	-webkit-box-sizing:border-box;
	box-sizing:border-box
}
.ui-tab-content {
	margin-top:45px;
	display:-webkit-box
}
.ui-tab-content>li {
	-webkit-box-flex:1;
	width:100%
}
.ui-tab-nav li {
	height:45px;
	line-height:45px;
	min-width:70px;
	box-flex:1;
	-webkit-box-flex:1;
	text-align:center;
	color:gray;
	-webkit-box-sizing:border-box;
	box-sizing:border-box;
	font-size: 14px;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #F6F6F6;
}
.ui-tab-nav li.current {
	color:#00bb9c;
	border-bottom-width: 2px;
	border-bottom-style: solid;
	border-bottom-color: #00bb9c;
}
.ui-tab-nav li:active {
	opacity:.8
}
.ui-table {
	width:100%;
	border-collapse:collapse
}
.ui-table th {
	font-weight:500
}
.ui-table td,.ui-table th {
	border-bottom:1px solid #dedfe0;
	border-right:1px solid #dedfe0
}
@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-table td,.ui-table th {
	position:relative;
	border-right:0;
	border-bottom:0
}
.ui-table td:before,.ui-table td:after,.ui-table th:before,.ui-table th:after {
	content:"";
	position:absolute;
	-webkit-transform-origin:right bottom
}
.ui-table td:before,.ui-table th:before {
	width:0;
	border-right:1px solid #dedfe0;
	right:0;
	top:0;
	bottom:0;
	-webkit-transform:scaleX(0.5)
}
.ui-table td:after,.ui-table th:after {
	left:0;
	right:0;
	height:0;
	border-bottom:1px solid #dedfe0;
	bottom:0;
	-webkit-transform:scaleY(0.5)
}
.ui-table tr td:last-child:before,.ui-table tr th:last-child:before {
	border-right:0
}
.ui-table tr:last-child td:after {
	border-bottom:0
}
.ui-table td.no-content:after {
	border-bottom:0
}
}.ui-table tr td:last-child,.ui-table tr th:last-child {
	border-right:0
}
.ui-table tr:last-child td {
	border-bottom:0
}
.ui-table td.no-content {
	border-bottom:0
}
.ui-list {
	background-color:#fff
}
.ui-list>li {
	position:relative;
	display:-webkit-box;
	display:box;
}
.ui-list-text>li {
	line-height:20px;
	padding:12px 25px 12px 0;
	font-size:16px
}
.ui-list-text>li>p {
	overflow:hidden;
	white-space:nowrap;
	text-overflow:ellipsis;
	max-width:100%
}
.ui-list li {
	background-image:none
}
.ui-list li .ui-border-t {
	border-top:0;
	background-image:none
}
.ui-list-thumb {
	width:50px;
	height:50px;
	margin:7px 15px 7px 0;
	position:relative
}
.ui-list .ui-avatar,.ui-list .ui-avatar-s,.ui-list .ui-avatar-lg {
	margin:7px 15px 7px 0
}
.ui-list-thumb-s {
	position:relative;
	width:28px;
	height:28px;
	-webkit-background-size:28px auto;
	margin:11px 15px 11px 0;
	background-image:url(../images/default.png)
}
.ui-list-thumb span,.ui-list-thumb-s span {
	width:100%;
	height:100%;
	-webkit-background-size:100% auto;
	display:block;
	position:absolute;
	left:0;
	top:0;
	z-index:1
}
.ui-list-info {
	-webkit-box-flex:1;
	box-flex:1;
	display:box;
	display:-webkit-box;
	-webkit-box-orient:vertical;
	-webkit-box-pack:center
}
.ui-list-info>h4 {
	font-size:14px;
	line-height:20px;
	font-weight:400;
	overflow:hidden;
	white-space:nowrap;
	text-overflow:ellipsis;
	max-width:100%;
	height:20px
}
.ui-list-info>p {
	font-size:12px;
	line-height:20px;
	overflow:hidden;
	white-space:nowrap;
	text-overflow:ellipsis;
	max-width:100%;
	color:gray
}
.ui-list .ui-btn,.ui-list .ui-btn-lg {
	position:absolute;
	top:50%;
	right:15px;
	margin-top:-15px
}
.ui-list-checkbox .ui-checkbox {
	position:absolute;
	top:50%;
	left:0;
	margin-top:-13px
}
.ui-list-checkbox>li {
	padding-left:40px
}
.ui-list .ui-badge,.ui-list .ui-badge-muted,.ui-list-action {
	position:absolute;
	right:15px;
	top:50%;
	margin-top:-10px
}
.ui-list-link .ui-badge,.ui-list-link .ui-badge-muted,li.ui-list-item-link .ui-badge,li.ui-list-item-link .ui-badge-muted,.ui-list-link .ui-list-action,li.ui-list-item-link .ui-list-action {
	right:35px
}
.ui-list-active>li:active,.ui-list-link>li:active,li.ui-list-item-link:active {
	background-color:#f8f9fa;
	padding-left:15px;
	margin-left:0
}
.ui-list-active>li:active,.ui-list-active>li:active .ui-border-t,.ui-list-active>li:active+li.ui-border-t,.ui-list-active>li:active+.ui-border-t,.ui-list-link>li:active,.ui-list-link>li:active .ui-border-t,.ui-list-link>li:active+li.ui-border-t,.ui-list-link>li:active+.ui-border-t,li.ui-list-item-link:active,li.ui-list-item-link:active .ui-border-t,li.ui-list-item-link:active+li.ui-border-t,li.ui-list-item-link:active+.ui-border-t {
	background-image:none;
	border-top-color:#f8f9fa
}
@media screen and (-webkit-min-device-pixel-ratio:2) {
	.ui-list-active>li:active,.ui-list-active>li:active .ui-border-t,.ui-list-active>li:active+li.ui-border-t,.ui-list-active>li:active+.ui-border-t,.ui-list-link>li:active,.ui-list-link>li:active .ui-border-t,.ui-list-link>li:active+li.ui-border-t,.ui-list-link>li:active+.ui-border-t,li.ui-list-item-link:active,li.ui-list-item-link:active .ui-border-t,li.ui-list-item-link:active+li.ui-border-t,li.ui-list-item-link:active+.ui-border-t {
	background-image:none
}
}.ui-list-function .ui-list-info {
	padding-right:70px
}
.ui-list-cover li {
	padding-left:15px;
	margin-left:0
}
.ui-list-link li:after,li.ui-list-item-link:after {
	background:url(../images/icon.png) no-repeat 0 -75px;
	-webkit-background-size:150px auto;
	content:"";
	display:block;
	width:9px;
	height:15px;
	position:absolute;
	right:15px;
	top:50%;
	margin-top:-7px
}
.ui-list-link .ui-list-info,.ui-list-item-link .ui-list-info,.ui-list-text .ui-list-info {
	padding-right:35px
}
.ui-list-text.ui-list-link .ui-list-info,.ui-list-text .ui-list-item-link .ui-list-info {
	padding-right:70px
}
.ui-list li.selected {
	color:#00a5e0
}
.ui-list li.selected:after {
	content:"";
	display:block;
	width:14px;
	height:12px;
	background:url(../images/icon.png) no-repeat -100px 0;
	-webkit-background-size:150px auto;
	position:absolute;
	right:15px;
	top:50%;
	margin-top:-4px
}
.ui-loading-wrap {
	display:-webkit-box;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	text-align:center;
	height:40px
}
.ui-loading {
	width:20px;
	height:20px;
	display:block;
	background:url(../images/loading_sprite.png);
	-webkit-background-size:auto 20px
}
.ui-loading-bright {
	width:37px;
	height:37px;
	display:block;
	background-image:url(../images/loading_sprite_white.png);
	-webkit-background-size:auto 37px
}
.ui-loading-wrap .ui-loading {
	margin:10px
}
.ui-notice {
	width:100%;
	height:100%;
	z-index:999;
	display:-webkit-box;
	-webkit-box-orient:vertical;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	position:absolute;
	text-align:center
}
.ui-notice i {
	display:block;
	-webkit-background-size:100px 100px;
	width:100px;
	height:100px;
	margin-bottom:20px;
	background-image:url(../images/icon_warn.png)
}
.ui-notice-news i {
	background-image:url(../images/icon_warn_news.png)
}
.ui-notice-nearby i {
	background-image:url(../images/icon_warn_nearby.png)
}

.ui-notice p {
	font-size:16px;
	line-height:20px;
	color:#a6a6a6;
	text-align:center;
	padding:0 15px
}
.ui-notice-warn {
	width:100%;
	height:150px;
	z-index:999;
	display:-webkit-box;
	-webkit-box-orient:vertical;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	text-align:center;
	color:#a6a6a6
}
.ui-notice-warn i {
	margin-bottom:10px
}
.ui-notice-btn {
	width:100%;
	-webkit-box-sizing:border-box;
	padding:50px 15px
}
.ui-notice-btn button {
	margin:10px 0
}
.ui-form {
	background-color:#fff
}
.ui-form ::-webkit-input-placeholder {
	color:#a6a6a6
}
.ui-form .ui-icon-close {
	top:7px;
	right:7px
}
.ui-form-item-order:active {
	background-color:#e5e6e7
}
.ui-form-item {
	height:44px;
	font-size:16px;
	line-height:44px;
	vertical-align:middle;
	padding-right:40px;
	position:relative;
	padding-left:15px
}
.ui-form-item label:not(.ui-switch):not(.ui-checkout):not(.ui-radio) {
	width:95px;
	position:absolute;
	text-align:left;
	-webkit-box-sizing:border-box;
	box-sizing:border-box
}
.ui-form-item input:not([type=checkbox]),.ui-form-item textarea {
	width:100%;
	padding-left:95px;
	-webkit-box-sizing:border-box;
	box-sizing:border-box
}
.ui-form-item-textarea {
	height:65px
}
.ui-form-item-textarea label {
	vertical-align:top
}
.ui-form-item-textarea textarea {
	margin-top:15px;
	border:0
}
.ui-form-item-textarea textarea:focus {
	outline:0
}
.ui-form-item-l label,.ui-form-item-r button {
	color:#00a5e0;
	text-align:center
}
.ui-form-item-r .ui-icon-close {
	right:125px
}
.ui-form .ui-form-item-l input {
	padding-left:115px;
	-webkit-box-sizing:border-box;
	box-sizing:border-box
}
.ui-form-item-r {
	padding-right:0
}
.ui-form .ui-form-item-r input {
	padding:0 150px 0 0;
	-webkit-box-sizing:border-box;
	box-sizing:border-box
}
.ui-form-item-r button {
	width:110px;
	height:44px;
	font-size:16px;
	position:absolute;
	top:0;
	right:0
}
.ui-form-item-r button.disabled {
	color:#a6a6a6
}
.ui-form-item-r button:not(.disabled):active {
	background-color:#e5e6e7
}
.ui-form .ui-form-item-pure input {
	padding-left:0
}
.ui-form .ui-form-item-show label {
	color:gray
}
.ui-form-item-link:after {
	background:url(../images/icon.png) no-repeat 0 -75px;
	-webkit-background-size:150px auto;
	content:"";
	display:block;
	width:9px;
	height:15px;
	position:absolute;
	right:15px;
	top:50%;
	margin-top:-7px
}
.ui-checkbox input {
	display:inline-block;
	width:25px;
	height:1px;
	position:relative;
	overflow:visible;
	border:0;
	background:0 0;
	-webkit-appearance:none;
	outline:0;
	vertical-align:middle
}
.ui-checkbox input:before {
	background:url(../images/icon.png) no-repeat -50px -25px;
	-webkit-background-size:150px auto;
	content:"";
	display:block;
	width:25px;
	height:25px;
	position:absolute;
	left:0;
	top:-10px
}
.ui-checkbox input:checked:before {
	background-position:-25px -25px
}
.ui-checkbox-s {
	width:19px
}
.ui-checkbox-s input:before {
	width:19px;
	height:19px;
	background-position:-100px -25px
}
.ui-checkbox-s input:checked:before {
	background-position:-75px -25px
}
.ui-form-item-switch {
	height:44px;
	line-height:44px;
	position:relative
}
.ui-switch {
	position:absolute;
	font-size:16px;
	right:15px;
	top:6px;
	width:52px;
	height:32px;
	line-height:1em
}
.ui-switch input {
	width:52px;
	height:32px;
	position:absolute;
	z-index:10;
	border:0;
	background:0 0;
	-webkit-appearance:none;
	outline:0
}
.ui-switch input:before {
	content:'';
	width:50px;
	height:30px;
	border:1px solid #dfdfdf;
	background-color:#fdfdfd;
	border-radius:20px;
	cursor:pointer;
	display:inline-block;
	position:relative;
	vertical-align:middle;
	-webkit-user-select:none;
	user-select:none;
	-webkit-box-sizing:content-box;
	box-sizing:content-box;
	border-color:#dfdfdf;
	-webkit-box-shadow:#dfdfdf 0 0 0 0 inset;
	box-shadow:#dfdfdf 0 0 0 0 inset;
	-webkit-transition:border .4s,-webkit-box-shadow .4s;
	transition:border .4s,box-shadow .4s;
	-webkit-background-clip:content-box;
	background-clip:content-box
}
.ui-switch input:checked:before {
	border-color:#64bd63;
	-webkit-box-shadow:#64bd63 0 0 0 16px inset;
	box-shadow:#64bd63 0 0 0 16px inset;
	background-color:#64bd63;
	transition:border .4s,box-shadow .4s,background-color 1.2s;
	-webkit-transition:border .4s,-webkit-box-shadow .4s,background-color 1.2s;
	background-color:#64bd63
}
.ui-switch input:checked:after {
	left:21px
}
.ui-switch input:after {
	content:'';
	width:30px;
	height:30px;
	position:absolute;
	top:1px;
	left:0;
	border-radius:100%;
	background-color:#fff;
	-webkit-box-shadow:0 1px 3px rgba(0,0,0,.4);
	box-shadow:0 1px 3px rgba(0,0,0,.4);
	-webkit-transition:left .2s;
	transition:left .2s
}
.ui-radio {
	line-height:26px;
	display:inline-block;
	margin:5px 0
}
.ui-radio input {
	display:inline-block;
	width:24px;
	height:1px;
	position:relative;
	overflow:visible;
	border:0;
	background:0 0;
	-webkit-appearance:none;
	outline:0;
	margin-right:8px;
	vertical-align:middle
}
.ui-radio input:before {
	content:'';
	display:block;
	width:24px;
	height:24px;
	border:1px solid #dfe0e1;
	border-radius:13px;
	-webkit-background-clip:padding-box;
	background-clip:padding-box;
	position:absolute;
	left:0;
	top:-14px
}
.ui-radio input:checked:after {
	content:'';
	display:block;
	width:14px;
	height:14px;
	background:#1cb7f0;
	border-radius:7px;
	position:absolute;
	left:6px;
	top:-8px
}
.ui-searchbar-wrap {
	display:-webkit-box;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	background-color:#f8f9fa
}
.ui-searchbar-wrap button {
	margin-right:10px
}
.ui-searchbar-wrap .ui-searchbar-cancel {
	color:gray;
	font-size:16px;
	padding:4px 8px
}
.ui-searchbar-wrap .ui-searchbar-input,.ui-searchbar-wrap button,.ui-searchbar-wrap .ui-icon-close {
	display:none
}
.ui-searchbar-wrap.focus {
	-webkit-box-pack:start
}
.ui-searchbar-wrap.focus .ui-searchbar-input,.ui-searchbar-wrap.focus button,.ui-searchbar-wrap.focus .ui-icon-close {
	display:block
}
.ui-searchbar-wrap.focus .ui-searchbar-text {
	display:none
}
.ui-searchbar {
	border-radius:4px;
	height:30px;
	margin:7px 10px;
	background:#fff;
	font-size:14px;
	line-height:30px;
	position:relative;
	padding-left:4px;
	-webkit-box-flex:1;
	display:-webkit-box;
	-webkit-box-pack:center;
	-webkit-box-align:center;
	color:gray;
	width:100%
}
.ui-searchbar input {
	-webkit-appearance:none;
	font-size:14px;
	color:#000;
	border:0;
	width:100%;
	padding:7px 0
}
.ui-searchbar-input {
	-webkit-box-flex:1
}
.ui-slider {
	width:100%;
	overflow:hidden;
	position:relative;
	padding-top:62.5%
}
.ui-slider-content {
	display:-webkit-box;
	width:300%;
	position:absolute;
	left:0;
	top:0
}
.ui-slider-content>li {
	-webkit-box-flex:1;
	width:100%
}
.ui-slider-content>li img {
	width:100%
}
.ui-slider-indicators {
	position:absolute;
	bottom:0;
	right:10px
}
.ui-slider-indicators li {
	display:inline-block;
	text-indent:100%;
	white-space:nowrap;
	overflow:hidden;
	width:8px;
	height:8px;
	background-color:#a6a6a6;
	border-radius:10px
}
.ui-slider-indicators li.current {
	background-color:#ff4040
}
.ui-loading {
	-webkit-animation:rotate 1s steps(12) infinite
}
.ui-loading-bright {
	-webkit-animation:rotate2 1s steps(12) infinite
}
.ui-icon-refresh.loading {
	-webkit-animation:rotate3 1s infinite linear
}
@-webkit-keyframes rotate {
	from {
	background-position:0 0
}
to {
	background-position:-240px 0
}
}@-webkit-keyframes rotate2 {
	from {
	background-position:0 0
}
to {
	background-position:-444px 0
}
}@-webkit-keyframes rotate3 {
	0% {
	-webkit-transform:rotate(0)
}
100% {
	-webkit-transform:rotate(360deg)
}
}
