﻿@charset "utf-8";

/*header*/
.header{ height:44px; background:#4eabe8;position: relative;z-index: 10;}
/* CSS Document */

.main {
	padding-top: 15px;
	padding-bottom: 15px;
}
.main #frm_login {
	padding-right: 15px;
	padding-left: 15px;
}

.main .item {
	margin-bottom: 15px;
	position: relative;
	width: 100%;
	margin-top: 15px;
}
.main .item .txt-username,
.main .item .txt-password,
.main .item .txt-password_PwdTwo,
.main .item .txt-password_PwdThree,
.main .item .txt-captcha{
	font-family: "Microsoft YaHei", Arial, Helvetica, sans-serif;
	margin-bottom: 10px;
	padding-left: 10px;
}
.main .item .txt-phone {text-indent:80px;}
.main .item .txt-input {
	background: none repeat scroll 0 0 #fff;
	border: 1px solid #D1D1D1;
	border-radius:5px;
	color: #252525;
	font-size: 14px;
	height: 44px;
	width: 100%;
}

.main .item .txt-inputyzm {
	background: none repeat scroll 0 0 #fff;
	border: 1px solid #D1D1D1;
	border-radius:5px;
	color: #252525;
	font-size: 14px;
	height: 44px;
	width: 50%;
}

.main .item-username .input-close {right: 7px;}
.main .item .input-close {
	cursor: pointer;
	display: block;
	height: 25px;
	position: absolute;
	top: 10px;
	width: 25px;
	background-attachment: scroll;
	background-image: url(../images/u_close.png);
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 25px;
}
.main .item-password .btn-off {background: url("../images/u_s1.png") no-repeat scroll 0 0 / 51px 35px;}
.main .item-password .tp-btn {cursor: pointer;display: block;height: 35px;position: absolute; right: 10px;top: 8px; width: 51px; z-index: 1;}
.main .item-password .btn-on {background: url("../images/u_s2.png") no-repeat scroll 0 0 / 51px 35px;}
.main .item-password .btn-off_PwdTwo {background: url("../images/u_s1.png") no-repeat scroll 0 0 / 51px 35px;}
.main .item-password .tp-btn_PwdTwo {cursor: pointer;display: block;height: 35px;position: absolute; right: 10px;top: 8px; width: 51px; z-index: 1;}
.main .item-password .btn-on_PwdTwo {background: url("../images/u_s2.png") no-repeat scroll 0 0 / 51px 35px;}
.main .item-password .btn-off_PwdThree {background: url("../images/u_s1.png") no-repeat scroll 0 0 / 51px 35px;}
.main .item-password .tp-btn_PwdThree {cursor: pointer;display: block;height: 35px;position: absolute; right: 10px;top: 8px; width: 51px; z-index: 1;}
.main .item-password .btn-on_PwdThree {background: url("../images/u_s2.png") no-repeat scroll 0 0 / 51px 35px;}
.main .item-login-option {
	font-size: 14px;
	margin-bottom:5px;
	height: 30px;
}
.main  .item-login-option  .aoutlogin  {
	float: left;
	margin-left: 10px;
	font-size: 12px;
	color: #666666;
}

.main .item-login-option .retrieve-password {
	float: right;
	text-align: right;
	width: 50%;
	margin-right: 10px;
}
.main .item-login-option .retrieve-password a {
	color: #4eabe8;
	font-size: 12px;
	text-decoration: none;
}
.main .item-login-other dt {
	color: #666;
	font-size: 12px;
	margin-bottom: 13px;
}
.main   .item-login-other   dd {
	margin: 0px;
}

.main .item-login-other dd .qq {width: 20%;}
.main .item-login-other dd a {float: left;}
.main .item-login-other dd a span {display:block;height: 40px;overflow: hidden; width: 40px;}
.ui-btn-wrap { padding-top:5px; padding-bottom:10px;}
.ui-btn-lg {
	cursor:pointer;
	border-radius:5px;
	display: block;
	font-size: 14px;
	height: 40px;
	line-height: 40px;
	width: 100%;
	text-align:center;
	text-decoration: none;
}
.ui-btn-primary {
	background-clip: padding-box;
	background-color: #4eabe8;
	border-color: #4eabe8;
	color: #fff;
	text-decoration: none;
}
.ui-btn-danger{background-clip: padding-box;background-color: #de3b8a;border-color: #de3b8a;color: #fff;}
.ui-btn-danger:visited{background-clip: padding-box;background-color: #de3b8a;border-color: #de3b8a;color: #fff;}
.main .item-captcha {margin-bottom: 10px;}
.main .item-captcha .input-info {margin-bottom: 10px;}
.main .item-captcha .input-close {right: 79px;}
.main .item-captcha #captcha-img { border-left: 1px solid #d7d7d7;height: 25px; padding-left: 7px; position: absolute;right: 0;top: 9px;width: 79px;}
.main .item-captcha .err-tips {
	color: #848689;
	font-size: 12px;
	margin: 13px 0 0px;
}
.main .item-captcha .err-tips a {
	color: #0052ce;
	text-decoration: none;
}
.register_verify {
	margin-top: 20px;
	margin-bottom: 20px;
	text-align: center;
}
.register_verify .ok_iocn {
}
.register_verify h1 {
	font-size: 20px;
	line-height: 30px;
	font-weight: bold;
	color: #f23436;
	margin-top: 20px;
}


.register_verify h2 {
	line-height: 50px;
	color: #333333;
	font-size: 14px;
}
.register_verify h3 {
	color: #333333;
	margin-bottom: 20px;
}
.register_verify p {
	margin-top: 20px;
}
.register_verify p a {
	margin-right: 20px;
	margin-left: 20px;
	font-size: 16px;
	margin-top: 10px;
}
