.media {
    margin-top: 15px;
}
.media:first-child {
    margin-top: 0;
}
.media, .media-body {
    overflow: hidden;
    zoom: 1;
}
.media-body {
    width: 10000px;
}
.media-object {
    display: block;
}
.media-right, .media>.pull-right {
    padding-left: 10px;
}
.media-left, .media>.pull-left {
    padding-right: 10px;
}
.media-left, .media-right, .media-body {
    display: table-cell;
    vertical-align: top;
}
.media-middle {
    vertical-align: middle;
}
.media-bottom {
    vertical-align: bottom;
}
.media-heading {
    margin-top: 0;
    margin-bottom: 5px;
}
.media-list {
    padding-left: 0;
    list-style: none;
}
.list-group {
    padding-left: 0;
    margin-bottom: 20px;
}
.list-group-item {
    position: relative;
    display: block;
    padding: 10px 15px;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid #ddd;
}
.list-group-item:first-child {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}
.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
}
a.list-group-item {
    color: #555;
}
a.list-group-item .list-group-item-heading {
    color: #333;
}
a.list-group-item:hover, a.list-group-item:focus {
    color: #555;
    text-decoration: none;
    background-color: #f5f5f5;
}
.list-group-item.disabled, .list-group-item.disabled:hover, .list-group-item.disabled:focus {
    color: #777;
    cursor: not-allowed;
    background-color: #eee;
}
.list-group-item.disabled .list-group-item-heading, .list-group-item.disabled:hover .list-group-item-heading, .list-group-item.disabled:focus .list-group-item-heading {
    color: inherit;
}
.list-group-item.disabled .list-group-item-text, .list-group-item.disabled:hover .list-group-item-text, .list-group-item.disabled:focus .list-group-item-text {
    color: #777;
}
.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus {
    z-index: 2;
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7;
}
.list-group-item.active .list-group-item-heading, .list-group-item.active:hover .list-group-item-heading, .list-group-item.active:focus .list-group-item-heading, .list-group-item.active .list-group-item-heading>small, .list-group-item.active:hover .list-group-item-heading>small, .list-group-item.active:focus .list-group-item-heading>small, .list-group-item.active .list-group-item-heading>.small, .list-group-item.active:hover .list-group-item-heading>.small, .list-group-item.active:focus .list-group-item-heading>.small {
    color: inherit;
}
.list-group-item.active .list-group-item-text, .list-group-item.active:hover .list-group-item-text, .list-group-item.active:focus .list-group-item-text {
    color: #c7ddef;
}
.list-group-item-success {
    color: #3c763d;
    background-color: #dff0d8;
}
a.list-group-item-success {
    color: #3c763d;
}
a.list-group-item-success .list-group-item-heading {
    color: inherit;
}
a.list-group-item-success:hover, a.list-group-item-success:focus {
    color: #3c763d;
    background-color: #d0e9c6;
}
a.list-group-item-success.active, a.list-group-item-success.active:hover, a.list-group-item-success.active:focus {
    color: #fff;
    background-color: #3c763d;
    border-color: #3c763d;
}
.list-group-item-info {
    color: #31708f;
    background-color: #d9edf7;
}
a.list-group-item-info {
    color: #31708f;
}
a.list-group-item-info .list-group-item-heading {
    color: inherit;
}
a.list-group-item-info:hover, a.list-group-item-info:focus {
    color: #31708f;
    background-color: #c4e3f3;
}
a.list-group-item-info.active, a.list-group-item-info.active:hover, a.list-group-item-info.active:focus {
    color: #fff;
    background-color: #31708f;
    border-color: #31708f;
}
.list-group-item-warning {
    color: #8a6d3b;
    background-color: #fcf8e3;
}
a.list-group-item-warning {
    color: #8a6d3b;
}
a.list-group-item-warning .list-group-item-heading {
    color: inherit;
}
a.list-group-item-warning:hover, a.list-group-item-warning:focus {
    color: #8a6d3b;
    background-color: #faf2cc;
}
a.list-group-item-warning.active, a.list-group-item-warning.active:hover, a.list-group-item-warning.active:focus {
    color: #fff;
    background-color: #8a6d3b;
    border-color: #8a6d3b;
}
.list-group-item-danger {
    color: #a94442;
    background-color: #f2dede;
}
a.list-group-item-danger {
    color: #a94442;
}
a.list-group-item-danger .list-group-item-heading {
    color: inherit;
}
a.list-group-item-danger:hover, a.list-group-item-danger:focus {
    color: #a94442;
    background-color: #ebcccc;
}
a.list-group-item-danger.active, a.list-group-item-danger.active:hover, a.list-group-item-danger.active:focus {
    color: #fff;
    background-color: #a94442;
    border-color: #a94442;
}
.list-group-item-heading {
    margin-top: 0;
    margin-bottom: 5px;
}
.list-group-item-text {
    margin-bottom: 0;
    line-height: 1.3;
}
.text-muted {
    color: #999999;
}
a.text-muted:hover, a.text-muted:active, a.text-muted:focus, button.text-muted:hover, button.text-muted:active, button.text-muted:focus {
    color: #999999;
    opacity: .75;
}
.text-primary {
    color: #5c90d2;
}
a.text-primary:hover, a.text-primary:active, a.text-primary:focus, button.text-primary:hover, button.text-primary:active, button.text-primary:focus {
    color: #5c90d2;
    opacity: .75;
}
.text-primary-dark {
    color: #3e4a59;
}
a.text-primary-dark:hover, a.text-primary-dark:active, a.text-primary-dark:focus, button.text-primary-dark:hover, button.text-primary-dark:active, button.text-primary-dark:focus {
    color: #3e4a59;
    opacity: .75;
}
.text-primary-darker {
    color: #2c343f;
}
a.text-primary-darker:hover, a.text-primary-darker:active, a.text-primary-darker:focus, button.text-primary-darker:hover, button.text-primary-darker:active, button.text-primary-darker:focus {
    color: #2c343f;
    opacity: .75;
}
.text-primary-light {
    color: #98b9e3;
}
a.text-primary-light:hover, a.text-primary-light:active, a.text-primary-light:focus, button.text-primary-light:hover, button.text-primary-light:active, button.text-primary-light:focus {
    color: #98b9e3;
    opacity: .75;
}
.text-primary-lighter {
    color: #ccdcf1;
}
a.text-primary-lighter:hover, a.text-primary-lighter:active, a.text-primary-lighter:focus, button.text-primary-lighter:hover, button.text-primary-lighter:active, button.text-primary-lighter:focus {
    color: #ccdcf1;
    opacity: .75;
}
.text-success {
    color: #46c37b;
}
a.text-success:hover, a.text-success:active, a.text-success:focus, button.text-success:hover, button.text-success:active, button.text-success:focus {
    color: #46c37b;
    opacity: .75;
}
.text-warning {
    color: #f3b760;
}
a.text-warning:hover, a.text-warning:active, a.text-warning:focus, button.text-warning:hover, button.text-warning:active, button.text-warning:focus {
    color: #f3b760;
    opacity: .75;
}
.text-info {
    color: #70b9eb;
}
a.text-info:hover, a.text-info:active, a.text-info:focus, button.text-info:hover, button.text-info:active, button.text-info:focus {
    color: #70b9eb;
    opacity: .75;
}
.text-danger {
    color: #d26a5c;
}
a.text-danger:hover, a.text-danger:active, a.text-danger:focus, button.text-danger:hover, button.text-danger:active, button.text-danger:focus {
    color: #d26a5c;
    opacity: .75;
}
.text-success-light {
    color: #e0f5e9;
}
a.text-success-light:hover, a.text-success-light:active, a.text-success-light:focus, button.text-success-light:hover, button.text-success-light:active, button.text-success-light:focus {
    color: #e0f5e9;
    opacity: .75;
}
.text-warning-light {
    color: #fdf3e5;
}
a.text-warning-light:hover, a.text-warning-light:active, a.text-warning-light:focus, button.text-warning-light:hover, button.text-warning-light:active, button.text-warning-light:focus {
    color: #fdf3e5;
    opacity: .75;
}
.text-info-light {
    color: #edf6fd;
}
a.text-info-light:hover, a.text-info-light:active, a.text-info-light:focus, button.text-info-light:hover, button.text-info-light:active, button.text-info-light:focus {
    color: #edf6fd;
    opacity: .75;
}
.text-danger-light {
    color: #f9eae8;
}
a.text-danger-light:hover, a.text-danger-light:active, a.text-danger-light:focus, button.text-danger-light:hover, button.text-danger-light:active, button.text-danger-light:focus {
    color: #f9eae8;
    opacity: .75;
}
.text-white {
    color: #ffffff;
}
a.text-white:hover, a.text-white:active, a.text-white:focus, button.text-white:hover, button.text-white:active, button.text-white:focus {
    color: #ffffff;
    opacity: .75;
}
.text-white-op {
    color: rgba(255, 255, 255, 0.85);
}
a.text-white-op:hover, a.text-white-op:active, a.text-white-op:focus, button.text-white-op:hover, button.text-white-op:active, button.text-white-op:focus {
    color: rgba(255, 255, 255, 0.85);
    opacity: .75;
}
.text-black {
    color: #000000;
}
a.text-black:hover, a.text-black:active, a.text-black:focus, button.text-black:hover, button.text-black:active, button.text-black:focus {
    color: #000000;
    opacity: .75;
}
.text-black-op {
    color: rgba(0, 0, 0, 0.5);
}
a.text-black-op:hover, a.text-black-op:active, a.text-black-op:focus, button.text-black-op:hover, button.text-black-op:active, button.text-black-op:focus {
    color: rgba(0, 0, 0, 0.5);
    opacity: .75;
}
.text-gray {
    color: #c9c9c9;
}
a.text-gray:hover, a.text-gray:active, a.text-gray:focus, button.text-gray:hover, button.text-gray:active, button.text-gray:focus {
    color: #c9c9c9;
    opacity: .75;
}
.text-gray-dark {
    color: #999999;
}
a.text-gray-dark:hover, a.text-gray-dark:active, a.text-gray-dark:focus, button.text-gray-dark:hover, button.text-gray-dark:active, button.text-gray-dark:focus {
    color: #999999;
    opacity: .75;
}
.text-gray-darker {
    color: #393939;
}
a.text-gray-darker:hover, a.text-gray-darker:active, a.text-gray-darker:focus, button.text-gray-darker:hover, button.text-gray-darker:active, button.text-gray-darker:focus {
    color: #393939;
    opacity: .75;
}
.text-gray-light {
    color: #f3f3f3;
}
a.text-gray-light:hover, a.text-gray-light:active, a.text-gray-light:focus, button.text-gray-light:hover, button.text-gray-light:active, button.text-gray-light:focus {
    color: #f3f3f3;
    opacity: .75;
}
.text-gray-lighter {
    color: #f9f9f9;
}
a.text-gray-lighter:hover, a.text-gray-lighter:active, a.text-gray-lighter:focus, button.text-gray-lighter:hover, button.text-gray-lighter:active, button.text-gray-lighter:focus {
    color: #f9f9f9;
    opacity: .75;
}
.css-input {
    position: relative;
    display: inline-block;
    margin: 2px 0;
    font-weight: 400;
    cursor: pointer;
}
.css-input input {
    position: absolute;
    opacity: 0;
}
.css-input input:focus+span {
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.25);
}
.css-input input+span {
    position: relative;
    display: inline-block;
    margin-top: -2px;
    margin-right: 3px;
    vertical-align: middle;
}
.css-input input+span:after {
    position: absolute;
    content: "";
}
.css-checkbox {
    margin: 7px 0;
}
.css-checkbox input+span {
    width: 20px;
    height: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    -webkit-transition: background-color 0.2s;
    transition: background-color 0.2s;
}
.css-checkbox input+span:after {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    font-family: "FontAwesome";
    font-size: 10px;
    color: #fff;
    line-height: 18px;
    content: "\f00c";
    text-align: center;
}
.css-checkbox:hover input+span {
    border-color: #ccc;
}
.css-checkbox.css-checkbox-sm {
    margin: 9px 0 8px;
    font-size: 12px;
}
.css-checkbox.css-checkbox-sm input+span {
    width: 16px;
    height: 16px;
}
.css-checkbox.css-checkbox-sm input+span:after {
    font-size: 8px;
    line-height: 15px;
}
.css-checkbox.css-checkbox-lg {
    margin: 3px 0;
}
.css-checkbox.css-checkbox-lg input+span {
    width: 30px;
    height: 30px;
}
.css-checkbox.css-checkbox-lg input+span:after {
    font-size: 12px;
    line-height: 30px;
}
.css-checkbox.css-checkbox-rounded input+span {
    border-radius: 3px;
}
.css-checkbox-default input:checked+span {
    background-color: #999999;
    border-color: #999999;
}
.css-checkbox-primary input:checked+span {
    background-color: #5c90d2;
    border-color: #5c90d2;
}
.css-checkbox-info input:checked+span {
    background-color: #70b9eb;
    border-color: #70b9eb;
}
.css-checkbox-success input:checked+span {
    background-color: #46c37b;
    border-color: #46c37b;
}
.css-checkbox-warning input:checked+span {
    background-color: #f3b760;
    border-color: #f3b760;
}
.css-checkbox-danger input:checked+span {
    background-color: #d26a5c;
    border-color: #d26a5c;
}
.css-radio {
    margin: 7px 0;
}
.css-radio input+span {
    width: 20px;
    height: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 50%;
}
.css-radio input+span:after {
    top: 2px;
    right: 2px;
    bottom: 2px;
    left: 2px;
    background-color: #fff;
    border-radius: 50%;
    opacity: 0;
    -webkit-transition: opacity 0.2s ease-out;
    transition: opacity 0.2s ease-out;
}
.css-radio input:checked+span:after {
    opacity: 1;
}
.css-radio:hover input+span {
    border-color: #ccc;
}
.css-radio.css-radio-sm {
    margin: 9px 0 8px;
    font-size: 12px;
}
.css-radio.css-radio-sm input+span {
    width: 16px;
    height: 16px;
}
.css-radio.css-radio-lg {
    margin: 5px 0;
}
.css-radio.css-radio-lg input+span {
    width: 26px;
    height: 26px;
}
.css-radio-default input:checked+span:after {
    background-color: #999999;
}
.css-radio-primary input:checked+span:after {
    background-color: #5c90d2;
}
.css-radio-info input:checked+span:after {
    background-color: #70b9eb;
}
.css-radio-success input:checked+span:after {
    background-color: #46c37b;
}
.css-radio-warning input:checked+span:after {
    background-color: #f3b760;
}
.css-radio-danger input:checked+span:after {
    background-color: #d26a5c;
}
.switch {
    margin: 3px 0;
}
.switch input+span {
    width: 54px;
    height: 30px;
    background-color: #eee;
    border-radius: 30px;
    -webkit-transition: background-color 0.4s;
    transition: background-color 0.4s;
}
.switch input+span:after {
    top: 2px;
    bottom: 2px;
    left: 2px;
    width: 26px;
    background-color: #fff;
    border-radius: 50%;
    -webkit-box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);
    box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);
    -webkit-transition: -webkit-transform 0.15s ease-out;
    transition: transform 0.15s ease-out;
}
.switch input:checked+span {
    background-color: #ddd;
}
.switch input:checked+span:after {
    -webkit-box-shadow: -2px 0 3px rgba(0, 0, 0, 0.2);
    box-shadow: -2px 0 3px rgba(0, 0, 0, 0.2);
    -webkit-transform: translateX(23px);
    -ms-transform: translateX(23px);
    transform: translateX(23px);
}
.switch.switch-sm {
    margin: 8px 0 7px;
    font-size: 12px;
}
.switch.switch-sm input+span {
    width: 36px;
    height: 20px;
}
.switch.switch-sm input+span:after {
    width: 16px;
}
.switch.switch-sm input:checked+span:after {
    -webkit-transform: translateX(15px);
    -ms-transform: translateX(15px);
    transform: translateX(15px);
}
.switch.switch-lg {
    margin: 1px 0;
}
.switch.switch-lg input+span {
    width: 70px;
    height: 34px;
}
.switch.switch-lg input+span:after {
    width: 30px;
}
.switch.switch-lg input:checked+span:after {
    -webkit-transform: translateX(35px);
    -ms-transform: translateX(35px);
    transform: translateX(35px);
}
.switch.switch-square input+span, .switch.switch-square input+span:after {
    border-radius: 0;
}
.switch-default input:checked+span {
    background-color: #999999;
}
.switch-primary input:checked+span {
    background-color: #5c90d2;
}
.switch-info input:checked+span {
    background-color: #70b9eb;
}
.switch-success input:checked+span {
    background-color: #46c37b;
}
.switch-warning input:checked+span {
    background-color: #f3b760;
}
.switch-danger input:checked+span {
    background-color: #d26a5c;
}
.block {
    margin-bottom: 10px;
    background-color: #fff;
    -webkit-box-shadow: 0 2px rgba(0, 0, 0, 0.01);
    box-shadow: 0 2px rgba(0, 0, 0, 0.01);
}
.side-content .block {
    -webkit-box-shadow: none;
    box-shadow: none;
}
.block-header {
    padding: 15px 20px;
    -webkit-transition: opacity 0.2s ease-out;
    transition: opacity 0.2s ease-out;
}
.block-header:before, .block-header:after {
    content: " ";
    display: table;
}
.block-header:after {
    clear: both;
}
.block-title {
    font-size: 15px;
    font-weight: 600;
    text-transform: uppercase;
    line-height: 1.2;
}
.block-title.text-normal {
    text-transform: none;
}
.block-title small {
    font-size: 13px;
    font-weight: normal;
    text-transform: none;
}
.block-content {
    margin: 0 auto;
    padding: 20px 20px 1px;
    max-width: 100%;
    overflow-x: visible;
    -webkit-transition: opacity 0.2s ease-out;
    transition: opacity 0.2s ease-out;
}
.block-content p, .block-content .push, .block-content .block, .block-content .items-push>div {
    margin-bottom: 20px;
}
.block-content .items-push-2x>div {
    margin-bottom: 40px;
}
.block-content .items-push-3x>div {
    margin-bottom: 60px;
}
.block-content.block-content-full {
    padding-bottom: 20px;
}
.block-content.block-content-full .pull-b {
    margin-bottom: -20px;
}
.block-content .pull-t {
    margin-top: -20px;
}
.block-content .pull-r-l {
    margin-right: -20px;
    margin-left: -20px;
}
.block-content .pull-b {
    margin-bottom: -1px;
}
.block-content.block-content-mini {
    padding-top: 10px;
}
.block-content.block-content-mini.block-content-full.block-content-mini {
    padding-bottom: 10px;
}
@media screen and (min-width: 1200px) {
    .block-content.block-content-narrow {
        padding-left: 10%;
        padding-right: 10%;
    }
}
.block.block-full .block-content {
    padding-bottom: 20px;
}
.block.block-full .block-content.block-content-mini {
    padding-bottom: 10px;
}
.block-table {
    width: 100%;
}
.block-table td {
    padding: 10px;
    vertical-align: middle;
}
.block.block-bordered {
    border: 1px solid #e9e9e9;
    -webkit-box-shadow: none;
    box-shadow: none;
}
.block.block-bordered .block-header {
    border-bottom: 1px solid #e9e9e9;
}
.block.block-rounded {
    border-radius: 4px;
}
.block.block-rounded .block-header {
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
}
.block.block-rounded .block-content:first-child {
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
}
.block.block-rounded .block-content:last-child {
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
}
.block.block-themed>.block-header {
    border-bottom: none;
}
.block.block-themed>.block-header .block-title {
    color: #fff;
}
.block.block-themed>.block-header .block-title small {
    color: rgba(255, 255, 255, 0.75);
}
.block.block-transparent {
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;
}
.block.block-opt-refresh {
    position: relative;
}
.block.block-opt-refresh .block-header {
    opacity: .25;
}
.block.block-opt-refresh .block-content {
    opacity: .15;
}
.block.block-opt-refresh:after {
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -20px 0 0 -20px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    color: #646464;
    font-family: Simple-Line-Icons;
    font-size: 18px;
    text-align: center;
    z-index: 2;
    content: "\e09a";
    -webkit-animation: fa-spin 2s infinite linear;
    animation: fa-spin 2s infinite linear;
}
.ie9 .block.block-opt-refresh:after {
    content: "Loading..";
}
.block.block-opt-fullscreen {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    margin-bottom: 0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}
.block.block-opt-hidden.block-bordered .block-header {
    border-bottom: none;
}
.block.block-opt-hidden .block-content {
    display: none;
}
a.block {
    display: block;
    color: #646464;
    font-weight: normal;
    -webkit-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
a.block:hover {
    color: #646464;
    opacity: .9;
}
a.block.block-link-hover1:hover {
    -webkit-box-shadow: 0 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px rgba(0, 0, 0, 0.1);
    opacity: 1;
}
a.block.block-link-hover1:active {
    -webkit-box-shadow: 0 2px rgba(0, 0, 0, 0.01);
    box-shadow: 0 2px rgba(0, 0, 0, 0.01);
}
a.block.block-link-hover2:hover {
    -webkit-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    transform: translateY(-2px);
    -webkit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
    opacity: 1;
}
a.block.block-link-hover2:active {
    -webkit-transform: translateY(-1px);
    -ms-transform: translateY(-1px);
    transform: translateY(-1px);
    -webkit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05);
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05);
}
a.block.block-link-hover3:hover {
    -webkit-box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
    opacity: 1;
}
a.block.block-link-hover3:active {
    -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
}
.block>.nav-tabs {
    background-color: #f9f9f9;
    border-bottom: none;
}
.block>.nav-tabs.nav-tabs-right>li {
    float: right;
}
.block>.nav-tabs.nav-justified>li>a {
    margin-bottom: 0;
}
.block>.nav-tabs>li {
    margin-bottom: 0;
}
.block>.nav-tabs>li>a {
    margin-right: 0;
    padding-top: 12px;
    padding-bottom: 12px;
    color: #646464;
    font-weight: 600;
    border: 1px solid transparent;
    border-radius: 0;
}
.block>.nav-tabs>li>a:hover {
    color: #5c90d2;
    background-color: transparent;
    border-color: transparent;
}
.block>.nav-tabs>li.active>a, .block>.nav-tabs>li.active>a:hover, .block>.nav-tabs>li.active>a:focus {
    color: #646464;
    background-color: #fff;
    border-color: transparent;
}
.block>.nav-tabs.nav-tabs-alt {
    background-color: transparent;
    border-bottom: 1px solid #e9e9e9;
}
.block>.nav-tabs.nav-tabs-alt>li>a {
    -webkit-transition: all 0.15s ease-out;
    transition: all 0.15s ease-out;
}
.block>.nav-tabs.nav-tabs-alt>li>a:hover {
    -webkit-box-shadow: 0 2px #5c90d2;
    box-shadow: 0 2px #5c90d2;
}
.block>.nav-tabs.nav-tabs-alt>li.active>a, .block>.nav-tabs.nav-tabs-alt>li.active>a:hover, .block>.nav-tabs.nav-tabs-alt>li.active>a:focus {
    -webkit-box-shadow: 0 2px #5c90d2;
    box-shadow: 0 2px #5c90d2;
}
.block .block-content.tab-content {
    overflow: hidden;
}
.block-options {
    float: right;
    margin: -3px 0 -3px 15px;
    padding: 0;
    height: 24px;
    list-style: none;
}
.block-options:before, .block-options:after {
    content: " ";
    display: table;
}
.block-options:after {
    clear: both;
}
.block-options.block-options-left {
    float: left;
    margin-right: 15px;
    margin-left: 0;
}
.block-options.block-options-left+.block-title {
    float: right;
}
.block-options>li {
    display: inline-block;
    margin: 0 2px;
    padding: 0;
}
.block-options>li>a, .block-options>li>button {
    display: block;
    padding: 2px 3px;
    color: #999999;
    opacity: .6;
}
.block.block-themed>.block-header .block-options>li>a, .block.block-themed>.block-header .block-options>li>button {
    color: #fff;
}
.block-options>li>a:hover, .block-options>li>button:hover {
    text-decoration: none;
    opacity: 1;
}
.block-options>li>a:active, .block-options>li>button:active {
    opacity: .6;
}
.block-options>li>span {
    display: block;
    padding: 2px 3px;
}
.block.block-themed>.block-header .block-options>li>span {
    color: #fff;
}
.block-options>li>a:focus {
    text-decoration: none;
    opacity: 1;
}
.block-options>li>button {
    background: none;
    border: none;
}
.block-options>li.active>a, .block-options>li.open>button {
    text-decoration: none;
    opacity: 1;
}