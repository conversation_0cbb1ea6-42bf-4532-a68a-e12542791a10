


[class*=" icon-"]:before, [class^=icon-]:before {
	font-family: "iconfont","YDUI-ICONS";
	font-size: inherit;
}

.m-cell:after{
	border-bottom: 0;
	height: 0;
}
.icon {
	width: 1em;
	height: 1em;
	vertical-align: -0.15em;
	fill: currentColor;
	overflow: hidden;
	font-size: 28px;
}
input[type="date"]:before{
	content: attr(placeholder);
	color:#757575;
}
input[class="navbar-title"]::-webkit-input-placeholder{
	color: #fff;
}
.m-cell{margin-bottom:0}
.m-celltitle {
	padding: 5px 12px;
}
.m-celltitle:after{border: 0}
.lv{
	margin-top: 2px;
	margin-left: 4px;
	font-size: 12px;
	padding: 2px 3px;
	overflow: hidden;
	border-radius: 4px;
	display: inline-flex;
	height: 17px;
	line-height: 14px;
}
.lv i{
	font-size: 12px;
}
.nan{
	color: #fff;
	background: #03A9F4;
	box-shadow: 0 1px 10px -2px #03A9F4;
}
.nv{
	color: #fff;
	background: rgb(233, 15, 204);
	box-shadow: 0 1px 10px -2px rgb(233, 15, 204);
}
.no{
	color: #fff;
	background: #929292;
	
}
.sex-nan{
	color: #03A9F4;
	position: relative;
	bottom: -1px;
}
.sex-nv{
	color: rgb(233, 15, 204);
	position: relative;
	bottom: -1px;
}
.sex-no{
	color: #673AB7;
	position: relative;
	bottom: -1px;
}
.text-center{
	text-align: center;
}
.border-radius{
	border-radius: 100%;
}
.btn-fenxiang{
	background-color: #607D8B;
	color: #fff
}
.btn-tuijian{
	background: #00BCD4;
	color: #fff;
}
.btn-primary p,
.btn-danger p,
.btn-fenxiang p,
.btn-tuijian p,
.btn-warning p
{
	color: #000;
	line-height: 20px;
}
.btn-icon {
	display: inline-block;
	padding: 0;
}
.btn-icon>div {
	display: flex;
	width: 36px;
	height: 36px;
	align-items: center;
	justify-content: center;
	line-height: 35px;
}
.btn-icon>div>i {
	font-size: 16px;
}

.slider-item {
	display: flex;
	justify-content: center;
}
.slider-item .key{
	position: relative;
	
}
.slider-item .key em{
	position: absolute;
	left: 0;
	top: 15px;
	padding: 5px 10px;
	background: rgba(0, 0, 0, 0.3);
	color: #fff;
}
.slider-item .title{
	position: absolute;
	bottom: 25px;
	background: rgba(0, 0, 0, 0.46);
	padding: 5px 10px;
	color: #fff;
	display: inline-table;
	border-radius: 2px;
}
 .imui_water {
	position: absolute;
	left: 0;
	bottom: -8px;
	height: 30px;
	width: 100%;
	z-index: 1;
}
.imui_water_1, .imui_water_2 {
	position: absolute;
	width: 100%;
	height: 30px;
}
.imui_water_1 {
	background: url(img/water-1.svg) repeat-x;
	background-size: 600px;
	-webkit-animation: wave-animation-z 3.5s infinite linear;
	animation: wave-animation-z 3.5s infinite linear;
}
.imui_water_2 {
	top: 5px;
	background: url(img/water-2.svg) repeat-x;
	background-size: 600px;
	-webkit-animation: wave-animation-y 6s infinite linear;
	animation: wave-animation-y 6s infinite linear;
}
@-moz-keyframes wave-animation-y{
	0% {
		background-position: 0 top;
	}
	100% {
		background-position: 600px top;
	}
}
@-moz-keyframes wave-animation-z{
	0% {
		background-position: 0 top;
	}
	100% {
		background-position: 600px top;
	}
}
@keyframes wave-animation-y{
	0% {
		background-position: 0 top;
	}
	100% {
		background-position: 600px top;
	}
}
@keyframes wave-animation-z{
	0% {
		background-position: 0 top;
	}
	100% {
		background-position: 600px top;
	}
}

.m-navbar{
	background: #fff;
}
.navbar-item{
	padding: 0 15px;
}

.m-navbar:after{
	border: none;
}
.tabbar-item.tabbar-active{
	color: rgba(3, 169, 244, 0.8);
	text-shadow: 0 1px 5px rgba(3, 169, 244, 0.22);
}
.m-tabbar{
	box-shadow: 0 1px 5px #ddd;
}
.m-tabbar:after{
	border-top:none;
}
.cell-item:not(:last-child):after{
	border-bottom: 1px solid #f5f5f5;
}
.yd-mask {
	position: fixed;
	bottom: 0;
	right: 0;
	left: 0;
	top: 0;
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-ms-flex-align: center;
	align-items: center;
	pointer-events: none;
	-webkit-transition: opacity .2s ease-in;
	transition: opacity .2s ease-in;
	opacity: 0.5
}
.yd-mask-show{
	background-color: rgb(0, 0, 0);
	z-index: 1500; 
	opacity: 0.5; 
	pointer-events: auto;
}
.yd-popup {
	position: fixed;
	background-color: #fff;
	z-index: 1501;
	-webkit-transition: -webkit-transform .2s;
	transition: -webkit-transform .2s;
	transition: transform .2s;
	transition: transform .2s, -webkit-transform .2s;
	pointer-events: none;
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-webkit-flex-direction: column;
	-ms-flex-direction: column;
	flex-direction: column
}

.yd-popup-content {
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	-ms-flex: 1;
	flex: 1;
	position: relative;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch
}

.yd-popup-left {
	-webkit-transform: translate(-100%);
	transform: translate(-100%);
	left: 0;
	top: 0;
	height: 100%
}

.yd-popup-right {
	-webkit-transform: translate(100%);
	transform: translate(100%);
	right: 0;
	top: 0;
	height: 100%
}

.yd-popup-bottom {
	-webkit-transform: translateY(100%);
	transform: translateY(100%);
	right: 0;
	bottom: 0
}

.yd-popup-show {
	pointer-events: auto;
	-webkit-transform: translate(0);
	transform: translate(0)
}

.yd-popup-center {
	z-index: 1502;
	position: fixed;
	top: 50%;
	left: 50%;
	opacity: 0;
	-webkit-transform: translate(-50%, -50%) scale(.95);
	transform: translate(-50%, -50%) scale(.95);
	-webkit-transform-origin: 50% 50%;
	transform-origin: 50% 50%;
	-webkit-transition: -webkit-transform .1s;
	transition: -webkit-transform .1s;
	transition: transform .1s;
	transition: transform .1s, -webkit-transform .1s
}

.yd-popup-center,
.yd-popup-center * {
	pointer-events: none
}

.yd-popup-center.yd-popup-show {
	opacity: 1;
	-webkit-transform: translate(-50%, -50%) scale(1);
	transform: translate(-50%, -50%) scale(1);
	pointer-events: auto
}

.yd-popup-center.yd-popup-show * {
	pointer-events: auto
}
.grids-txt i{
	font-size: 26px;
}

.nd_menu{
	width: calc(100% - 30px);
	margin-left: 15px;
	margin-bottom: 15px;
	border-radius: 10px;
	
	box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
	-webkit-transform: translate(0,120%);
	transform: translate(0,120%);
	box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
}

.nd_menu .nd_mend_content{
	background-color: #fff;
	border-radius: 10px;
	padding: 10px;
}
.nd_menu .nd_mened_user{
	height: 35px;
	line-height: 30px;
	position: relative;
}
.nd_menu .nd_mened_user img{
	height: 50px;
	width: 50px;
	border-radius: 50%;
	position: absolute;
	top: -25px;
	left: 5.5%;
	border: 2px solid #fff;
}
.nd_menu .nd_mened_user .name {
	text-transform : capitalize;
	margin-left: 85px;
	font-size: 16px;
	text-align: left;
}
.nd_menu .nd_mened_user .name em{
	color: #673AB7;
}
.nd_menu .m-grids-3 .grids-item,
.nd_menu .m-grids-4 .grids-item{
	padding:5px 0;
}
.nd_menu .m-grids-3 .grids-item:not(:nth-child(3n)):before,
.nd_menu .m-grids-4 .grids-item:not(:nth-child(4n)):before{
	border-right: none;
}
.nd_menu .grids-item:after,
.nd_menu .m-grids-2:before, 
.nd_menu .m-grids-3:before, 
.nd_menu .m-grids-4:before, 
.nd_menu .m-grids-5:before{
	border-bottom: none;
}
.yd-mask-show-menu{
	background: rgba(0, 0, 0, 0.1);
	z-index: 1500;
	opacity: 1;
	pointer-events: auto;
}
.nd_content{
	background: #fff;
	margin-top: 10px;
}
.nd_content .list-theme1{
	padding: 0px 5px 5px;
	background: #fafdff;
}
.nd_content h4{
	font-size: 16px;
	padding: 8px 10px;
}

.nd_list_1{
	position: relative;
	background-color: #fff;
	margin: 10px 10px 0;
	padding: 10px 10px 0;
	box-shadow: 0 3px 17px -7px rgba(96, 125, 139, 0.31);
	border-radius: 5px;
}


.nd_list_1 .list_header{
	display: flex;
	flex: 1;
}
.nd_list_1 .list_header img {
	height: 35px;
	width: 35px;
	border-radius: 50%;
	margin-right: 10px;
}
.nd_list_1 .list_header .title{
	font-size: 15px;
	display: flex;
	justify-content: center;
	align-items: center;
	text-transform: capitalize;
}
.nd_list_1 .list_header .title p{
	font-size: 12px;
	color: #757575;
}
.nd_list_1 .list_content{
	
	margin-left: -10px;
	margin-right: -10px;
}
.nd_list_1 .list_content img {
	width: 100%;
	height: 3cm;
	object-fit: cover;
	border-radius: 5px
}
.nd_list_1 .list_content .content_summary{
	padding: 0 10px 5px;
	font-size: 14px;
}
.nd_list_1 .list_content .grids-item{
	padding: 0;
}
.nd_list_1 .list_content .grids-item:after{
	border-bottom: 6px solid #fff;
}

.nd_list_1 .list_content .m-grids-1{
	position: relative;
	background-color: #FFF;
	overflow: hidden;
}
.content_img_list .grids-item{
	margin-right: 3px;
}
.content_img_list .grids-item:nth-child(3n+0){
	margin-right: 0px;
}
.nd_list_1 .list_content .m-grids-1 .grids-item{
	width: 100%;
}
.content_img_list .m-grids-3 {
	padding: 0 15px;
}
.content_img_list .m-grids-3 .grids-item{
	width: calc(33.333333% - 2px)
}
.nd_list_1 .list_content .m-grids-1 .grids-item .thread_content{
	padding: 0 10px 5px;
}
.nd_list_1 .list_content .m-grids-1 .grids-item .thread_content span{
	font-size: 18px;
}
.nd_list_1 .list_content .m-grids-1 .grids-item .thread_content p{
	color: #545454;
	font-weight: 200;
}
.nd_list_1 .list_content .m-grids-1 .grids-item img{
	height: 5cm;
}
.nd_list_1 .list_content .m-grids-2:before, 
.nd_list_1 .list_content .m-grids-3:before, 
.nd_list_1 .list_content .m-grids-4:before, 
.nd_list_1 .list_content .m-grids-5:before{
	border-bottom:none;
	
}
.content_img_list .m-grids-3 .grids-item:not(:nth-child(3n)):before{border-right: none;}
.nd_list_1 .thread_content .forum_name{
	color: #03a9f4;
}
.nd_list_1 .list_content .summary{
	color: #888;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
	overflow: hidden;
	font-size: 14px
}
.nd_list_1 .list_footer{
	margin-left: -10px;
	margin-right: -10px;
	margin-top: 5px;
}
.nd_list_1 .list_footer .post{
	border-radius: 0 0 5px 5px;
}
.nd_list_1 .list_footer .m-grids-3{
	border-radius: 0 0 5px 5px;
}
.grids-item:after{
	border: 0;
	height: 0;
}
.nd_list_1 .list_footer .grids-item{
	padding: 13px 0;
}
.nd_list_1 .list_footer .m-grids-2:after, 
.nd_list_1 .list_footer .m-grids-3:after, 
.nd_list_1 .list_footer .m-grids-4:after, 
.nd_list_1 .list_footer .m-grids-5:after{
	position: absolute;
	z-index: 1;
	top: 0;
	width: 100%;
	height: 1px;
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	-webkit-transform-origin: 0 100%;
	transform-origin: 0 100%;
	left: 0;
	border-bottom: 1px solid #e6e6e6;
	content: '';
}

.nd_list_1 .list_footer .grids-txt i {
	font-size: 15px;
}
.nd_list_1 .list_footer .zan{
	display: flex;
	justify-content: center;
	align-items: center;
}
.nd_list_1 .list_footer .zan p{
	margin-left: 5px;
}
.nd_list_1 .list_footer .right{
	text-align: right;
	font-size: 18px;
	line-height: 30px;
}
.nd_list_1 .list_footer .right span {
	margin-right: 10px;
	background: #f3f3f3;
	display: inline-block;
	line-height: 24px;
	width: 30px;
	text-align: center;
	border-radius: 1.5px;
}

.goods {
	font-size: 16px;
	padding: 0 10px;
	word-wrap: break-word;
}
.goods a{
	font-size: 14px;
	color: #673AB7;
}
.post{
	position: relative;
}


.tuijian_guanzhu li::after,
.post_info::after,
.forum_nav:after,
.thread_post:after{
	position: absolute;
	z-index: 1;
	bottom: -1px;
	width: 100%;
	height: 1px;
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	-webkit-transform-origin: 0 100%;
	transform-origin: 0 100%;
	left: 0;
	border-bottom: 1px solid #e6e6e6;
	content: '';
	box-sizing: content-box;
}
.thread_post:last-child:after{
	border-bottom: 0px;
}
.post .post_list:before{
	position: absolute;
	z-index: 1;
	top: -1px;
	width: calc(100% - 20px);
	height: 2px;
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	-webkit-transform-origin: 0 100%;
	transform-origin: 0 100%;
	left: 0;
	border-bottom: 1px solid #e6e6e6;
	content: '';
	margin: 5px 10px;
}
.post .post_list{
	padding-top: 10px;
	font-size: 13px;
	color: #7b7b7b;
	text-transform:capitalize;
}
.post .post_list li{
	padding-bottom: 5px; 
}
.post .post_list li .icon{
	font-size: 20px;
}
.post .post_list a{color: #1976D2}

.scroll {position: relative;}


.scroll:before,
.forum_nav:before
{
	position: absolute;
	z-index: 1;
	top: -1px;
	width: 100%;
	height: 1px;
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	-webkit-transform-origin: 0 100%;
	transform-origin: 0 100%;
	left: 0;
	border-bottom: 1px solid #e6e6e6;
	content: '';
}
.scroll span{
	background: #fff;
	text-align: center;
	line-height: 45px;
	font-size: 14px;
	
	margin-top: 10px;
	color: #8c8c8c;
	font-weight: 300;
	width: 100%;
	display: block;
}
.g-view:after {
    height: 50px;
}

.forum_nav{
	position: relative;
	margin-top: 15px;
}
.forum_nav span{
	background: #fff;
	line-height: 40px;
	padding-left: 10px;
	font-size: 16px;
	display: block;
}

.forum_list .grids-item{padding: 10px 0}
.forum_list .m-grids-2:before, 
.forum_list .m-grids-3:before, 
.forum_list .m-grids-4:before, 
.forum_list .m-grids-5:before{
	border-bottom: 1px solid #ececec;
}
.forum_list .list_img{
	justify-content:  center;
	display:  flex;
}
.forum_list .list_img img{
	width: 50px;
	height: 50px;
}
.forum_list .list_title{
	margin-top: 5px;
}

.forum_thread{
	height: 190px;
	overflow: initial;
	position: relative;
	margin-top: -50px;
}
.forum_thread_header{
	width: 100%;
	height: 100%;
	background-size: cover;
	background-position: center center;
	position: relative;
	text-shadow: 0 0 1px #00000052;
}
.forum_thread_header .m-navbar{
	background-color: rgba(255, 255, 255, 0);
}
.forum_thread_header .m-navbar:after{
	border-bottom:none;
}

.forum_thread_header .navbar-item,
.forum_thread_header .navbar-item .back-ico:before, 
.forum_thread_header .navbar-item .next-ico:before{
	color: #fff;
}

.forum_thread_header .mess{
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #fff;
	font-size: 15px;
	width: 65%;
	text-align: center;
}
.forum_thread_header .info{

}
.forum_thread_header .forumg{
	position: absolute;
	flex: 1;
	bottom: 0;
	left: 50%;
	transform: translate(-50%, -20%);
	display: flex;
	width: calc(100% - 40px);
	justify-content: space-between;
	align-items: center;    
}
.forum_thread_header .forumg h3{
	color: #fff;
	font-size: 22px;
}
.forum_thread_header .forumg a{
	background: rgba(255, 255, 255, 0.5);
	margin-left: 15px;
	border-radius: 50%;
	color: #fff;
	text-shadow: none;
}
.forum_thread_header .forumg img{
	width: 35px;
	height: 35px;
	border-radius: 100%;

}
.forum_thread_header .forumg .title{
	text-align: center;
}
.forum_thread_header .forumg .title h3{
	font-size: 18px;
	text-shadow: 0 0px 1px rgba(0, 0, 0, 0.2);
}
.forum_thread_header .forumg .forun_icon{
	width: 75px;
	height: 75px;
	margin-bottom: 4px;
	border-radius: 50%;
	border: 3px solid rgba(255, 255, 255, 0.5);
}

.forum_thread_header .forumg .forun_info{
	display: flex;
	justify-content: flex-end;
	margin-bottom: 15px;
	text-shadow: 0 0px 1px rgba(0, 0, 0, 0.2);
}
.forum_thread_header .forumg .forun_info div{
	text-align: center;
	margin-left:20px;
}
.forum_thread_header .forumg .forun_info div p{
	color: #f5f5f5
}
.forum_thread_nav{
	line-height: 45px;
	background: #fff;
	font-size: 16px;
	padding-left: 10px;
	position: relative;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.08);
}
.forum_thread_nav a{
	margin-right: 7px;
	padding: 6px 0px;
	margin: 0 10px;
	color: #444;
}
.forum_thread_nav .active{
	position: relative;
	color: #03A9F4;
}
.forum_thread_nav .active::after{
	content: '';
	position: absolute;
	bottom: 1px;
	height: 2px;
	width: 20px;
	background: #03A9F4;
	border-radius: 50px;
	left: 6px;
}
.forum_thread_nav .right{
	position: absolute;
	top: 0;
	right: 8px;
	border-left: 1px solid #e7e7e7;
	padding-left: 8px;
	line-height: 45px;
	font-size: 20px;
}

.forum_thread_zifenlei{
	text-align: center;
	position: fixed;
	top: 0px;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1000;
	background-color: #EFEFF4;
	-webkit-transform: translate(0,100%);
	transform: translate(0, 100%);
	-webkit-transition: -webkit-transform .3s;
	transition: -webkit-transform .3s;
	transition: transform .3s;
	transition: transform .3s, -webkit-transform .3s;
}
.actionsheet-toggle{
	-webkit-transform: translate(0,0);
	transform: translate(0, 0);
}
.forum_thread_zifenlei.forum_list .list_img img {
	width: 54px;
	height: 54px;
}
.forum_thread_zifenlei .list{
	background: #fff;
	overflow-y: scroll;
	margin-top: 15px;
}
.no_thread{
	text-align: center;
	padding: 30px;
	background: #fff;
	color: #b9b9b9;
	margin: 10px;
	border-radius: 5px;
	box-shadow: 0 1px 1px rgba(0, 0, 0, 0.08);
}
.no_thread i{
	font-size: 40px;
}

.thread_header{
	background-color: #fff;
	padding:10px;
	font-size: 20px;
	box-shadow: 0 3px 17px -7px rgba(96, 125, 139, 0.31);
}
.thread_header p{
	position: relative;
	font-size: 14px;
	color: #666;
	margin-top: 5px;
}
.thread_header p span{
	position: absolute;
	right: 0;
	top: 0;
}
.thread_user,
.thread_forum{
	position: relative;
	padding: 10px;
	display: flex;
	align-items: center;
	flex: 1;
}
.thread_user img,
.thread_forum img{
	margin-right: 10px;
	width: 40px;
	height: 40px;
	border-radius: 50%;
}
.thread_user .user{
	display: flex;
	justify-content: center;
	align-items: center;
}
.thread_user .user .user_name{ text-transform: capitalize;font-size: 15px;color: #444;}
.thread_user .user .btn_gz {
	padding: 2px 4px;
	border: 1px solid rgb(209, 224, 230);
	position: absolute;
	right: 10px;
	top: 13px;
	border-radius: 1.5px;
	color: rgba(3, 169, 244, 0.88);
}
.thread_user .user h2{
	font-size: 16px;
	text-transform:capitalize;
}
.thread_user .user p{
	color: #777;
}
.thread_forum .title{
	width: 100%
}
.thread_forum .guanzu{
	width: 70px;
	border: 1px solid #cbe8f7;
	padding: 6px 4px;
	text-align: center;
	color: #03A9F4;
	border-radius: 1.5px;
	font-size: 14px;
}
.thread_content{
	position: relative;
	background: #fff;
	padding: 10px;
	font-size: 17px;
	word-break: break-all;
	word-wrap: break-word;
	margin: 0 10px;
	border-radius: 5px;
	box-shadow: 0 3px 17px -7px rgba(96, 125, 139, 0.31);
}
.thread_content p a{
	color: #03A9F4
}
.thread_content p{
	margin-bottom: 10px;
}

.thread_content img {
    max-width: 100%!important;
    width: inherit!important;
    display: inline;
    height: inherit!important;
}
.tuzhang,
.thread_content>.tuzhang{
	position: absolute;
	right: 20px;
	top: -23px;
	width: 80px !important;
	z-index: 1;
	height: inherit!important;
}
.thread_footer{
	margin: 0 10px;
	border-radius: 5px;
	box-shadow: 0 3px 17px -7px rgba(96, 125, 139, 0.31);
}
.thread_footer .thread_forum{
	border-radius: 5px;
}
.thread_content_foot{
	margin-top: 10px;
}
.thread_content_foot a{
	margin: 0 5px;
}
.thread_footer{

}
.post_nav{
	position: relative;
	padding: 0 10px;
	margin-top: 10px;
	line-height: 45px;
	font-size: 16px;
	background: #fff;
	margin: 0 10px;
	border-radius: 5px 5px 0 0;
	box-shadow: 0 -1px 17px -7px rgba(96, 125, 139, 0.31);
}
.post_nav .post_sort{
	position: absolute;
	right: 10px;
	top: 0;
}
.post_nav .post_sort span{
margin-left: 10px;

}
.post_nav .post_sort .active{
	position: relative;
	padding: 5px;
}
.post_nav .post_sort .active a{
	color: #2196F3;
}
.post_nav .post_sort .active a::after{
	content: '';
	position: absolute;
	bottom: 1px;
	height: 2px;
	width: 20px;
	background: #03A9F4;
	border-radius: 50px;
	left: 10px;
}
.thread_post_post_list{
	position: relative;
	margin-top: 10px;
	background: #f7f7f7;padding: 10px;border-radius: 3px;
}
.thread_post_post_list::before{
	position: absolute;
	width: 0;
	height: 0;
	border-width: 0 10px 10px;
	border-style: solid;
	border-color: transparent transparent #f7f7f7;
	top: -6px;
	z-index: 10;
	left: 10px;
	content: '';
}
.thread_post_post_list li{
	list-style: none;
	line-height: 20px;
}
.thread_post_post_list li a{
	color: #00bcd4;
	text-transform: capitalize;
}
#post_list{
	margin: 0 10px 0;
	background: #fff;
	border-radius: 0px 0px 5px 5px;
	box-shadow: 0 3px 17px -7px rgba(96, 125, 139, 0.31);
}
.thread_post{
	position: relative;
}
.thread_post .post_header img{
	width: 40px;
	height: 40px;
}
.thread_post .post_header .thread_user .user h2{
	font-size: 16px;
	font-weight: 100;
}
.thread_post .post_conent{
	padding: 5px 10px;
	font-size: 15px;
}
.thread_post .post_conent p img{
	max-width: 100%;
}
.thread_post .post_conent img{
	max-width: 100%;
	display: inline;
}
.thread_post .post_footer{
	padding:5px 10px 10px;
	font-size: 16px;
	text-align: right;
}
.thread_post .post_footer span{
	margin-left: 10px;
}


.user_ipc{
	height: 200px;
	overflow: initial;
	position: relative;
	margin-bottom: -50px;
}
.user_info{
	color: #fff;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
}
.user_info img{
	width: 55px;
	height: 55px;
	border-radius: 50%;
	display: initial;
	border: 2px solid rgba(255, 255, 255, 0.5);
	margin-bottom: 5px;
}
.user_info i{
	font-size: 12px;
}

.user_menu{
	box-shadow: 0 3px 17px -7px rgba(96, 125, 139, 0.31);   
}
.user_menu .m-grids-2:before, 
.user_menu .m-grids-3:before, 
.user_menu .m-grids-4:before, 
.user_menu .m-grids-5:before,
.user_menu .grids-item:after,
.user_menu .m-grids-4 .grids-item:not(:nth-child(4n)):before
{
	border: 0;
}
.user_menu .m-grids-4{
	padding: 5px 3px;
}
.user_menu .grids-item{
	padding: 6px;
}
.user_nav .active{
	color: #03a9f4;
}
.user_menu i{
	color: #03A9F4;
}
.user_nav .active::after{
	content: '';
	position: absolute;
	bottom: 10px;
	height: 3px;
	background: #03A9F4;
	left: 50%;
	width: 16px;
	border-radius: 20px;
	text-align: center;
	transform: translate(-50%, 0);
}
.user_nav .active .grids-txt{
	color: inherit;
}

.yd-timeline{background-color:#fff;font-size:13px;color:#444;overflow:hidden;position:relative;z-index:1;padding-top: 10px;}
.yd-timeline:after{content:"";position:absolute;z-index:0;top:0;left:0;width:100%;border-top:1px solid #e6e6e6;-webkit-transform:scaleY(.5);transform:scaleY(.5);-webkit-transform-origin:0 0;transform-origin:0 0}
.yd-timeline-content{margin-left:30px;border-left:1px solid rgba(0, 188, 212, 0.08)}
.yd-timeline-custom-item,
.yd-timeline-item{padding:0px 12px 10px 0;margin-left:30px;position:relative}
.yd-timeline-item .thread_user_pic{
	width: 35px;
	height: 35px;
	border-radius: 50%;
	position: absolute;
	left: -48px;
	border: 2px solid rgba(0, 188, 212, 0.5);
}
.yd-timeline-custom-item:not(:last-child):after,
.yd-timeline-item:not(:last-child):after{content:"";position:absolute;z-index:0;bottom:0;left:0;width:100%;border-bottom:1px solid #e6e6e6;-webkit-transform:scaleY(.5);transform:scaleY(.5);-webkit-transform-origin:0 0;transform-origin:0 0}
.yd-timeline-custom-item .yd-timeline-icon,.yd-timeline-item .yd-timeline-icon{content:"";position:absolute;z-index:1;left:-16px;display:block;top:19px;-webkit-transform:translate(-50%);transform:translate(-50%)}

.yd-timeline-custom-item:first-child>.yd-timeline-icon,.yd-timeline-item:first-child>.yd-timeline-icon{top:3px}
.yd-timeline-custom-item:last-child:before,.yd-timeline-item:last-child:before{content:"";width:1px;height:100%;background-color:#fff;position:absolute;left:-17px;top:19px}
.yd-timeline-item .yd-timeline-icon{width:8px;height:8px;border-radius:99px;background-color:#e4e5e9}
.yd-timeline-item:first-child>.yd-timeline-icon{background-color:#f23030;width:10px;height:10px}
.yd-timeline-item:first-child:before.yd-timeline-custom-item:first-child>.yd-timeline-icon{top:0}


.lookfor {
	position: relative;
	background: #fff;
	padding: 15px;
	margin-top: 10px;
}
.lookfor li{
	position: relative;
	align-items: center;
	display: flex;
	padding: 8px 0;
}
.lookfor li h1{
	text-transform: capitalize;
}
.lookfor li:after,
.lookfor li .list:after{
	content: "";
	position: absolute;
	z-index: 2;
	bottom: 0;
	width: 100%;
	height: 1px;
	border-bottom: 1px solid #e6e6e6;
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	-webkit-transform-origin: 0 100%;
	transform-origin: 0 100%;
}
.lookfor li:last-child:after{
	border: none;
}
.lookfor li img{
	width: 40px;
	height: 40px;
	border-radius: 100%;
}
.lookfor li .content{
	margin-left: 5px;
}
.lookfor li .content h1{
	font-size: 15px;
}
.lookfor li .right{
	position: absolute;
	right: 0;
	
}
.lookfor li .right i{
	font-size: 26px;
	color: #4bbcef
}


.user_card_nav{
	position: relative;
	line-height: 35px;
	background: #fff;
}
.user_card_nav h2{
	padding: 0 10px;
	font-size: 15px;
}

.post{
	background: #fff;
	margin: 10px;
	padding: 10px;
	border-radius: 5px;
	box-shadow: 0 3px 17px -7px rgba(96, 125, 139, 0.31);
}
.post input::-webkit-input-placeholder{
	color: #999999
}
.post input{
	line-height: 35px;
	padding: 0 10px;
	border: none;
	background: #f3f3f3;
	width: 100%;
	font-size: 16px;
}
.post .simditor{text-align: left;border:none}
.post .simditor .simditor-body{
	
	overflow-y: scroll;
	min-height: 100px;
	padding: 10px 10px 0px;
	background: #f3f3f3;
}
.simditor-body img{
	display: inline;
}
.post .simditor .simditor-body img{
	max-width: 100%;
	margin:0;
	display: inline;
}
.post .simditor .simditor-wrapper{
	background: none;
}
.post .simditor .simditor-body p, .simditor .simditor-body div, .editor-style p, .editor-style div{
	margin: 0;
}
.post .simditor .simditor-wrapper .simditor-placeholder{
	padding: 10px;
	z-index: 3;
}
.post .image-popover{
	display: none!important;
}
.post .post_edit{
	position: relative;
	padding-bottom: 45px;
	background: #f3f3f3;
}
.post .post_edit .fenlei{
	position: absolute;
	bottom: 10px;
	left: 10px;
	padding: 5px 10px;
	color: #9E9E9E;
	border: 1px solid #9E9E9E;
	border-radius: 15px;
	z-index: 10;
}
.post .post_form{
	margin-top: 10px;
}
.post .post_mune .navbar{
	display: flex;
	align-items: center;
	margin-top: 10px;
}
.post .fenlei_list{
	height: calc(100% - 50px);
	overflow-y: scroll;
}
.post .tab-active{
	color: #03A9F4;
}
.post .post_mune .navbar a{
	display: inline-block;
	padding-right: 10px;
	height: 33px;
	line-height: 30px;

}
.post .post_mune .navbar a i{
	font-size: 28px;
}
.post .uploadfile button input{
	position: absolute;
	display: block;
	width: calc(100% - 0px);
	height: calc(100% - 0px);
	opacity: 0;
	top: 0;
	left: 0;
}
.post .tab_nume{
	position: relative;
}
.post .tab-panel .tab-panel-item{
	background: #f3f3f3;
	margin-top: 5px;
	padding: 10px!important;
}
.post .tab-panel .biaoqing{
	height: 180px;
	overflow-y: scroll;
}
.post .tab-active:before{
	position: absolute;
	width: 0;
	height: 0;
	border-width: 0 7px 10px;
	border-style: solid;
	border-color: transparent transparent #f3f3f3;
	bottom: -8px;
	z-index: 10;
	left: 7px;
	content: '';
}
.post .m-grids-2,
.post .m-grids-3,
.post .m-grids-4,
.post .m-grids-5{
	background: none;
}
.post .m-cell:after,
.post .m-grids-4:before,
.post .m-grids-5:before,
.post .grids-item:after,
.post .m-grids-4 .grids-item:not(:nth-child(4n)):before,
.post .m-grids-5 .grids-item:not(:nth-child(5n)):before{
	border: none
}
.post .m-grids-4 input{
	position: absolute;
	display: block;
	width: calc(100% - -6px);
	height: calc(100% - 9px);
	opacity: 0;
	top: 0;
}
.post .m-grids-4 .grids-item{
	padding:0 0 10px;
}
.post .m-grids-4 .grids-item img{
	margin-left: 2px;
	border-radius: 5px;
}
.top{
	position: fixed;
	right: 15px;
	bottom: 65px;
	z-index: 10;
}
.top a{
	margin-top: 5px;
	font-size: 16px;
	width: 36px;
	height: 35px;
	border-radius: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #fff;
	background: rgba(136, 136, 136, 0.78);
	opacity: 0;
	-webkit-transition: opacity .2s ease-in;
	transition: opacity .2s ease-in;
}
.top-bg{
	opacity: 1 !important;
}
.meitu .list-mes{
	position: absolute;
	bottom: 0;
	width: 100%;
	background: rgba(0, 0, 0, 0.27);
	color: #fff;
}
.meitu .list-mes h3{
	color: #fff
}
.list-theme1 .list-item .list-mes .list-title, .list-theme2 .list-item .list-mes .list-title{
	height: 22px;
}
.list-theme1 .list-item{
	padding: 0 5px;
	margin-top: 10px;
}
.list-theme1 .list-img{
	border-radius: 5px;
	box-shadow: 0 3px 17px -7px #607D8B;
}
.list-theme1 .list-mes{
	background: rgba(255, 255, 255, 0);
}
.list-theme1 .list-price{
	font-size: 14px;
	color: #41a9d8;
}
.list-mes .list-title{
	font-weight: 400;
}
.tuijianz {
	position: relative;
	background: #fff;
	margin-top: 10px;
	padding: 10px;
	margin: 10px 10px;
	border-radius: 5px;
	box-shadow: 0 3px 17px -7px rgba(96, 125, 139, 0.31);
}
.tuijianz img{
	max-width: 100%;
}
.tuijianz span{
	position: absolute;
	bottom: 10px;
	left:10px;
	padding: 3px 6px;
	color: #fff;
	background: rgba(0, 0, 0, 0.45);
}
.list-img img{
	width: 100%;
	height: 100%;
	margin:0;
	object-fit:cover;
}
#pic .grids-item img{
	background: #fff; width: 76px;height: 76px;object-fit: cover;
}
.del_img{
	position: absolute;
	top: -7px;
	right: 7px;
	background: #F44336;
	border-radius: 100%;
	color: #fff;
	width: 25px;
	height: 25px;
	justify-content: center;
	align-items: center;
	display: flex;
	text-align: center;
}
.del_img i{
	font-size: 12px;
}
#pic{
	padding: 10px 10px 0;
	margin: 0px -10px 0;
}
.max{ 
	border-radius: 4px;
	padding: 2px 4px;
	box-shadow: 0 1px 10px -2px #8d4fef;
	background: #8d4fef;
	font-size: 12px; 
	color:#fff; 
	animation: changeshadow 1.3s  ease-in  infinite ;
	
	-webkit-animation: changeshadow 1.3s linear infinite;
	-moz-animation: changeshadow 1.3s linear infinite;
	-ms-animation: changeshadow 1.3s linear infinite;
	-o-animation: changeshadow 1.3s linear infinite;
}  
.jcenter{
	display: flex;
	align-items: center;
}
@keyframes changeshadow {
	0%{ box-shadow: 0 0 4px #8d4fef}  
	50%{ box-shadow: 0 0 40px #8d4fef}  
	100%{ box-shadow: 0 0 4px #8d4fef}  
}

@-webkit-keyframes changeshadow {
  0%{ box-shadow: 0 0 4px #8d4fef}  
	  50%{ box-shadow: 0 0 40px #8d4fef}  
	  100%{ box-shadow: 0 0 4px #8d4fef}  
}
@-moz-keyframes changeshadow {
	0%{ box-shadow: 0 0 4px #8d4fef}  
		50%{ box-shadow: 0 0 40px #8d4fef}  
		100%{ box-shadow: 0 0 4px #8d4fef}  
}
@-ms-keyframes changeshadow {
	0%{ box-shadow: 0 0 4px #8d4fef}  
		50%{ box-shadow: 0 0 40px #8d4fef}  
		100%{ box-shadow: 0 0 4px #8d4fef}  
}
@-o-keyframes changeshadow {
	0%{ box-shadow: 0 0 4px #8d4fef}  
		50%{ box-shadow: 0 0 40px #8d4fef}  
		100%{ box-shadow: 0 0 4px #8d4fef}  
}
.list_header .title div{
	position: relative;
}
.list_header .title .btn_gz{
    padding: 3px 4px;
    border: 1px solid rgb(209, 224, 230);
    position: absolute;
    right: 10px;
    top: 10px;
    border-radius: 5px;
    color: rgba(3, 169, 244, 0.88);
    display: flex;
    align-items: center;
    font-size: 12px;
}
.sh{
	position: relative;
	background:#fff;
	padding: 5px 10px;
}
.sh input{
	background: #eee;
	width: 100%;
	border: none;
	line-height: 30px;
	border-radius: 30px;
	padding: 0 15px;
	font-size: 14px;
	height: 31px;
}
.shbtn{
	position: absolute;
	background: rgb(3, 169, 244);
	color: #fff;
	top: 5px;
	line-height: 30px;
	border: none;
	padding: 0px 10px;
	border-radius: 0 15px 15px 0;
	right: 10px;
	height: 31px;
}
.resou{
	margin: 10px;
}
.resou a{
	border: 1px solid #888;
	padding: 4px 8px;
	line-height: 18px;
	border-radius: 15px;
	color: #888;
	display: inline-block;
	height: 26px;
}
.emoji{
	max-width: 100%;
}
.thread-emoji{
	width: 25px;
}


.loader {     
	position: relative;
	width: 10px;
	height: 10px;
	border-radius: 76%;
	margin: 5px;
	display: inline-block;
	vertical-align: middle;}

.loader-1 .loader-outter {
	position: absolute;
	border: 2px solid #00BCD4;
	border-left-color: transparent;
	border-bottom: 0;
	width: 100%;
	height: 100%;
	border-radius: 50%;
	-webkit-animation: loader-1-outter 1s cubic-bezier(.42, .61, .58, .41) infinite;
	animation: loader-1-outter 1s cubic-bezier(.42, .61, .58, .41) infinite;
}
.loader-1 .loader-inner {
	position: absolute;
	border: 2px solid #03A9F4;
	border-radius: 50%;
	width: 25px;
	height: 25px;
	left: calc(50% - 13px);
	top: calc(50% - 13px);
	border-right: 0;
	border-top-color: transparent;
	-webkit-animation: loader-1-inner 1s cubic-bezier(.42, .61, .58, .41) infinite;
	animation: loader-1-inner 1s cubic-bezier(.42, .61, .58, .41) infinite;
}
	
@-webkit-keyframes loader-1-outter {
	0% {
	-webkit-transform: rotate(0deg);
	transform: rotate(0deg);
   }
	100% {
	-webkit-transform: rotate(360deg);
	transform: rotate(360deg);
   }
   }
	@keyframes loader-1-outter {
	0% {
	-webkit-transform: rotate(0deg);
	transform: rotate(0deg);
   }
	100% {
	-webkit-transform: rotate(360deg);
	transform: rotate(360deg);
   }
   }
	@-webkit-keyframes loader-1-inner {
	0% {
	-webkit-transform: rotate(0deg);
	transform: rotate(0deg);
   }
	100% {
	-webkit-transform: rotate(-360deg);
	transform: rotate(-360deg);
   }
   }
	@keyframes loader-1-inner {
	0% {
	-webkit-transform: rotate(0deg);
	transform: rotate(0deg);
   }
	100% {
	-webkit-transform: rotate(-360deg);
	transform: rotate(-360deg);
   }
   }
   
.zhuan{
	-webkit-animation: loader-1-inner 1s cubic-bezier(.42, .61, .58, .41) infinite;
	animation: loader-1-inner 1s cubic-bezier(.42, .61, .58, .41) infinite;
}
.reds{
	width: 80px;
	height: 80px;
	background: rgba(255, 255, 255, 0.95);
	justify-content: center;
	align-items: center;
	display: flex;
	border-radius: 10px;
	opacity: 1;
}
.loading-page{
	z-index: 100000;
	position: fixed;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.21);
	text-align: center;
	justify-content: center;
	align-items: center;
	display: flex;
}
.loader2 {
	width: 35px;
	height: 35px;
	display: inline-block;
	padding: 0px;
	border-radius: 100%;
	border: 5px solid;
	border-top-color: rgba(254, 168, 23, 0.65);
	border-bottom-color: rgba(57, 154, 219, 0.65);
	border-left-color: rgba(188, 84, 93, 0.95);
	border-right-color: rgba(137, 188, 79, 0.95);
	-webkit-animation: loader2 2s ease-in-out infinite alternate;
	animation: loader2 2s ease-in-out infinite alternate;
}
@keyframes loader2 {
	from {transform: rotate(0deg);}
	to {transform: rotate(720deg);}
}
@-webkit-keyframes loader2 {
	from {-webkit-transform: rotate(0deg);}
	to {-webkit-transform: rotate(720deg);}
}
.loading {
	animation: pins 1.5s linear infinite;
}

@-webkit-keyframes pins {
	100% {
		-webkit-transform: rotate(-360deg);
		transform: rotate(-360deg);
	}

	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
}

@keyframes pins {
	100% {
		-webkit-transform: rotate(-360deg);
		transform: rotate(-360deg);
	}

	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
}
.plugins .cell-left{
	width: 80%;
	text-overflow: ellipsis;
	overflow-x: scroll;
	white-space: nowrap;
}
.thread_user_tz{
	margin: 8px 10px;border-radius: 5px;background: #FFF;
	box-shadow: 0 3px 17px -7px rgba(96, 125, 139, 0.31);
}
.thread_user_tz a.cell-item
{
	width: 100%;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	background-color: #fff0 !important;
}
.thread_user_tz .cell-right{
	min-height: 40px;
}
pre{
	overflow-x: scroll;
	background: rgb(40, 42, 54) !important;
	padding: 10px;
	color: #cacaca;
	border-radius: 3px;
	font-size: 14px
	
}
.zhiding{
	display: flex;
	align-items: center;
	width: 100%;
	line-height: 38px;
	padding: 0 0 0 10px;
	margin-left: -12px;
}
.zhiding a{
	width: 100%;
	font-size: 14px;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}
.zhiding .time{
	width: 100px;
	text-align: right;
}
.zhiding span{
	font-size: 12px;
	padding: 1px 2px 0;
	background: #8d4fef;
	color: #fff;
	border-radius: 1.5px;
	margin-right: 5px;
	height: 18px;
	line-height: 17px;
	width: 38px;
	text-align: center;
}
.lock{
	text-align: center;
	border: 2px dashed #f27474;
	height: 40px;
	line-height: 40px;
	font-weight: bold;
	color: #f27474;
	margin-bottom: 10px;
}
.m-scrolltab {
	top: 50px;
	bottom: 56px;
}
.style_list3 li{
	position: relative;
	padding-top: 10px;
	padding-bottom: 10px;
}
.style_list3 li:last-child,
.style_list3 li:last-child:after
{
	padding-bottom: 0;
	border: none;
}
.style_list3 li::after{
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	content: '';
	height: 1px;
	border-bottom: 1px solid #f5f5f5;
	-webkit-transform: scaleY(.5);
	transform: scaleY(.5);
	-webkit-transform-origin: 0 100%;
	transform-origin: 0 100%;
}
.style_list3 li img{
	border-radius: 100%;
	width: 2.5rem;
	height: 2.5rem;
	margin-right: 6px;
}
.style_list3 .form_info{
	display: flex;
	flex: 1;
	align-items: center;
}
.scrolltab-item.crt>div{
	color: #03A9F4;
}
.style_list3 .form_info .form_name{
	font-size: 16px;
	color: #444;
}
.style_list3 .form_info .form_name,
.style_list3 .form_info .form_right{
	width: 100%;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}
.style_list3 .form_info .form_html{
	color: #a9a9a9;
	width: 100%;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}
.style_list3 .form_info .form_guanzhu {
	width: 80px;
	border: 1px solid #cbe8f7;
	padding: 5px;
	text-align: center;
	color: #03A9F4;
	border-radius: 1.5px;
}

.scrolltab-item:after{
	border-bottom: 1px solid #f5f5f5;
}
.scrolltab-nav:after,
.scrolltab-item:before{
	border-right: 1px solid #f5f5f5;
}
.post-xuand{
	width: 32px;
	height: 32px;
	border-radius: 100%;
	margin-right: 10px;
}
.mask-white-dialog{
	z-index: 10000;
}
.btn-primary{
	background: #03A9F4;
}
.btn-primary:active{
	background: #0289c5;
}

.forum_list .grids-item{
	padding: 5px 0;
	display: flex;
	justify-content: center;
	align-items: center;
}
.forum_list .grids-item img{
	width: 45px;
	height: 45px;
	border-radius: 100%;
	display: initial;
}


.yd-popup-content li{
	line-height: 40px;
	text-align: left;
	list-style: none;
	font-size: 16px;
	padding: 0 15px;
}
.yd-popup-content li a{
	display: block;
}
.yd-popup-content .sidbr li a i{
	margin-left:15px;
	padding-right:10px;
}
.yd-popup-content .sidbg{
	position: relative;
	height: 110px;
	background-size: 100%;
}
.yd-popup-content .sidbg .userinfo{
	position: absolute;
	text-align:left;padding: 10px;
	bottom: 0;
	text-shadow: 0 1px 1px #444;
	color: #fff;
}
.yd-popup-content .sidbg .userinfo h3{
	font-size: 16px;
}
.yd-popup-content .sidbg .zhuti i{
	position: absolute;
	top: 10px;
	right: 10px;
	font-size: 18px;
	color: #fff;
	text-shadow: 0 1px 1px #444;
}


.index_menu{
	margin-bottom: 10px;
	padding: 0 13px;
	box-shadow: 0 3px 17px -7px rgba(96, 125, 139, 0.31);
}
.index_menu h1{
	font-size: 16px;
}
.index_menu span{
	color: #fff;
}
.index_menu .grids-txt{
	padding: 10px 10px;
}
.index_menu .sign{
	border-radius: 5px;
	padding: 5px;
	color: #fff;
	background-image: linear-gradient(65deg, rgba(3, 211, 232, 0.51) 20%,#53ddea 80%);
	box-shadow: 0 5px 19px -7px #53ddea;
}
.index_menu .task{
	border-radius: 5px;
	padding: 5px;
	color: #fff;
	background-image: linear-gradient(65deg, rgba(203, 79, 224, 0.56) 20%,#d450ea 80%);
	box-shadow: 0 5px 19px -7px #d450ea;
}
.index_menu .posts{
	border-radius: 5px;
	padding: 5px;
	color: #fff;
	background-image: linear-gradient(65deg, rgba(233, 30, 99, 0.5) 20%,#f75d78 80%);
	box-shadow: 0 5px 19px -7px #f75d78;
}
.index_menu a{
	padding: 10px 0;
}

.index_menu .grids-item:after{
	border-bottom:0;
}
.index_menu .grids-item:not(:nth-child(3n)):before{
	border-right:0;
}
.m-grids-2:before, .m-grids-3:before, .m-grids-4:before, .m-grids-5:before{
	border-bottom: 1px solid #e6e6e6;
}
.m-grids-3:before{
	border-bottom: 0;
}

.bankuai{
	display: flex;
	align-items: center;
	background: #fff;
	padding: 15px;
}
.bankuai img{
	width: 55px;
	height: 55px;
	border-radius: 10px;
	margin-right: 15px;
}
.bankuai h3{
	font-size: 18px;
	color: #444;
}
.bankuai p{
	color: #969696
}

.bankuai .guanzu{
	width: 80px;
	border: 1px solid #cbe8f7;
	padding: 8px 5px;
	text-align: center;
	color: #03A9F4;
	border-radius: 1.5px;
}
.bangui{
	font-size: 14px;
	color: #5a5a5a;
	padding: 10px;
}
.gengduo .m-grids-4 .grids-item:not(:nth-child(4n)):before,
.ff6a00m:after{border:none}
.ff6a00
ff6a00;
}ff6a00
.ptr--text{
	font-size: 14px;
}

.login_input{
	border: 0;
	border-bottom: 0.08rem solid rgba(111, 23, 255, 0.45);
	line-height: 38px;
	width: 100%;
	font-size: 1rem;
	color: rgba(111, 23, 255, 0.45);
	font-weight: bold;
}
.login_input::-webkit-input-placeholder{
	color: rgba(111, 23, 255, 0.45);
}
.login_button{
	background: #6f17ff;
	color: #fff;
	line-height: 45px;
	width: 100%;
	border: none;
	border-radius: 3px;
	font-size: 1rem;
	box-shadow: 0 9px 32px -13px #6f17ff;
}
.login_zhaohui{
	color: rgba(111, 23, 255, 0.45);
	font-weight: bold;
	font-size: 0.9rem;
}
.login_disanf{
	display: flex;
	justify-content: center;
}
.login_disanf span{
	position: relative;
	width: 150px;
	height: 2px;
	padding: 8px;
	border-bottom: 1px solid rgba(111, 23, 255, 0.45);
	z-index: 1;
	font-size: 1rem;
	display: block;

}
.login_disanf span::after{
	content: "or";
	position: absolute;
	color: rgba(111, 23, 255, 0.45);
	width: 35px;
	transform: translate(-50%, -50%);
	top: 15px;
	background: #ffffff;
}
.login_disanf_btn{
	margin: 20px 0;
	display: flex;
	justify-content: center;
}
.login_disanf_btn button{
	border: 1px solid rgba(111, 23, 255, 0.45);
	line-height: 43px;
	width: calc(50% - 60px);
	font-size: 16px;
	height: 43px;
	border-radius: 3px;
}

.icon-fanhui:before {
	
}
.login_icon{
	border-radius: 50%;
	height: 41px;
	line-height: 40px;
	margin-right: 15px;
}
.login_icon:last-child{
	margin-right: 0;
}
.login_icon div{
	width: 40px;
	height: 41px;
	line-height: 40px;
}
.login_icon div i{
	font-size: 20px;
}
.login_icon_qq{
	border: 0.08rem solid rgba(3, 169, 244, 0.5);
	color: #03A9F4;
	text-shadow: 0 1px 12px rgba(3, 169, 244, 0.54);
	box-shadow: inset 0px 1px 14px -4px rgba(3, 169, 244, 0.54), 0px 1px 14px -4px rgba(3, 169, 244, 0.54);
}
.login_icon_wb{
	border: 0.08rem solid rgba(244, 67, 54, 0.5);
	color: #F44336;
	text-shadow: 0 1px 12px rgba(244, 67, 54, 0.55);
	box-shadow: inset 0px 1px 14px -4px rgba(244, 67, 54, 0.54), 0px 1px 14px -4px rgba(244, 67, 54, 0.54);
}
.login_icon_wx {
	border: 0.08rem solid rgba(76, 175, 80, 0.5);
	color: #4CAF50;
	text-shadow: 0 1px 12px rgba(76, 175, 80, 0.54);
	box-shadow: inset 0px 1px 14px -4px rgba(76, 175, 80, 0.54), 0px 1px 14px -4px rgba(76, 175, 80, 0.54);
}
.login_icon_tb {
	border: 0.08rem solid rgba(255, 80, 0, 0.5);
	color: #ff5000;
	text-shadow: 0 1px 12px #ff5000;
	box-shadow: inset 0px 1px 14px -4px rgba(255, 80, 0, 0.54), 0px 1px 14px -4px rgba(255, 80, 0, 0.54);
}
.login_icon_github {
	border: 0.08rem solid rgba(36, 41, 46, 0.5);
	color: #24292e;
	text-shadow: 0 1px 12px rgba(36, 41, 46, 0.54);
	box-shadow: inset 0px 1px 14px -4px rgba(36, 41, 46, 0.54), 0px 1px 14px -4px rgba(36, 41, 46, 0.54);
}

.repass_email{
	position: absolute;
	right: 0;
	top: 0;
	border: 0;
	font-size: 1rem;
	color: rgba(111, 23, 255, 0.45);
	font-weight: bold;
}
.nd_crde {margin-top: 10px;}
.nd_crde h4{
	margin: 0 10px;
	font-size: 16px;
	color: #444;
}
.tuijian_guanzhu_1 .name{
	font-size:16px;line-height: 28px;
}
.tuijian_guanzhu_1 .thread{
	color:#a7a7a7
}
.tuijian_guanzhu_1 button{
	border: 0;border-radius: 28px;padding: 5px 20px;color: #fff;margin-top: 8px;
}
.tuijian_guanzhu_1 .nan_1{
	background-image: linear-gradient(65deg, rgba(3, 211, 232, 0.51) 20%,#53ddea 80%);box-shadow: 0 5px 19px -7px #53ddea;
}
.tuijian_guanzhu_1 .nv_1{
	background-image: linear-gradient(65deg, rgba(203, 79, 224, 0.56) 20%,#d450ea 80%);
	box-shadow: 0 5px 19px -7px #d450ea;
}
.tuijian_guanzhu_1 .weizhi{
	background-image: linear-gradient(65deg, rgba(71, 47, 208, 0.54) 20%,#472fd0 80%);
	box-shadow: 0 5px 19px -7px #472fd0;
}
.swiper-pagination-bullet {
	background: #03a9f4;
	opacity: .2;
}
.swiper-pagination-bullet-active {
	opacity: 1;
	background: #03a9f4;
}

	.user_mess .m-actionsheet {
		background: #fff;
		height: 100%;

	}

	.tab-panel .tab-panel-item {
		padding: 0;
	}

	.tab-nav-item:not(:last-child):after {
		border: 0
	}

	.tab-nav-item.tab-active:before {
		width: 100%;
		margin-left: -50%;
	}

	.actionsheet-item {
		position: relative;
		height: 50px;
		text-align: left;
		padding: 0 17px;
		display: flex;
		align-items: center;
	}

	.actionsheet-item img {
		margin-right: 5px;
		border-radius: 100%;
	}

	.user_mess .actionsheet-action,
	.user_mess .actionsheet-item {
		line-height: inherit;
	}

	.actionsheet-item span {
		position: absolute;
		right: 15px;
		top: 15px;
	}

	.user_mess .m-actionsheet .g-view {
		height: 100%;
		overflow-y: scroll;
	}

	

	#liaotian .m-tab,
	#liaotian .tab-panel,
	#liaotian .tab-panel-item {
		height: 100%;
	}

	#msg-list {
		height: 100%;
		padding-bottom: 10px;
		position: absolute;
		width: 100%;
		overflow: auto;
		-webkit-overflow-scrolling: touch;
	}

	.msg-item {
		padding: 4px 8px;
		clear: both;
	}


	.msg-item .msg-user-img {
		width: 38px;
		height: 38px;
		display: inline-block;
		border-radius: 3px;
		vertical-align: top;
		text-align: center;
		float: left;
		color: #ddd;
	}


	.msg-item .msg-content {
		display: inline-block;
		border-radius: 5px;
		border: solid 1px #d3d3d3;
		background-color: #FFFFFF;
		color: #333;
		padding: 8px;
		vertical-align: top;
		font-size: 15px;
		position: relative;
		margin: 0px 8px;
		max-width: 75%;
		min-width: 35px;
		float: left;
	}
	.msg-item .clearfix{
		word-wrap: break-word;
		text-align: left;
	}
	.msg-item .msg-content .msg-content-arrow {
		position: absolute;
		border: solid 1px #d3d3d3;
		border-right: none;
		border-top: none;
		background-color: #FFFFFF;
		width: 10px;
		height: 10px;
		left: -5px;
		top: 12px;
		-webkit-transform: rotateZ(45deg);
		transform: rotateZ(45deg);
	}

	.msg-item-self .msg-user,
	.msg-item-self .msg-content {
		float: right;
	}
	.msg-item-self .msg-content,
	.msg-item-self .msg-content .msg-content-arrow {
		background-color: #3ecc56;
		color: #fff;
		border-color: #2AC845;
	}
	.msg-item-self .msg-content .msg-content-arrow {
		left: auto;
		right: -5px;
		-webkit-transform: rotateZ(225deg);
		transform: rotateZ(225deg);
	}
	.msg-item .mui-item-clear {
		clear: both;
	}
	.footer-center {
		height: 100%;
		padding: 5px 0px;
		width: calc(100% - 70px);
	}
	.footer-center .input-text {
		background: #fff;
		border: solid 1px #ddd;
		padding: 10px !important;
		font-size: 16px !important;
		line-height: 18px !important;
		font-family: verdana !important;
		overflow: hidden;
		resize: none;
		outline: none;
	}
	.footer-center [class*=input] {
		width: 100%;
		height: 100%;
		border-radius: 5px;
	}
	.footer-right {
		position: absolute;
		width: 50px;
		height: 55px;
		right: 19px;
		bottom: 0px;
		text-align: center;
		vertical-align: middle;
		line-height: 100%;
		padding: 9px 5px;
		display: inline-block;
	}
	.friend-hide{
		display: none;
	}
	.friend-show{
		display: block;
	}
	.navbar-title3{
		text-align: center;
		color: #fff;
		width: 100%;
		white-space: nowrap;
		overflow: hidden;
		display: block;
		text-overflow: ellipsis;
		font-size: 20px;
	}
.guanzhu .sign{
	word-wrap: normal;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.index_footer{
	color: #d6d6d6;
	text-align: center;
	margin-bottom: -10px;
}
.index_footer a{
	color: #c4e8f7;
}
.simditor .simditor-popover {
    z-index: 200 !important;
}