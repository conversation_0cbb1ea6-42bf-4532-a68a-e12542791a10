$.fn.pcodedmenu=function(a){var b=this.attr("id");var c={themelayout:"vertical",MenuTrigger:"click",SubMenuTrigger:"click",activeMenuClass:"active",ThemeBackgroundPattern:"pattern6",HeaderBackground:"theme4",LHeaderBackground:"theme4",NavbarBackground:"theme4",ActiveItemBackground:"theme0",SubItemBackground:"theme4",ActiveItemStyle:"style0",ItemBorder:true,ItemBorderStyle:"solid",SubItemBorder:true,DropDownIconStyle:"style1",FixedNavbarPosition:false,FixedHeaderPosition:false,horizontalMenuplacement:"top",horizontalMenulayout:"widebox",horizontalBrandItem:true,horizontalLeftNavItem:true,horizontalRightItem:false,horizontalSearchItem:false,horizontalBrandItemAlign:"left",horizontalLeftNavItemAlign:"right",horizontalRightItemAlign:"right",horizontalsearchItemAlign:"right",horizontalstickynavigation:false,horizontalNavigationView:"view1",horizontalNavIsCentered:false,horizontalNavigationMenuIcon:true,layouttype:"light",verticalMenuplacement:"left",verticalMenulayout:"wide",collapseVerticalLeftHeader:true,VerticalSubMenuItemIconStyle:"style6",VerticalNavigationView:"view1",verticalMenueffect:{desktop:"shrink",tablet:"push",phone:"overlay",},defaultVerticalMenu:{desktop:"expanded",tablet:"collapsed",phone:"offcanvas",},onToggleVerticalMenu:{desktop:"collapsed",tablet:"expanded",phone:"expanded",},};var a=$.extend({},c,a);var d={PcodedMenuInit:function(){d.Handlethemelayout();d.HandleverticalMenuplacement();d.HandlehorizontalMenuplacement();d.HandleMenulayout();d.HandleDeviceType();d.Handlecomponetheight();d.HandleMenuOnClick();d.HandleMenuTrigger();d.HandleSubMenuTrigger();d.HandleActiveItem();d.HandleOffcanvasMenu();d.HandleVerticalLeftHeader();d.HandleThemeBackground();d.HandleActiveItemStyle();d.HandleItemBorder();d.HandleBorderStyle();d.HandleSubItemBorder();d.HandleDropDownIconStyle();d.HandleOptionSelectorPanel();d.HandleNavbarPosition();d.HandleVerticalSubMenuItemIconStyle();d.HandleVerticalNavigationView();d.HandleHorizontalItemIsCentered();d.HandleHorizontalItemAlignment();d.HandleSubMenuOffset();d.HandleHorizontalStickyNavigation();d.HandleDocumentClickEvent();d.HandleVerticalScrollbar();d.HandleHorizontalMobileMenuToggle();d.horizontalNavigationMenuIcon();d.verticalNavigationSearchBar();d.safariBrowsercompatibility();d.Handlemenutype();d.Handlelayoutvartype()},safariBrowsercompatibility:function(){is_chrome=navigator.userAgent.indexOf("Chrome")>-1;is_explorer=navigator.userAgent.indexOf("MSIE")>-1;is_firefox=navigator.userAgent.indexOf("Firefox")>-1;is_safari=navigator.userAgent.indexOf("Safari")>-1;is_opera=navigator.userAgent.indexOf("Presto")>-1;is_mac=(navigator.userAgent.indexOf("Mac OS")!=-1);is_windows=!is_mac;if(is_chrome&&is_safari){is_safari=false}if(is_safari||is_windows){}},verticalNavigationSearchBar:function(){if(a.themelayout==="vertical"){$(".searchbar-toggle").on("click",function(){$(this).parent(".pcoded-search").toggleClass("open")})}},horizontalNavigationMenuIcon:function(){if(a.themelayout==="horizontal"){switch(a.horizontalNavigationMenuIcon){case false:$("#"+b+".pcoded .pcoded-navbar .pcoded-item > li > a .pcoded-micon").hide();$("#"+b+".pcoded .pcoded-navbar .pcoded-item.pcoded-search-item > li > a .pcoded-micon").show();break;default:}}},HandleHorizontalMobileMenuToggle:function(){if(a.themelayout==="horizontal"){$("#mobile-collapse").on("click",function(){$(".pcoded-navbar").toggleClass("show-menu")})}},HandleVerticalScrollbar:function(){if(a.themelayout==="vertical"){satnt=a.defaultVerticalMenu.desktop;if(satnt==="expanded"||satnt==="compact"){mt=a.MenuTrigger;if(mt==="click"){$(window).on("load",function(){$(".sidebar_toggle a").click(function(g){g.preventDefault();var f=$(this);rel=f.attr("rel");el=$(".pcoded-navbar")})})}}}},HandleDocumentClickEvent:function(){function e(){$(document).on("click",function(g){var k=$(g.target);var j=$("#"+b).attr("pcoded-device-type");var i=$("#"+b).attr("vertical-nav-type");var h=$("#"+b+" .pcoded-item li");if(!k.parents(".pcoded-item").length){if(j!="phone"){if(i!="expanded"){h.removeClass("pcoded-trigger")}}}})}function f(){$(document).on("click",function(g){var i=$(g.target);var h=$("#"+b+" .pcoded-search");if(!i.parents(".pcoded-search").length){h.removeClass("open")}})}e();f()},HandleHorizontalStickyNavigation:function(){switch(a.horizontalstickynavigation){case true:$(window).on("scroll",function(){var e=$(this).scrollTop();if(e>=100){$(".pcoded-navbar").addClass("stickybar");$("stickybar").fadeIn(3000)}else{if(e<=100){$(".pcoded-navbar").removeClass("stickybar");$(".stickybar").fadeOut(3000)}}});break;case false:$(".pcoded-navbar").removeClass("stickybar");break;default:}},HandleSubMenuOffset:function(){switch(a.themelayout){case"horizontal":var e=a.SubMenuTrigger;if(e==="hover"){$("li.pcoded-hasmenu").on("mouseenter mouseleave",function(k){if($(".pcoded-submenu",this).length){var n=$(".pcoded-submenu:first",this);var m=n.offset();var g=m.left;var f=n.width();var j=$(window).height();var h=$(window).width();var i=(g+f<=h);if(!i){$(this).addClass("edge")}else{$(this).removeClass("edge")}}})}else{$("li.pcoded-hasmenu").on("click",function(k){k.preventDefault();if($(".pcoded-submenu",this).length){var n=$(".pcoded-submenu:first",this);var m=n.offset();var g=m.left;var f=n.width();var j=$(window).height();var h=$(window).width();var i=(g+f<=h);if(!i){$(this).toggleClass("edge")}}})}break;default:}},HandleHorizontalItemIsCentered:function(){if(a.themelayout==="horizontal"){switch(a.horizontalNavIsCentered){case true:$("#"+b+" .pcoded-navbar").addClass("isCentered");break;case false:$("#"+b+" .pcoded-navbar").removeClass("isCentered");break;default:}}},HandleHorizontalItemAlignment:function(){var h=a.themelayout;if(h==="horizontal"){function g(){var j=$("#"+b+".pcoded .pcoded-navbar .pcoded-brand");if(a.horizontalBrandItem===true){switch(a.horizontalBrandItemAlign){case"left":j.removeClass("pcoded-right-align");j.addClass("pcoded-left-align");break;case"right":j.removeClass("pcoded-left-align");j.addClass("pcoded-right-align");break;default:}}else{j.hide()}}function f(){var j=$("#"+b+".pcoded .pcoded-navbar .pcoded-item.pcoded-left-item");if(a.horizontalLeftNavItem===true){switch(a.horizontalLeftNavItemAlign){case"left":j.removeClass("pcoded-right-align");j.addClass("pcoded-left-align");break;case"right":j.removeClass("pcoded-left-align");j.addClass("pcoded-right-align");break;default:}}else{j.hide()}}function e(){var j=$("#"+b+".pcoded .pcoded-navbar .pcoded-item.pcoded-right-item");if(a.horizontalRightItem===true){switch(a.horizontalRightItemAlign){case"left":j.removeClass("pcoded-right-align");j.addClass("pcoded-left-align");break;case"right":j.removeClass("pcoded-left-align");j.addClass("pcoded-right-align");break;default:}}else{j.hide()}}function i(){var j=$("#"+b+".pcoded .pcoded-navbar .pcoded-search-item");if(a.horizontalSearchItem===true){switch(a.horizontalsearchItemAlign){case"left":j.removeClass("pcoded-right-align");j.addClass("pcoded-left-align");break;case"right":j.removeClass("pcoded-left-align");j.addClass("pcoded-right-align");break;default:}}else{j.hide()}}if(a.horizontalNavIsCentered===false){g();f();e();i()}}},HandleVerticalNavigationView:function(){switch(a.themelayout){case"vertical":var e=a.VerticalNavigationView;$("#"+b+".pcoded").attr("vnavigation-view",e);break;case"horizontal":var e=a.horizontalNavigationView;$("#"+b+".pcoded").attr("hnavigation-view",e);break;default:}},HandleVerticalSubMenuItemIconStyle:function(){switch(a.themelayout){case"vertical":var e=a.VerticalSubMenuItemIconStyle;$("#"+b+" .pcoded-navbar .pcoded-hasmenu").attr("subitem-icon",e);break;case"horizontal":$("#"+b+" .pcoded-navbar .pcoded-hasmenu").attr("subitem-icon",e);break;default:}},HandleNavbarPosition:function(){var f=a.FixedNavbarPosition;var g=a.FixedHeaderPosition;var e=a.FixedRightHeaderPosition;switch(a.themelayout){case"vertical":if(f==true){$("#"+b+" .pcoded-navbar").attr("pcoded-navbar-position","fixed");$("#"+b+" .pcoded-header .pcoded-left-header").attr("pcoded-lheader-position","fixed")}else{$("#"+b+" .pcoded-navbar").attr("pcoded-navbar-position","absolute");$("#"+b+" .pcoded-header .pcoded-left-header").attr("pcoded-lheader-position","absolute")}if(g==true){$("#"+b+" .pcoded-header").attr("pcoded-header-position","fixed");$("#"+b+" .pcoded-main-container").css("margin-top",$(".pcoded-header").outerHeight())}else{$("#"+b+" .pcoded-header").attr("pcoded-header-position","relative");$("#"+b+" .pcoded-main-container").css("margin-top","0px")}break;case"horizontal":if(f==true){$("#"+b+" .pcoded-navbar").attr("pcoded-navbar-position","fixed");$("#"+b+" .pcoded-header .pcoded-left-header").attr("pcoded-lheader-position","fixed")}else{$("#"+b+" .pcoded-navbar").attr("pcoded-navbar-position","absolute");$("#"+b+" .pcoded-header .pcoded-left-header").attr("pcoded-lheader-position","absolute")}if(g==true){$("#"+b+" .pcoded-header").attr("pcoded-header-position","fixed");$("#"+b+" .pcoded-main-container").css("margin-top",$(".pcoded-header").outerHeight())}else{$("#"+b+" .pcoded-header").attr("pcoded-header-position","relative");$("#"+b+" .pcoded-main-container").css("margin-top","0px")}break;default:}},HandleOptionSelectorPanel:function(){$(".selector-toggle > a").on("click",function(){$("#styleSelector").toggleClass("open")})},HandleDropDownIconStyle:function(){var e=a.DropDownIconStyle;switch(a.themelayout){case"vertical":$("#"+b+" .pcoded-navbar .pcoded-hasmenu").attr("dropdown-icon",e);break;case"horizontal":$("#"+b+" .pcoded-navbar .pcoded-hasmenu").attr("dropdown-icon",e);break;default:}},HandleSubItemBorder:function(){switch(a.SubItemBorder){case true:$("#"+b+" .pcoded-navbar .pcoded-item").attr("subitem-border","true");break;case false:$("#"+b+" .pcoded-navbar .pcoded-item").attr("subitem-border","false");break;default:}},HandleBorderStyle:function(){var e=a.ItemBorderStyle;switch(a.ItemBorder){case true:$("#"+b+" .pcoded-navbar .pcoded-item").attr("item-border-style",e);break;case false:$("#"+b+" .pcoded-navbar .pcoded-item").attr("item-border-style","");break;default:}},HandleItemBorder:function(){switch(a.ItemBorder){case true:$("#"+b+" .pcoded-navbar .pcoded-item").attr("item-border","true");break;case false:$("#"+b+" .pcoded-navbar .pcoded-item").attr("item-border","false");break;default:}},HandleActiveItemStyle:function(){var e=a.ActiveItemStyle;if(e!=undefined&&e!=""){$("#"+b+" .pcoded-navbar").attr("active-item-style",e)}else{$("#"+b+" .pcoded-navbar").attr("active-item-style","style0")}},Handlemenutype:function(){var h=a.menutype;var e=a.freamtype;var g=a.NavbarImage;var f=a.ActiveNavbarImage;if(h!=undefined&&h!=""){$("#"+b).attr("nav-type",h)}else{$("#"+b).attr("nav-type","st1")}if(e!=undefined&&e!=""){$("#"+b).attr("fream-type",e)}else{$("#"+b).attr("fream-type","theme1")}if(g!=undefined&&g!=""){$("#"+b).attr("sidebar-img",g)}else{$("#"+b).attr("sidebar-img","false")}if(f!=undefined&&f!=""){$("#"+b).attr("sidebar-img-type",f)}else{$("#"+b).attr("sidebar-img-type","img1")}},Handlelayoutvartype:function(){var e=a.layouttype;if(e!=undefined&&e!=""){$("#"+b).attr("layout-type",e)}else{$("#"+b).attr("layout-type","light")}},HandleThemeBackground:function(){function g(){var k=a.ThemeBackgroundPattern;if(k!=undefined&&k!=""){$("body").attr("themebg-pattern",k)}else{$("body").attr("themebg-pattern","pattern1")}}function f(){var k=a.HeaderBackground;if(k!=undefined&&k!=""){$("#"+b+" .pcoded-header").attr("header-theme",k)}else{$("#"+b+" .pcoded-header").attr("header-theme","theme1")}}function i(){var k=a.LHeaderBackground;if(k!=undefined&&k!=""){$("#"+b+" .pcoded-header .navbar-logo").attr("logo-theme",k);$("#"+b+" .pcoded-navigation-label").attr("menu-title-theme",k)}else{$("#"+b+" .pcoded-header .navbar-logo").attr("logo-theme","theme4");$("#"+b+" .pcoded-navigation-label").attr("menu-title-theme","theme4")}}function j(){var k=a.NavbarBackground;if(k!=undefined&&k!=""){$("#"+b+" .pcoded-navbar").attr("navbar-theme",k)}else{$("#"+b+" .pcoded-navbar").attr("navbar-theme","theme1")}}function h(){var k=a.ActiveItemBackground;if(k!=undefined&&k!=""){$("#"+b+" .pcoded-navbar").attr("active-item-theme",k)}else{$("#"+b+" .pcoded-navbar").attr("active-item-theme","theme1")}}function e(){var k=a.SubItemBackground;if(k!=undefined&&k!=""){$("#"+b+" .pcoded-navbar").attr("sub-item-theme",k)}else{$("#"+b+" .pcoded-navbar").attr("sub-item-theme","theme1")}}g();f();i();j();h();e()},HandleVerticalLeftHeader:function(){if(a.themelayout==="vertical"){switch(a.collapseVerticalLeftHeader){case true:$("#"+b+" .pcoded-header").addClass("iscollapsed");$("#"+b+" .pcoded-header").removeClass("nocollapsed");$("#"+b+".pcoded").addClass("iscollapsed");$("#"+b+".pcoded").removeClass("nocollapsed");break;case false:$("#"+b+" .pcoded-header").removeClass("iscollapsed");$("#"+b+" .pcoded-header").addClass("nocollapsed");$("#"+b+".pcoded").removeClass("iscollapsed");$("#"+b+".pcoded").addClass("nocollapsed");break;default:}}else{return false}},HandleOffcanvasMenu:function(){if(a.themelayout==="vertical"){var e=$("#"+b).attr("vertical-nav-type");if(e=="offcanvas"){$("#"+b).attr("vertical-layout","wide")}}},HandleActiveItem:function(){},HandleSubMenuTrigger:function(){switch(a.SubMenuTrigger){case"hover":$("#"+b+" .pcoded-navbar .pcoded-hasmenu").addClass("is-hover");var i=$(window);var h=$(".pcoded-submenu > li");var e=i.width();var g="";(e>=767)?f("hover"):f("click");i.resize(function(){var j=i.width();if(e==j){return}if(j>=767&&g!="hover"){f("hover")}else{if(j<767&&g!="click"){f("click")}}e=j});function f(j){if(j=="hover"){g=j;h.off("click").off("mouseenter mouseleave").hover(function(){$(this).addClass("pcoded-trigger")},function(){$(this).removeClass("pcoded-trigger")})}else{if(j=="click"){g=j;h.off("mouseenter mouseleave").off("click").on("click",function(k){k.stopPropagation();var l=$(this).closest(".pcoded-submenu").length;if(l===0){if($(this).hasClass("pcoded-trigger")){$(this).removeClass("pcoded-trigger")}else{$(this).closest(".pcoded-inner-navbar").find("li.pcoded-trigger").removeClass("pcoded-trigger");$(this).addClass("pcoded-trigger")}}else{if($(this).hasClass("pcoded-trigger")){$(this).removeClass("pcoded-trigger")}else{$(this).closest(".pcoded-submenu").find("li.pcoded-trigger").removeClass("pcoded-trigger");$(this).addClass("pcoded-trigger")}}})}}}break;case"click":$("#"+b+" .pcoded-navbar .pcoded-hasmenu").removeClass("is-hover");$(".pcoded-submenu > li").on("click",function(j){j.stopPropagation();var k=$(this).closest(".pcoded-submenu").length;if(k===0){if($(this).hasClass("pcoded-trigger")){$(this).removeClass("pcoded-trigger")}else{$(this).closest(".pcoded-inner-navbar").find("li.pcoded-trigger").removeClass("pcoded-trigger");$(this).addClass("pcoded-trigger")}}else{if($(this).hasClass("pcoded-trigger")){$(this).removeClass("pcoded-trigger")}else{$(this).closest(".pcoded-submenu").find("li.pcoded-trigger").removeClass("pcoded-trigger");$(this).addClass("pcoded-trigger")}}});break}},HandleMenuTrigger:function(){switch(a.MenuTrigger){case"hover":$("#"+b+" .pcoded-navbar").addClass("is-hover");var i=$(window);var h=$(".pcoded-item > li");var e=i.width();var f="";(e>=767)?g("hover"):g("click");i.resize(function(){var j=i.width();if(e==j){return}if(j>=767&&f!="hover"){g("hover")}else{if(j<767&&f!="click"){g("click")}}e=j});function g(j){if(j=="hover"){f=j;h.off("click").off("mouseenter mouseleave").hover(function(){$(this).addClass("pcoded-trigger")},function(){$(this).removeClass("pcoded-trigger")})}else{if(j=="click"){f=j;h.off("mouseenter mouseleave").off("click").on("click",function(){if($(this).hasClass("pcoded-trigger")){$(this).removeClass("pcoded-trigger")}else{$(this).closest(".pcoded-inner-navbar").find("li.pcoded-trigger").removeClass("pcoded-trigger");$(this).addClass("pcoded-trigger")}})}}}break;case"click":$("#"+b+" .pcoded-navbar").removeClass("is-hover");$(".pcoded-item > li ").on("click",function(){if($(this).hasClass("pcoded-trigger")){$(this).removeClass("pcoded-trigger")}else{$(this).closest(".pcoded-inner-navbar").find("li.pcoded-trigger").removeClass("pcoded-trigger");$(this).addClass("pcoded-trigger")}});break}},HandleMenuOnClick:function(){var e=$(window)[0].innerWidth;if(a.themelayout==="vertical"){$("#mobile-collapse,.sidebar_toggle a, .pcoded-overlay-box,.menu-toggle a").on("click",function(){$(this).parent().find(".menu-icon").toggleClass("is-clicked");var g=$("#"+b).attr("pcoded-device-type");if(g=="desktop"){var l=a.onToggleVerticalMenu.desktop;var k=a.defaultVerticalMenu.desktop;var j=$("#"+b).attr("vertical-nav-type");if(j==k){$("#"+b).attr("vertical-nav-type",l)}else{if(j==l){$("#"+b).attr("vertical-nav-type",k)}else{return false}}}else{if(g=="tablet"){var o=a.onToggleVerticalMenu.tablet;var n=a.defaultVerticalMenu.tablet;var m=$("#"+b).attr("vertical-nav-type");if(m==n){$("#"+b).attr("vertical-nav-type",o)}else{if(j==l){$("#"+b).attr("vertical-nav-type",n)}}}else{if(g=="phone"){var f=a.onToggleVerticalMenu.phone;var i=a.defaultVerticalMenu.phone;var h=$("#"+b).attr("vertical-nav-type");if(h==i){$("#"+b).attr("vertical-nav-type",f)}else{if(j==l){$("#"+b).attr("vertical-nav-type",i)}}}}}$(".pcoded").addClass("pcoded-toggle-animate");setTimeout(function(){$(".pcoded").removeClass("pcoded-toggle-animate")},250)})}else{if(a.themelayout==="horizontal"){if(e>=768&&e<=992){$("#"+b).attr("pcoded-device-type","tablet")}else{if(e<768){$("#"+b).attr("pcoded-device-type","phone")}else{$("#"+b).attr("pcoded-device-type","desktop")}}}}},Handlecomponetheight:function(){function e(){var h=$(window).height();var j=$(".pcoded-header").innerHeight();var k=$(".pcoded-navbar").innerHeight();var g=$(".pcoded-footer").innerHeight();var i=h-j;var l=h-j;var f=h-j}e();$(window).resize(function(){e()})},HandleDeviceType:function(){function e(){var f=$(window)[0].innerWidth;if(a.themelayout==="vertical"){if(f>=768&&f<=992){$("#"+b).attr("pcoded-device-type","tablet");var h=a.defaultVerticalMenu.tablet;if(h!=undefined&&h!=""){$("#"+b).attr("vertical-nav-type",h)}else{$("#"+b).attr("vertical-nav-type","collapsed")}var g=a.verticalMenueffect.tablet;if(g!=undefined&&h!=""){$("#"+b).attr("vertical-effect",g)}else{$("#"+b).attr("vertical-effect","shrink")}}else{if(f<768){$("#"+b).attr("pcoded-device-type","phone");var h=a.defaultVerticalMenu.phone;if(h!=undefined&&h!=""){$("#"+b).attr("vertical-nav-type",h)}else{$("#"+b).attr("vertical-nav-type","offcanvas")}var g=a.verticalMenueffect.phone;if(g!=undefined&&h!=""){$("#"+b).attr("vertical-effect",g)}else{$("#"+b).attr("vertical-effect","push")}}else{$("#"+b).attr("pcoded-device-type","desktop");var h=a.defaultVerticalMenu.desktop;if(h!=undefined&&h!=""){$("#"+b).attr("vertical-nav-type",h)}else{$("#"+b).attr("vertical-nav-type","expanded")}var g=a.verticalMenueffect.desktop;if(g!=undefined&&h!=""){$("#"+b).attr("vertical-effect",g)}else{$("#"+b).attr("vertical-effect","shrink")}}}}else{if(a.themelayout==="horizontal"){if(f>=768&&f<=992){$("#"+b).attr("pcoded-device-type","tablet")}else{if(f<768){$("#"+b).attr("pcoded-device-type","phone")}else{$("#"+b).attr("pcoded-device-type","desktop")}}}}}e();$(window).resize(function(){tw=$(window)[0].innerWidth;dt=$("#"+b).attr("pcoded-device-type");if(dt=="desktop"&&tw<992){e()}else{if(dt=="phone"&&tw>768){e()}else{if(dt=="tablet"&&tw<768){e()}else{if(dt=="tablet"&&tw>992){e()}}}}})},HandleMenulayout:function(){if(a.themelayout==="vertical"){switch(a.verticalMenulayout){case"wide":$("#"+b).attr("vertical-layout","wide");break;case"box":$("#"+b).attr("vertical-layout","box");break;case"widebox":$("#"+b).attr("vertical-layout","widebox");break;default:}}else{if(a.themelayout==="horizontal"){switch(a.horizontalMenulayout){case"wide":$("#"+b).attr("horizontal-layout","wide");break;case"box":$("#"+b).attr("horizontal-layout","box");break;case"widebox":$("#"+b).attr("horizontal-layout","widebox");break;default:}}else{return false}}},HandlehorizontalMenuplacement:function(){if(a.themelayout==="horizontal"){switch(a.horizontalMenuplacement){case"bottom":$("#"+b).attr("horizontal-placement","bottom");break;case"top":$("#"+b).attr("horizontal-placement","top");break;default:}}else{$("#"+b).removeAttr("horizontal-placement")}},HandleverticalMenuplacement:function(){if(a.themelayout==="vertical"){switch(a.verticalMenuplacement){case"left":$("#"+b).attr("vertical-placement","left");break;case"right":$("#"+b).attr("vertical-placement","right");break;default:}}else{$("#"+b).removeAttr("vertical-placement")}},Handlethemelayout:function(){switch(a.themelayout){case"horizontal":$("#"+b).attr("theme-layout","horizontal");break;case"vertical":$("#"+b).attr("theme-layout","vertical");break;default:}},};d.PcodedMenuInit()};$(window).scroll(function(){if($(this).scrollTop()>80){$('.pcoded[theme-layout="vertical"] .pcoded-navbar[pcoded-navbar-position="fixed"][pcoded-header-position="relative"]').css("position","fixed");$('.pcoded[theme-layout="vertical"] .pcoded-navbar[pcoded-navbar-position="fixed"][pcoded-header-position="relative"]').css("top",0)}else{$('.pcoded[theme-layout="vertical"] .pcoded-navbar[pcoded-navbar-position="fixed"][pcoded-header-position="relative"]').css("position","absolute");$('.pcoded[theme-layout="vertical"] .pcoded-navbar[pcoded-navbar-position="fixed"][pcoded-header-position="relative"]').css("top","auto")}});$(window).scroll(function(){if($(this).scrollTop()>80){$('.pcoded[theme-layout="horizontal"][pcoded-device-type="desktop"] .pcoded-navbar[pcoded-navbar-position="fixed"][pcoded-header-position="relative"]').css("position","fixed");$('.pcoded[theme-layout="horizontal"][pcoded-device-type="desktop"] .pcoded-navbar[pcoded-navbar-position="fixed"][pcoded-header-position="relative"]').css("top",0)}else{$('.pcoded[theme-layout="horizontal"][pcoded-device-type="desktop"] .pcoded-navbar[pcoded-navbar-position="fixed"][pcoded-header-position="relative"]').css("position","absolute");$('.pcoded[theme-layout="horizontal"][pcoded-device-type="desktop"] .pcoded-navbar[pcoded-navbar-position="fixed"][pcoded-header-position="relative"]').css("top","auto")}});$(window).on("load",function(){$('.pcoded[vertical-nav-type="collapsed"] .pcoded-navbar').hover(function(){$(".pcoded").attr("vertical-nav-type","expanded")},function(){$(".pcoded").attr("vertical-nav-type","collapsed")})});